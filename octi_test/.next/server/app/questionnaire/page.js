/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/questionnaire/page";
exports.ids = ["app/questionnaire/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquestionnaire%2Fpage&page=%2Fquestionnaire%2Fpage&appPaths=%2Fquestionnaire%2Fpage&pagePath=private-next-app-dir%2Fquestionnaire%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquestionnaire%2Fpage&page=%2Fquestionnaire%2Fpage&appPaths=%2Fquestionnaire%2Fpage&pagePath=private-next-app-dir%2Fquestionnaire%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'questionnaire',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/questionnaire/page.tsx */ \"(rsc)/./src/app/questionnaire/page.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/questionnaire/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/questionnaire/page\",\n        pathname: \"/questionnaire\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquestionnaire%2Fpage&page=%2Fquestionnaire%2Fpage&appPaths=%2Fquestionnaire%2Fpage&pagePath=private-next-app-dir%2Fquestionnaire%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fquestionnaire%2Fpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fquestionnaire%2Fpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/questionnaire/page.tsx */ \"(ssr)/./src/app/questionnaire/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhcHBsZSUyRkRvY3VtZW50cyUyRjIuMSUyMEFJJTIwSm91cm5leSUyRkN1cnNvcl9wcm9qZWN0cyUyRm9jdGlfdGVzdCUyRnNyYyUyRmFwcCUyRnF1ZXN0aW9ubmFpcmUlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWFzc2Vzc21lbnQtc3lzdGVtLz81M2QwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FwcGxlL0RvY3VtZW50cy8yLjEgQUkgSm91cm5leS9DdXJzb3JfcHJvamVjdHMvb2N0aV90ZXN0L3NyYy9hcHAvcXVlc3Rpb25uYWlyZS9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fquestionnaire%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/questionnaire/page.tsx":
/*!****************************************!*\
  !*** ./src/app/questionnaire/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionnairePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_questionnaire_QuestionnaireLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/questionnaire/QuestionnaireLoader */ \"(ssr)/./src/components/questionnaire/QuestionnaireLoader.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction QuestionnairePage() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"selection\",\n        selectedVersion: null,\n        questionnaireConfig: null,\n        isLoading: false,\n        error: null\n    });\n    // 版本选择处理 - 简化为直接切换到问卷模式，让QuestionnaireLoader处理流式加载\n    const handleVersionSelect = async (version)=>{\n        setState((prev)=>({\n                ...prev,\n                selectedVersion: version,\n                mode: \"questionnaire\",\n                error: null,\n                isLoading: false\n            }));\n    };\n    // 问卷完成处理\n    const handleQuestionnaireComplete = async (responses)=>{\n        console.log(\"问卷完成:\", responses);\n        setState((prev)=>({\n                ...prev,\n                mode: \"completed\"\n            }));\n    };\n    // 重新开始\n    const handleRestart = ()=>{\n        setState({\n            mode: \"selection\",\n            selectedVersion: null,\n            questionnaireConfig: null,\n            isLoading: false,\n            error: null\n        });\n    };\n    // 渲染版本选择界面\n    const renderVersionSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: \"OCTI 组织文化评估问卷\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-8\",\n                            children: \"选择适合您组织的评估版本，开始深入了解您的组织文化特征\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: \"标准版\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\",\n                                                    children: \"推荐\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"适合大多数组织的基础评估，快速了解组织文化现状\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"约 40 道题目\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"预计 15-20 分钟\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"适合中小型团队\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"基础分析报告\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full mt-6\",\n                                            onClick: ()=>handleVersionSelect(\"standard\"),\n                                            disabled: state.isLoading,\n                                            children: state.isLoading && state.selectedVersion === \"standard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: \"生成中...\"\n                                            }, void 0, false) : \"选择标准版\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-purple-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: \"专业版\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 border border-gray-300 text-gray-700 text-xs rounded-full\",\n                                                    children: \"深度分析\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"深度评估组织文化，提供详细的分析和改进建议\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"约 60 道题目\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"预计 25-35 分钟\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"适合大型组织\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"详细分析报告\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full mt-6\",\n                                            variant: \"outline\",\n                                            onClick: ()=>handleVersionSelect(\"professional\"),\n                                            disabled: state.isLoading,\n                                            children: state.isLoading && state.selectedVersion === \"professional\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: \"生成中...\"\n                                            }, void 0, false) : \"选择专业版\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 7\n                }, this),\n                state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: state.error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"mt-2\",\n                            onClick: ()=>setState((prev)=>({\n                                        ...prev,\n                                        error: null\n                                    })),\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n            lineNumber: 57,\n            columnNumber: 5\n        }, this);\n    // 渲染问卷界面\n    const renderQuestionnaire = ()=>{\n        if (!state.questionnaireConfig) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_questionnaire_QuestionnaireLoader__WEBPACK_IMPORTED_MODULE_2__.QuestionnaireLoader, {\n                version: state.selectedVersion || \"standard\",\n                organizationId: \"demo-org\",\n                onComplete: handleQuestionnaireComplete\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染完成界面\n    const renderCompleted = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 max-w-2xl text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-green-500\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M5 13l4 4L19 7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"问卷完成！\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"感谢您完成 OCTI 组织文化评估问卷。我们正在分析您的回答，稍后将为您生成详细的评估报告。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleRestart,\n                                variant: \"outline\",\n                                className: \"w-full\",\n                                children: \"重新开始评估\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"w-full\",\n                                children: \"查看评估报告\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n            lineNumber: 191,\n            columnNumber: 5\n        }, this);\n    // 主渲染逻辑\n    switch(state.mode){\n        case \"selection\":\n            return renderVersionSelection();\n        case \"questionnaire\":\n            return renderQuestionnaire();\n        case \"completed\":\n            return renderCompleted();\n        default:\n            return renderVersionSelection();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/questionnaire/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/ProgressBar.tsx":
/*!******************************************************!*\
  !*** ./src/components/questionnaire/ProgressBar.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgressBar: () => (/* binding */ ProgressBar),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ProgressBar,default auto */ \n\nconst ProgressBar = ({ progress, className = \"\", showPercentage = true })=>{\n    const clampedProgress = Math.min(Math.max(progress, 0), 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `w-full ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"问卷进度\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    showPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            Math.round(clampedProgress),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2.5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out\",\n                    style: {\n                        width: `${clampedProgress}%`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProgressBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/ProgressBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionCounter.tsx":
/*!**********************************************************!*\
  !*** ./src/components/questionnaire/QuestionCounter.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionCounter: () => (/* binding */ QuestionCounter),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QuestionCounter,default auto */ \n\nconst dimensionNames = {\n    SF: \"S/F维度：战略聚焦度\",\n    IT: \"I/T维度：团队协同度\",\n    MV: \"M/V维度：价值导向度\",\n    AD: \"A/D维度：能力发展度\"\n};\nconst dimensionColors = {\n    SF: \"bg-blue-100 text-blue-800 border-blue-200\",\n    IT: \"bg-green-100 text-green-800 border-green-200\",\n    MV: \"bg-purple-100 text-purple-800 border-purple-200\",\n    AD: \"bg-orange-100 text-orange-800 border-orange-200\"\n};\nconst QuestionCounter = ({ current, total, dimension, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center justify-between ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `px-3 py-1 rounded-full text-sm font-medium border ${dimensionColors[dimension]}`,\n                        children: dimensionNames[dimension]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"第 \",\n                            current,\n                            \" 题，共 \",\n                            total,\n                            \" 题\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: [\n                            current,\n                            \"/\",\n                            total\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 bg-gray-200 rounded-full h-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-1.5 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${current / total * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionCounter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionCounter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionDisplay.tsx":
/*!**********************************************************!*\
  !*** ./src/components/questionnaire/QuestionDisplay.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionDisplay: () => (/* binding */ QuestionDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QuestionDisplay auto */ \n\n/**\n * 问题显示组件\n * 根据问题类型渲染不同的输入控件\n */ const QuestionDisplay = ({ question, value, onChange, error })=>{\n    // 渲染单选题\n    const renderSingleChoice = ()=>{\n        if (!question.options || question.options.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: \"错误：单选题缺少选项数据\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: question.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"radio\",\n                            name: question.id,\n                            value: option.value || option.text,\n                            checked: value === (option.value || option.text),\n                            onChange: (e)=>onChange(e.target.value),\n                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-900 flex-1\",\n                            children: option.text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, undefined),\n                        option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"(\",\n                                option.score,\n                                \"分)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, `${question.id}-option-${index}`, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染多选题\n    const renderMultipleChoice = ()=>{\n        if (!question.options || question.options.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: \"错误：多选题缺少选项数据\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined);\n        }\n        const selectedValues = Array.isArray(value) ? value : [];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: question.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"checkbox\",\n                            value: option.value || option.text,\n                            checked: selectedValues.includes(option.value || option.text),\n                            onChange: (e)=>{\n                                const optionValue = option.value || option.text;\n                                let newValues = [\n                                    ...selectedValues\n                                ];\n                                if (e.target.checked) {\n                                    if (!newValues.includes(optionValue)) {\n                                        newValues.push(optionValue);\n                                    }\n                                } else {\n                                    newValues = newValues.filter((v)=>v !== optionValue);\n                                }\n                                onChange(newValues);\n                            },\n                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-900 flex-1\",\n                            children: option.text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, undefined),\n                        option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"(\",\n                                option.score,\n                                \"分)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, `${question.id}-option-${index}`, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染李克特量表\n    const renderLikertScale = ()=>{\n        const scaleOptions = question.options || [\n            {\n                text: \"非常不同意\",\n                value: \"1\",\n                score: 1\n            },\n            {\n                text: \"不同意\",\n                value: \"2\",\n                score: 2\n            },\n            {\n                text: \"中立\",\n                value: \"3\",\n                score: 3\n            },\n            {\n                text: \"同意\",\n                value: \"4\",\n                score: 4\n            },\n            {\n                text: \"非常同意\",\n                value: \"5\",\n                score: 5\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-5 gap-2\",\n                children: scaleOptions.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex flex-col items-center space-y-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                name: question.id,\n                                value: option.value,\n                                checked: value === option.value,\n                                onChange: (e)=>onChange(e.target.value),\n                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-center text-gray-900\",\n                                children: option.text\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, undefined),\n                            option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: option.score\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, `${question.id}-scale-${index}`, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染文本输入\n    const renderTextInput = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n            value: value || \"\",\n            onChange: (e)=>onChange(e.target.value),\n            placeholder: \"请输入您的答案...\",\n            rows: 4,\n            className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 根据问题类型渲染对应的输入控件\n    const renderQuestionInput = ()=>{\n        switch(question.type){\n            case \"single_choice\":\n            case \"single-choice\":\n                return renderSingleChoice();\n            case \"multiple_choice\":\n            case \"multiple-choice\":\n                return renderMultipleChoice();\n            case \"likert_scale\":\n            case \"likert-scale\":\n                return renderLikertScale();\n            case \"text\":\n            case \"textarea\":\n                return renderTextInput();\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"不支持的问题类型:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" \",\n                                question.type\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-yellow-600 mt-2\",\n                            children: \"请联系开发人员添加对此问题类型的支持\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 text-xs text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"问题数据:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"mt-1 bg-white p-2 rounded border text-xs overflow-auto\",\n                                    children: JSON.stringify(question, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: question.text || question.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined),\n                    question.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: question.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, undefined),\n                    question.dimension && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                children: question.dimension\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined),\n                            question.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: \"*\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: renderQuestionInput()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionnaireLoader.tsx":
/*!**************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireLoader.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireLoader: () => (/* binding */ QuestionnaireLoader),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _QuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuestionnaireRenderer */ \"(ssr)/./src/components/questionnaire/QuestionnaireRenderer.tsx\");\n/* harmony import */ var _StreamQuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StreamQuestionnaireRenderer */ \"(ssr)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireLoader,default auto */ \n\n\n\n\n\n/**\n * 问卷加载器组件\n * 负责从API加载问卷配置并渲染问卷界面\n */ const QuestionnaireLoader = ({ version, organizationId, onComplete, onConfigLoaded })=>{\n    // 统一状态管理\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        config: null,\n        loading: true,\n        error: null,\n        retryCount: 0,\n        // 流式加载状态初始化\n        streamLoading: false,\n        loadedQuestions: [],\n        totalExpected: 60,\n        currentProgress: {\n            completed: 0,\n            total: 60,\n            percentage: 0\n        },\n        questionnaireId: null\n    });\n    /**\n   * 转换API数据为QuestionnaireConfig格式\n   */ const transformApiData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((apiData)=>{\n        const questionnaireConfig = {\n            version: apiData.version || version,\n            total_questions: apiData.questions?.length || 0,\n            dimensions: {\n                SF: {\n                    questions: []\n                },\n                IT: {\n                    questions: []\n                },\n                MV: {\n                    questions: []\n                },\n                AD: {\n                    questions: []\n                }\n            }\n        };\n        // 按维度分组问题\n        if (apiData.questions && Array.isArray(apiData.questions)) {\n            apiData.questions.forEach((q)=>{\n                // 处理维度名称映射\n                let dimensionKey;\n                switch(q.dimension){\n                    case \"S/F\":\n                        dimensionKey = \"SF\";\n                        break;\n                    case \"I/T\":\n                        dimensionKey = \"IT\";\n                        break;\n                    case \"M/V\":\n                        dimensionKey = \"MV\";\n                        break;\n                    case \"A/D\":\n                        dimensionKey = \"AD\";\n                        break;\n                    default:\n                        console.warn(`未知维度: ${q.dimension}，默认使用SF`);\n                        dimensionKey = \"SF\";\n                }\n                const configQuestion = {\n                    id: q.id,\n                    dimension: dimensionKey,\n                    sub_dimension: q.subdimension || \"\",\n                    type: q.type,\n                    text: q.title || q.text || \"\",\n                    options: q.options?.map((opt, index)=>({\n                            id: opt.id || `${q.id}_opt_${index}`,\n                            text: opt.text || opt,\n                            value: opt.value || opt,\n                            score: opt.score || index + 1\n                        })) || [],\n                    scoring: {\n                        dimension_weight: q.weight || 1,\n                        sub_dimension_weight: 1,\n                        option_scores: q.options?.map((_, index)=>index + 1) || [],\n                        reverse_scoring: false\n                    }\n                };\n                questionnaireConfig.dimensions[dimensionKey].questions.push(configQuestion);\n            });\n        }\n        return questionnaireConfig;\n    }, [\n        version\n    ]);\n    /**\n   * 流式加载问卷配置 - 新的核心方法\n   */ const loadQuestionnaireStreamly = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (state.retryCount >= 3) {\n            setState((prev)=>({\n                    ...prev,\n                    error: \"重试次数过多，请稍后再试\",\n                    loading: false,\n                    streamLoading: false\n                }));\n            return;\n        }\n        setState((prev)=>({\n                ...prev,\n                loading: true,\n                streamLoading: true,\n                error: null,\n                loadedQuestions: [],\n                currentProgress: {\n                    completed: 0,\n                    total: 60,\n                    percentage: 0\n                }\n            }));\n        try {\n            console.log(\"开始流式加载问卷配置:\", {\n                version,\n                organizationId\n            });\n            // 第一步：启动问卷生成，获取第一批题目\n            const firstBatchResponse = await fetch(\"/api/questionnaire/batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    organizationType: \"technology\",\n                    version,\n                    batchSize: 5\n                })\n            });\n            if (!firstBatchResponse.ok) {\n                throw new Error(`启动问卷生成失败: ${firstBatchResponse.status}`);\n            }\n            const firstBatch = await firstBatchResponse.json();\n            if (!firstBatch.success) {\n                throw new Error(firstBatch.error || \"启动问卷生成失败\");\n            }\n            console.log(\"收到第一批题目:\", firstBatch.data);\n            // 更新状态：显示第一批题目\n            setState((prev)=>({\n                    ...prev,\n                    loadedQuestions: firstBatch.data.questions,\n                    totalExpected: firstBatch.data.totalExpected,\n                    currentProgress: firstBatch.data.progress,\n                    questionnaireId: firstBatch.data.questionnaireId,\n                    loading: false // 第一批加载完成，可以开始显示\n                }));\n            // 第二步：继续加载剩余题目\n            await loadRemainingQuestions(firstBatch.data);\n        } catch (err) {\n            console.error(\"流式加载问卷配置失败:\", err);\n            setState((prev)=>({\n                    ...prev,\n                    error: err instanceof Error ? err.message : \"加载问卷配置失败\",\n                    loading: false,\n                    streamLoading: false,\n                    retryCount: prev.retryCount + 1\n                }));\n        }\n    }, [\n        version,\n        organizationId,\n        state.retryCount\n    ]);\n    /**\n   * 加载剩余题目\n   */ const loadRemainingQuestions = async (initialBatch)=>{\n        let nextBatch = initialBatch.nextBatch;\n        let shouldContinueLoading = true // 使用局部变量控制循环\n        ;\n        let batchCount = 0;\n        const maxBatches = 20 // 防止无限循环的保护机制\n        ;\n        console.log(\"开始加载剩余题目，初始nextBatch:\", nextBatch);\n        while(nextBatch && shouldContinueLoading && batchCount < maxBatches){\n            try {\n                batchCount++;\n                console.log(`[批次 ${batchCount}] 加载下一批题目: ${nextBatch.dimension} 从 ${nextBatch.startIndex}`);\n                const response = await fetch(`/api/questionnaire/batch?questionnaireId=${initialBatch.questionnaireId}&dimension=${nextBatch.dimension}&startIndex=${nextBatch.startIndex}&batchSize=5`);\n                if (!response.ok) {\n                    console.warn(`[批次 ${batchCount}] 批次加载失败: ${response.status}，停止加载`);\n                    shouldContinueLoading = false;\n                    break;\n                }\n                const batchData = await response.json();\n                if (!batchData.success) {\n                    console.warn(`[批次 ${batchCount}] 批次数据无效，停止加载:`, batchData.error);\n                    shouldContinueLoading = false;\n                    break;\n                }\n                console.log(`[批次 ${batchCount}] 收到批次数据:`, {\n                    dimension: batchData.data.dimension,\n                    questionCount: batchData.data.questions?.length || 0,\n                    progress: batchData.data.progress,\n                    hasNextBatch: !!batchData.data.nextBatch\n                });\n                // 累积添加新题目\n                setState((prev)=>({\n                        ...prev,\n                        loadedQuestions: [\n                            ...prev.loadedQuestions,\n                            ...batchData.data.questions\n                        ],\n                        currentProgress: batchData.data.progress\n                    }));\n                // 准备下一批\n                nextBatch = batchData.data.nextBatch;\n                // 如果没有下一批，说明全部完成\n                if (!nextBatch) {\n                    console.log(`[批次 ${batchCount}] 所有题目加载完成!`);\n                    shouldContinueLoading = false;\n                    break;\n                }\n                // 短暂延迟，避免请求过于频繁\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            } catch (error) {\n                console.error(`[批次 ${batchCount}] 加载批次失败:`, error);\n                shouldContinueLoading = false;\n                break;\n            }\n        }\n        // 确保在所有退出路径都更新streamLoading状态\n        setState((prev)=>({\n                ...prev,\n                streamLoading: false\n            }));\n        if (batchCount >= maxBatches) {\n            console.warn(\"达到最大批次限制，停止加载\");\n        }\n        console.log(`题目加载流程结束，共处理 ${batchCount} 个批次`);\n    };\n    /**\n   * 传统的完整加载方法（保留作为备用）\n   * 加载问卷配置\n   */ const loadQuestionnaireConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    loading: true,\n                    error: null\n                }));\n            console.log(\"开始加载问卷配置:\", {\n                version,\n                organizationId\n            });\n            // 创建AbortController用于超时控制\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 15 * 60 * 1000) // 15分钟超时\n            ;\n            try {\n                const response = await fetch(\"/api/questionnaire/generate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        version,\n                        organizationType: \"technology\",\n                        organizationId\n                    }),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    throw new Error(`生成问卷失败: ${response.status} ${response.statusText}`);\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error?.message || \"生成问卷失败\");\n                }\n                console.log(\"API返回数据:\", result.data);\n                // 转换API数据\n                const questionnaireConfig = transformApiData(result.data);\n                setState((prev)=>({\n                        ...prev,\n                        config: questionnaireConfig,\n                        loading: false,\n                        retryCount: 0\n                    }));\n                // 通知父组件配置已加载\n                if (onConfigLoaded) {\n                    onConfigLoaded(questionnaireConfig);\n                }\n            } catch (error) {\n                clearTimeout(timeoutId);\n                if (error instanceof Error && error.name === \"AbortError\") {\n                    throw new Error(\"请求超时，问卷生成时间过长，请稍后重试\");\n                }\n                throw error;\n            }\n        } catch (err) {\n            console.error(\"加载问卷配置失败:\", err);\n            setState((prev)=>({\n                    ...prev,\n                    error: err instanceof Error ? err.message : \"加载问卷配置失败\",\n                    loading: false,\n                    retryCount: prev.retryCount + 1\n                }));\n        }\n    }, [\n        version,\n        organizationId,\n        onConfigLoaded,\n        transformApiData\n    ]);\n    /**\n   * 处理问卷完成\n   */ const handleQuestionnaireComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (responses)=>{\n        console.log(\"问卷完成，回答数量:\", responses.length);\n        try {\n            if (onComplete) {\n                await onComplete(responses);\n            }\n        } catch (error) {\n            console.error(\"处理问卷完成失败:\", error);\n        // 这里可以添加错误处理逻辑\n        }\n    }, [\n        onComplete\n    ]);\n    /**\n   * 重试加载\n   */ const handleRetry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (state.retryCount < 3) {\n            loadQuestionnaireConfig();\n        } else {\n            setState((prev)=>({\n                    ...prev,\n                    error: \"重试次数过多，请刷新页面重试\"\n                }));\n        }\n    }, [\n        loadQuestionnaireConfig,\n        state.retryCount\n    ]);\n    // 初始化加载 - 使用流式加载\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadQuestionnaireStreamly();\n    }, [\n        loadQuestionnaireStreamly\n    ]);\n    // 渲染初始加载状态（只在第一批题目加载前显示）\n    if (state.loading && state.loadedQuestions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 font-medium\",\n                                children: \"正在生成第一批题目...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400 mt-1\",\n                                children: [\n                                    \"版本: \",\n                                    version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, undefined),\n                            organizationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"组织ID: \",\n                                    organizationId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 419,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 418,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (state.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"max-w-md w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-500 text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: \"加载失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4 text-sm\",\n                            children: state.error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleRetry,\n                                    disabled: state.retryCount >= 3,\n                                    className: \"w-full\",\n                                    children: state.retryCount >= 3 ? \"重试次数已用完\" : `重新加载 (${state.retryCount}/3)`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, undefined),\n                                state.retryCount >= 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"w-full\",\n                                    children: \"刷新页面\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-xs text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"版本: \",\n                                        version\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, undefined),\n                                organizationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"组织ID: \",\n                                        organizationId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 34\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 437,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 436,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染流式加载状态 - 显示已加载的题目\n    if (state.loadedQuestions.length > 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: `border-2 ${state.streamLoading ? \"bg-blue-50 border-blue-200\" : \"bg-green-50 border-green-200\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `text-xl mr-3 ${state.streamLoading ? \"text-blue-500\" : \"text-green-500\"}`,\n                                    children: state.streamLoading ? \"\\uD83D\\uDD04\" : \"✅\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: `text-sm font-semibold ${state.streamLoading ? \"text-blue-900\" : \"text-green-900\"}`,\n                                            children: state.streamLoading ? \"问卷生成中...\" : \"问卷生成完成\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-sm ${state.streamLoading ? \"text-blue-700\" : \"text-green-700\"}`,\n                                            children: [\n                                                \"已生成 \",\n                                                state.loadedQuestions.length,\n                                                \" / \",\n                                                state.totalExpected,\n                                                \" 道题目\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        state.streamLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-blue-200 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: `${state.currentProgress.percentage}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: [\n                                                        state.currentProgress.percentage,\n                                                        \"% 完成\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right text-xs text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"版本: \",\n                                                version\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"已加载: \",\n                                                state.loadedQuestions.length,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamQuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_5__.StreamQuestionnaireRenderer, {\n                    questions: state.loadedQuestions,\n                    isLoading: state.streamLoading,\n                    progress: state.currentProgress,\n                    onAnswerChange: (questionId, answer)=>{\n                        console.log(\"答案变更:\", questionId, answer);\n                    },\n                    onComplete: (responses)=>{\n                        console.log(\"问卷完成:\", responses);\n                        if (handleQuestionnaireComplete) {\n                            // 转换为旧格式兼容\n                            const convertedResponses = responses.map((r)=>({\n                                    questionId: r.questionId,\n                                    answer: r.answer,\n                                    timestamp: r.timestamp\n                                }));\n                            handleQuestionnaireComplete(convertedResponses);\n                        }\n                    },\n                    className: \"bg-white\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 476,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 传统的完整加载成功状态（保留兼容性）\n    if (state.config) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_4__.QuestionnaireRenderer, {\n                config: state.config,\n                onComplete: handleQuestionnaireComplete,\n                className: \"bg-white\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 543,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 542,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\n// 默认导出以保持兼容性\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionnaireLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9xdWVzdGlvbm5haXJlL1F1ZXN0aW9ubmFpcmVMb2FkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRStEO0FBa0JwQjtBQUNJO0FBQ2dCO0FBQ1k7QUEwQjNFOzs7Q0FHQyxHQUNNLE1BQU1RLHNCQUEwRCxDQUFDLEVBQ3RFQyxPQUFPLEVBQ1BDLGNBQWMsRUFDZEMsVUFBVSxFQUNWQyxjQUFjLEVBQ2Y7SUFDQyxTQUFTO0lBQ1QsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdiLCtDQUFRQSxDQUFjO1FBQzlDYyxRQUFRO1FBQ1JDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxZQUFZO1FBQ1osWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLGlCQUFpQixFQUFFO1FBQ25CQyxlQUFlO1FBQ2ZDLGlCQUFpQjtZQUNmQyxXQUFXO1lBQ1hDLE9BQU87WUFDUEMsWUFBWTtRQUNkO1FBQ0FDLGlCQUFpQjtJQUNuQjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsbUJBQW1CeEIsa0RBQVdBLENBQUMsQ0FBQ3lCO1FBQ3BDLE1BQU1DLHNCQUEyQztZQUMvQ3BCLFNBQVNtQixRQUFRbkIsT0FBTyxJQUFJQTtZQUM1QnFCLGlCQUFpQkYsUUFBUUcsU0FBUyxFQUFFQyxVQUFVO1lBQzlDQyxZQUFZO2dCQUNWQyxJQUFJO29CQUFFSCxXQUFXLEVBQUU7Z0JBQUM7Z0JBQ3BCSSxJQUFJO29CQUFFSixXQUFXLEVBQUU7Z0JBQUM7Z0JBQ3BCSyxJQUFJO29CQUFFTCxXQUFXLEVBQUU7Z0JBQUM7Z0JBQ3BCTSxJQUFJO29CQUFFTixXQUFXLEVBQUU7Z0JBQUM7WUFDdEI7UUFDRjtRQUVBLFVBQVU7UUFDVixJQUFJSCxRQUFRRyxTQUFTLElBQUlPLE1BQU1DLE9BQU8sQ0FBQ1gsUUFBUUcsU0FBUyxHQUFHO1lBQ3pESCxRQUFRRyxTQUFTLENBQUNTLE9BQU8sQ0FBQyxDQUFDQztnQkFDekIsV0FBVztnQkFDWCxJQUFJQztnQkFDSixPQUFRRCxFQUFFRSxTQUFTO29CQUNqQixLQUFLO3dCQUNIRCxlQUFlO3dCQUNmO29CQUNGLEtBQUs7d0JBQ0hBLGVBQWU7d0JBQ2Y7b0JBQ0YsS0FBSzt3QkFDSEEsZUFBZTt3QkFDZjtvQkFDRixLQUFLO3dCQUNIQSxlQUFlO3dCQUNmO29CQUNGO3dCQUNFRSxRQUFRQyxJQUFJLENBQUMsQ0FBQyxNQUFNLEVBQUVKLEVBQUVFLFNBQVMsQ0FBQyxPQUFPLENBQUM7d0JBQzFDRCxlQUFlO2dCQUNuQjtnQkFFQSxNQUFNSSxpQkFBMkI7b0JBQy9CQyxJQUFJTixFQUFFTSxFQUFFO29CQUNSSixXQUFXRDtvQkFDWE0sZUFBZVAsRUFBRVEsWUFBWSxJQUFJO29CQUNqQ0MsTUFBTVQsRUFBRVMsSUFBSTtvQkFDWkMsTUFBTVYsRUFBRVcsS0FBSyxJQUFJWCxFQUFFVSxJQUFJLElBQUk7b0JBQzNCRSxTQUFTWixFQUFFWSxPQUFPLEVBQUVDLElBQUksQ0FBQ0MsS0FBVUMsUUFBbUI7NEJBQ3BEVCxJQUFJUSxJQUFJUixFQUFFLElBQUksQ0FBQyxFQUFFTixFQUFFTSxFQUFFLENBQUMsS0FBSyxFQUFFUyxNQUFNLENBQUM7NEJBQ3BDTCxNQUFNSSxJQUFJSixJQUFJLElBQUlJOzRCQUNsQkUsT0FBT0YsSUFBSUUsS0FBSyxJQUFJRjs0QkFDcEJHLE9BQU9ILElBQUlHLEtBQUssSUFBSUYsUUFBUTt3QkFDOUIsT0FBTyxFQUFFO29CQUNURyxTQUFTO3dCQUNQQyxrQkFBa0JuQixFQUFFb0IsTUFBTSxJQUFJO3dCQUM5QkMsc0JBQXNCO3dCQUN0QkMsZUFBZXRCLEVBQUVZLE9BQU8sRUFBRUMsSUFBSSxDQUFDVSxHQUFRUixRQUFrQkEsUUFBUSxNQUFNLEVBQUU7d0JBQ3pFUyxpQkFBaUI7b0JBQ25CO2dCQUNGO2dCQUVBcEMsb0JBQW9CSSxVQUFVLENBQUNTLGFBQWEsQ0FBQ1gsU0FBUyxDQUFDbUMsSUFBSSxDQUFDcEI7WUFDOUQ7UUFDRjtRQUVBLE9BQU9qQjtJQUNULEdBQUc7UUFBQ3BCO0tBQVE7SUFFWjs7R0FFQyxHQUNELE1BQU0wRCw0QkFBNEJoRSxrREFBV0EsQ0FBQztRQUM1QyxJQUFJVSxNQUFNSyxVQUFVLElBQUksR0FBRztZQUN6QkosU0FBU3NELENBQUFBLE9BQVM7b0JBQ2hCLEdBQUdBLElBQUk7b0JBQ1BuRCxPQUFPO29CQUNQRCxTQUFTO29CQUNURyxlQUFlO2dCQUNqQjtZQUNBO1FBQ0Y7UUFFQUwsU0FBU3NELENBQUFBLE9BQVM7Z0JBQ2hCLEdBQUdBLElBQUk7Z0JBQ1BwRCxTQUFTO2dCQUNURyxlQUFlO2dCQUNmRixPQUFPO2dCQUNQRyxpQkFBaUIsRUFBRTtnQkFDbkJFLGlCQUFpQjtvQkFBRUMsV0FBVztvQkFBR0MsT0FBTztvQkFBSUMsWUFBWTtnQkFBRTtZQUM1RDtRQUVBLElBQUk7WUFDRm1CLFFBQVF5QixHQUFHLENBQUMsZUFBZTtnQkFBRTVEO2dCQUFTQztZQUFlO1lBRXJELHFCQUFxQjtZQUNyQixNQUFNNEQscUJBQXFCLE1BQU1DLE1BQU0sNEJBQTRCO2dCQUNqRUMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxrQkFBa0I7b0JBQ2xCcEU7b0JBQ0FxRSxXQUFXO2dCQUNiO1lBQ0Y7WUFFQSxJQUFJLENBQUNSLG1CQUFtQlMsRUFBRSxFQUFFO2dCQUMxQixNQUFNLElBQUlDLE1BQU0sQ0FBQyxVQUFVLEVBQUVWLG1CQUFtQlcsTUFBTSxDQUFDLENBQUM7WUFDMUQ7WUFFQSxNQUFNQyxhQUFhLE1BQU1aLG1CQUFtQmEsSUFBSTtZQUVoRCxJQUFJLENBQUNELFdBQVdFLE9BQU8sRUFBRTtnQkFDdkIsTUFBTSxJQUFJSixNQUFNRSxXQUFXakUsS0FBSyxJQUFJO1lBQ3RDO1lBRUEyQixRQUFReUIsR0FBRyxDQUFDLFlBQVlhLFdBQVdHLElBQUk7WUFFdkMsZUFBZTtZQUNmdkUsU0FBU3NELENBQUFBLE9BQVM7b0JBQ2hCLEdBQUdBLElBQUk7b0JBQ1BoRCxpQkFBaUI4RCxXQUFXRyxJQUFJLENBQUN0RCxTQUFTO29CQUMxQ1YsZUFBZTZELFdBQVdHLElBQUksQ0FBQ2hFLGFBQWE7b0JBQzVDQyxpQkFBaUI0RCxXQUFXRyxJQUFJLENBQUNDLFFBQVE7b0JBQ3pDNUQsaUJBQWlCd0QsV0FBV0csSUFBSSxDQUFDM0QsZUFBZTtvQkFDaERWLFNBQVMsTUFBTSxpQkFBaUI7Z0JBQ2xDO1lBRUEsZUFBZTtZQUNmLE1BQU11RSx1QkFBdUJMLFdBQVdHLElBQUk7UUFFOUMsRUFBRSxPQUFPRyxLQUFLO1lBQ1o1QyxRQUFRM0IsS0FBSyxDQUFDLGVBQWV1RTtZQUM3QjFFLFNBQVNzRCxDQUFBQSxPQUFTO29CQUNoQixHQUFHQSxJQUFJO29CQUNQbkQsT0FBT3VFLGVBQWVSLFFBQVFRLElBQUlDLE9BQU8sR0FBRztvQkFDNUN6RSxTQUFTO29CQUNURyxlQUFlO29CQUNmRCxZQUFZa0QsS0FBS2xELFVBQVUsR0FBRztnQkFDaEM7UUFDRjtJQUNGLEdBQUc7UUFBQ1Q7UUFBU0M7UUFBZ0JHLE1BQU1LLFVBQVU7S0FBQztJQUU5Qzs7R0FFQyxHQUNELE1BQU1xRSx5QkFBeUIsT0FBT0c7UUFDcEMsSUFBSUMsWUFBWUQsYUFBYUMsU0FBUztRQUN0QyxJQUFJQyx3QkFBd0IsS0FBSyxhQUFhOztRQUM5QyxJQUFJQyxhQUFhO1FBQ2pCLE1BQU1DLGFBQWEsR0FBRyxjQUFjOztRQUVwQ2xELFFBQVF5QixHQUFHLENBQUMseUJBQXlCc0I7UUFFckMsTUFBT0EsYUFBYUMseUJBQXlCQyxhQUFhQyxXQUFZO1lBQ3BFLElBQUk7Z0JBQ0ZEO2dCQUNBakQsUUFBUXlCLEdBQUcsQ0FBQyxDQUFDLElBQUksRUFBRXdCLFdBQVcsV0FBVyxFQUFFRixVQUFVaEQsU0FBUyxDQUFDLEdBQUcsRUFBRWdELFVBQVVJLFVBQVUsQ0FBQyxDQUFDO2dCQUUxRixNQUFNQyxXQUFXLE1BQU16QixNQUNyQixDQUFDLHlDQUF5QyxFQUFFbUIsYUFBYWhFLGVBQWUsQ0FBQyxXQUFXLEVBQUVpRSxVQUFVaEQsU0FBUyxDQUFDLFlBQVksRUFBRWdELFVBQVVJLFVBQVUsQ0FBQyxZQUFZLENBQUM7Z0JBRzVKLElBQUksQ0FBQ0MsU0FBU2pCLEVBQUUsRUFBRTtvQkFDaEJuQyxRQUFRQyxJQUFJLENBQUMsQ0FBQyxJQUFJLEVBQUVnRCxXQUFXLFVBQVUsRUFBRUcsU0FBU2YsTUFBTSxDQUFDLEtBQUssQ0FBQztvQkFDakVXLHdCQUF3QjtvQkFDeEI7Z0JBQ0Y7Z0JBRUEsTUFBTUssWUFBWSxNQUFNRCxTQUFTYixJQUFJO2dCQUVyQyxJQUFJLENBQUNjLFVBQVViLE9BQU8sRUFBRTtvQkFDdEJ4QyxRQUFRQyxJQUFJLENBQUMsQ0FBQyxJQUFJLEVBQUVnRCxXQUFXLGNBQWMsQ0FBQyxFQUFFSSxVQUFVaEYsS0FBSztvQkFDL0QyRSx3QkFBd0I7b0JBQ3hCO2dCQUNGO2dCQUVBaEQsUUFBUXlCLEdBQUcsQ0FBQyxDQUFDLElBQUksRUFBRXdCLFdBQVcsU0FBUyxDQUFDLEVBQUU7b0JBQ3hDbEQsV0FBV3NELFVBQVVaLElBQUksQ0FBQzFDLFNBQVM7b0JBQ25DdUQsZUFBZUQsVUFBVVosSUFBSSxDQUFDdEQsU0FBUyxFQUFFQyxVQUFVO29CQUNuRHNELFVBQVVXLFVBQVVaLElBQUksQ0FBQ0MsUUFBUTtvQkFDakNhLGNBQWMsQ0FBQyxDQUFDRixVQUFVWixJQUFJLENBQUNNLFNBQVM7Z0JBQzFDO2dCQUVBLFVBQVU7Z0JBQ1Y3RSxTQUFTc0QsQ0FBQUEsT0FBUzt3QkFDaEIsR0FBR0EsSUFBSTt3QkFDUGhELGlCQUFpQjsrQkFBSWdELEtBQUtoRCxlQUFlOytCQUFLNkUsVUFBVVosSUFBSSxDQUFDdEQsU0FBUzt5QkFBQzt3QkFDdkVULGlCQUFpQjJFLFVBQVVaLElBQUksQ0FBQ0MsUUFBUTtvQkFDMUM7Z0JBRUEsUUFBUTtnQkFDUkssWUFBWU0sVUFBVVosSUFBSSxDQUFDTSxTQUFTO2dCQUVwQyxpQkFBaUI7Z0JBQ2pCLElBQUksQ0FBQ0EsV0FBVztvQkFDZC9DLFFBQVF5QixHQUFHLENBQUMsQ0FBQyxJQUFJLEVBQUV3QixXQUFXLFdBQVcsQ0FBQztvQkFDMUNELHdCQUF3QjtvQkFDeEI7Z0JBQ0Y7Z0JBRUEsZ0JBQWdCO2dCQUNoQixNQUFNLElBQUlRLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7WUFFbkQsRUFBRSxPQUFPcEYsT0FBTztnQkFDZDJCLFFBQVEzQixLQUFLLENBQUMsQ0FBQyxJQUFJLEVBQUU0RSxXQUFXLFNBQVMsQ0FBQyxFQUFFNUU7Z0JBQzVDMkUsd0JBQXdCO2dCQUN4QjtZQUNGO1FBQ0Y7UUFFQSw4QkFBOEI7UUFDOUI5RSxTQUFTc0QsQ0FBQUEsT0FBUztnQkFDaEIsR0FBR0EsSUFBSTtnQkFDUGpELGVBQWU7WUFDakI7UUFFQSxJQUFJMEUsY0FBY0MsWUFBWTtZQUM1QmxELFFBQVFDLElBQUksQ0FBQztRQUNmO1FBRUFELFFBQVF5QixHQUFHLENBQUMsQ0FBQyxhQUFhLEVBQUV3QixXQUFXLElBQUksQ0FBQztJQUM5QztJQUVBOzs7R0FHQyxHQUNELE1BQU1VLDBCQUEwQnBHLGtEQUFXQSxDQUFDO1FBQzFDLElBQUk7WUFDRlcsU0FBU3NELENBQUFBLE9BQVM7b0JBQ2hCLEdBQUdBLElBQUk7b0JBQ1BwRCxTQUFTO29CQUNUQyxPQUFPO2dCQUNUO1lBRUEyQixRQUFReUIsR0FBRyxDQUFDLGFBQWE7Z0JBQUU1RDtnQkFBU0M7WUFBZTtZQUVuRCwwQkFBMEI7WUFDMUIsTUFBTThGLGFBQWEsSUFBSUM7WUFDdkIsTUFBTUMsWUFBWUosV0FBVyxJQUFNRSxXQUFXRyxLQUFLLElBQUksS0FBSyxLQUFLLE1BQU0sU0FBUzs7WUFFaEYsSUFBSTtnQkFDRixNQUFNWCxXQUFXLE1BQU16QixNQUFNLCtCQUErQjtvQkFDMURDLFFBQVE7b0JBQ1JDLFNBQVM7d0JBQ1AsZ0JBQWdCO29CQUNsQjtvQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQm5FO3dCQUNBb0Usa0JBQWtCO3dCQUNsQm5FO29CQUNGO29CQUNBa0csUUFBUUosV0FBV0ksTUFBTTtnQkFDM0I7Z0JBRUFDLGFBQWFIO2dCQUViLElBQUksQ0FBQ1YsU0FBU2pCLEVBQUUsRUFBRTtvQkFDaEIsTUFBTSxJQUFJQyxNQUFNLENBQUMsUUFBUSxFQUFFZ0IsU0FBU2YsTUFBTSxDQUFDLENBQUMsRUFBRWUsU0FBU2MsVUFBVSxDQUFDLENBQUM7Z0JBQ3JFO2dCQUVBLE1BQU1DLFNBQVMsTUFBTWYsU0FBU2IsSUFBSTtnQkFFbEMsSUFBSSxDQUFDNEIsT0FBTzNCLE9BQU8sRUFBRTtvQkFDbkIsTUFBTSxJQUFJSixNQUFNK0IsT0FBTzlGLEtBQUssRUFBRXdFLFdBQVc7Z0JBQzNDO2dCQUVBN0MsUUFBUXlCLEdBQUcsQ0FBQyxZQUFZMEMsT0FBTzFCLElBQUk7Z0JBRW5DLFVBQVU7Z0JBQ1YsTUFBTXhELHNCQUFzQkYsaUJBQWlCb0YsT0FBTzFCLElBQUk7Z0JBRXhEdkUsU0FBU3NELENBQUFBLE9BQVM7d0JBQ2hCLEdBQUdBLElBQUk7d0JBQ1ByRCxRQUFRYzt3QkFDUmIsU0FBUzt3QkFDVEUsWUFBWTtvQkFDZDtnQkFFQSxhQUFhO2dCQUNiLElBQUlOLGdCQUFnQjtvQkFDbEJBLGVBQWVpQjtnQkFDakI7WUFFRixFQUFFLE9BQU9aLE9BQU87Z0JBQ2Q0RixhQUFhSDtnQkFDYixJQUFJekYsaUJBQWlCK0QsU0FBUy9ELE1BQU0rRixJQUFJLEtBQUssY0FBYztvQkFDekQsTUFBTSxJQUFJaEMsTUFBTTtnQkFDbEI7Z0JBQ0EsTUFBTS9EO1lBQ1I7UUFFRixFQUFFLE9BQU91RSxLQUFLO1lBQ1o1QyxRQUFRM0IsS0FBSyxDQUFDLGFBQWF1RTtZQUMzQjFFLFNBQVNzRCxDQUFBQSxPQUFTO29CQUNoQixHQUFHQSxJQUFJO29CQUNQbkQsT0FBT3VFLGVBQWVSLFFBQVFRLElBQUlDLE9BQU8sR0FBRztvQkFDNUN6RSxTQUFTO29CQUNURSxZQUFZa0QsS0FBS2xELFVBQVUsR0FBRztnQkFDaEM7UUFDRjtJQUNGLEdBQUc7UUFBQ1Q7UUFBU0M7UUFBZ0JFO1FBQWdCZTtLQUFpQjtJQUU5RDs7R0FFQyxHQUNELE1BQU1zRiw4QkFBOEI5RyxrREFBV0EsQ0FBQyxPQUFPK0c7UUFDckR0RSxRQUFReUIsR0FBRyxDQUFDLGNBQWM2QyxVQUFVbEYsTUFBTTtRQUUxQyxJQUFJO1lBQ0YsSUFBSXJCLFlBQVk7Z0JBQ2QsTUFBTUEsV0FBV3VHO1lBQ25CO1FBQ0YsRUFBRSxPQUFPakcsT0FBTztZQUNkMkIsUUFBUTNCLEtBQUssQ0FBQyxhQUFhQTtRQUMzQixlQUFlO1FBQ2pCO0lBQ0YsR0FBRztRQUFDTjtLQUFXO0lBRWY7O0dBRUMsR0FDRCxNQUFNd0csY0FBY2hILGtEQUFXQSxDQUFDO1FBQzlCLElBQUlVLE1BQU1LLFVBQVUsR0FBRyxHQUFHO1lBQ3hCcUY7UUFDRixPQUFPO1lBQ0x6RixTQUFTc0QsQ0FBQUEsT0FBUztvQkFDaEIsR0FBR0EsSUFBSTtvQkFDUG5ELE9BQU87Z0JBQ1Q7UUFDRjtJQUNGLEdBQUc7UUFBQ3NGO1FBQXlCMUYsTUFBTUssVUFBVTtLQUFDO0lBRTlDLGlCQUFpQjtJQUNqQmhCLGdEQUFTQSxDQUFDO1FBQ1JpRTtJQUNGLEdBQUc7UUFBQ0E7S0FBMEI7SUFFOUIseUJBQXlCO0lBQ3pCLElBQUl0RCxNQUFNRyxPQUFPLElBQUlILE1BQU1PLGVBQWUsQ0FBQ1ksTUFBTSxLQUFLLEdBQUc7UUFDdkQscUJBQ0UsOERBQUNvRjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEOzswQ0FDQyw4REFBQ0U7Z0NBQUVELFdBQVU7MENBQTRCOzs7Ozs7MENBQ3pDLDhEQUFDQztnQ0FBRUQsV0FBVTs7b0NBQTZCO29DQUFLNUc7Ozs7Ozs7NEJBQzlDQyxnQ0FDQyw4REFBQzRHO2dDQUFFRCxXQUFVOztvQ0FBd0I7b0NBQU8zRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTXhEO0lBRUEsU0FBUztJQUNULElBQUlHLE1BQU1JLEtBQUssRUFBRTtRQUNmLHFCQUNFLDhEQUFDbUc7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ2pILHFEQUFJQTtnQkFBQ2lILFdBQVU7MEJBQ2QsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQTZCOzs7Ozs7c0NBQzVDLDhEQUFDRTs0QkFBR0YsV0FBVTtzQ0FBMkM7Ozs7OztzQ0FDekQsOERBQUNDOzRCQUFFRCxXQUFVO3NDQUE4QnhHLE1BQU1JLEtBQUs7Ozs7OztzQ0FFdEQsOERBQUNtRzs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNoSCx5REFBTUE7b0NBQ0xtSCxTQUFTTDtvQ0FDVE0sVUFBVTVHLE1BQU1LLFVBQVUsSUFBSTtvQ0FDOUJtRyxXQUFVOzhDQUVUeEcsTUFBTUssVUFBVSxJQUFJLElBQUksWUFBWSxDQUFDLE1BQU0sRUFBRUwsTUFBTUssVUFBVSxDQUFDLEdBQUcsQ0FBQzs7Ozs7O2dDQUdwRUwsTUFBTUssVUFBVSxJQUFJLG1CQUNuQiw4REFBQ2IseURBQU1BO29DQUNMcUgsU0FBUTtvQ0FDUkYsU0FBUyxJQUFNRyxPQUFPQyxRQUFRLENBQUNDLE1BQU07b0NBQ3JDUixXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7c0NBTUwsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7O3dDQUFFO3dDQUFLN0c7Ozs7Ozs7Z0NBQ1BDLGdDQUFrQiw4REFBQzRHOzt3Q0FBRTt3Q0FBTzVHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU16QztJQUVBLHNCQUFzQjtJQUN0QixJQUFJRyxNQUFNTyxlQUFlLENBQUNZLE1BQU0sR0FBRyxHQUFHO1FBQ3BDLHFCQUNFLDhEQUFDb0Y7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNqSCxxREFBSUE7b0JBQUNpSCxXQUFXLENBQUMsU0FBUyxFQUFFeEcsTUFBTU0sYUFBYSxHQUFHLCtCQUErQiwrQkFBK0IsQ0FBQzs4QkFDaEgsNEVBQUNpRzt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVyxDQUFDLGFBQWEsRUFBRXhHLE1BQU1NLGFBQWEsR0FBRyxrQkFBa0IsaUJBQWlCLENBQUM7OENBQ3ZGTixNQUFNTSxhQUFhLEdBQUcsaUJBQU87Ozs7Ozs4Q0FFaEMsOERBQUNpRztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNFOzRDQUFHRixXQUFXLENBQUMsc0JBQXNCLEVBQUV4RyxNQUFNTSxhQUFhLEdBQUcsa0JBQWtCLGlCQUFpQixDQUFDO3NEQUMvRk4sTUFBTU0sYUFBYSxHQUFHLGFBQWE7Ozs7OztzREFFdEMsOERBQUNtRzs0Q0FBRUQsV0FBVyxDQUFDLFFBQVEsRUFBRXhHLE1BQU1NLGFBQWEsR0FBRyxrQkFBa0IsaUJBQWlCLENBQUM7O2dEQUFFO2dEQUM5RU4sTUFBTU8sZUFBZSxDQUFDWSxNQUFNO2dEQUFDO2dEQUFJbkIsTUFBTVEsYUFBYTtnREFBQzs7Ozs7Ozt3Q0FFM0RSLE1BQU1NLGFBQWEsa0JBQ2xCLDhEQUFDaUc7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQ0NDLFdBQVU7d0RBQ1ZTLE9BQU87NERBQUVDLE9BQU8sQ0FBQyxFQUFFbEgsTUFBTVMsZUFBZSxDQUFDRyxVQUFVLENBQUMsQ0FBQyxDQUFDO3dEQUFDOzs7Ozs7Ozs7Ozs4REFHM0QsOERBQUM2RjtvREFBRUQsV0FBVTs7d0RBQ1Z4RyxNQUFNUyxlQUFlLENBQUNHLFVBQVU7d0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzFDLDhEQUFDMkY7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7Z0RBQUk7Z0RBQUszRzs7Ozs7OztzREFDViw4REFBQzJHOztnREFBSTtnREFBTXZHLE1BQU1PLGVBQWUsQ0FBQ1ksTUFBTTtnREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT2hELDhEQUFDekIscUZBQTJCQTtvQkFDMUJ3QixXQUFXbEIsTUFBTU8sZUFBZTtvQkFDaEM0RyxXQUFXbkgsTUFBTU0sYUFBYTtvQkFDOUJtRSxVQUFVekUsTUFBTVMsZUFBZTtvQkFDL0IyRyxnQkFBZ0IsQ0FBQ0MsWUFBWUM7d0JBQzNCdkYsUUFBUXlCLEdBQUcsQ0FBQyxTQUFTNkQsWUFBWUM7b0JBQ25DO29CQUNBeEgsWUFBWSxDQUFDdUc7d0JBQ1h0RSxRQUFReUIsR0FBRyxDQUFDLFNBQVM2Qzt3QkFDckIsSUFBSUQsNkJBQTZCOzRCQUMvQixXQUFXOzRCQUNYLE1BQU1tQixxQkFBcUJsQixVQUFVNUQsR0FBRyxDQUFDK0UsQ0FBQUEsSUFBTTtvQ0FDN0NILFlBQVlHLEVBQUVILFVBQVU7b0NBQ3hCQyxRQUFRRSxFQUFFRixNQUFNO29DQUNoQkcsV0FBV0QsRUFBRUMsU0FBUztnQ0FDeEI7NEJBQ0FyQiw0QkFBNEJtQjt3QkFDOUI7b0JBQ0Y7b0JBQ0FmLFdBQVU7Ozs7Ozs7Ozs7OztJQUlsQjtJQUVBLHFCQUFxQjtJQUNyQixJQUFJeEcsTUFBTUUsTUFBTSxFQUFFO1FBQ2hCLHFCQUNFLDhEQUFDcUc7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQy9HLHlFQUFxQkE7Z0JBQ3BCUyxRQUFRRixNQUFNRSxNQUFNO2dCQUNwQkosWUFBWXNHO2dCQUNaSSxXQUFVOzs7Ozs7Ozs7OztJQUlsQjtJQUVBLE9BQU87QUFDVCxFQUFDO0FBRUQsYUFBYTtBQUNiLGlFQUFlN0csbUJBQW1CQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9jb21wb25lbnRzL3F1ZXN0aW9ubmFpcmUvUXVlc3Rpb25uYWlyZUxvYWRlci50c3g/NGU1YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBRdWVzdGlvbm5haXJlQ29uZmlnLCBRdWVzdGlvbm5haXJlUmVzcG9uc2UsIFF1ZXN0aW9uIH0gZnJvbSAnQC90eXBlcydcblxuLy8g5rWB5byP6aKY55uu57G75Z6L5a6a5LmJXG5pbnRlcmZhY2UgU3RyZWFtUXVlc3Rpb24ge1xuICBpZDogc3RyaW5nXG4gIGRpbWVuc2lvbjogc3RyaW5nXG4gIHR5cGU6IHN0cmluZ1xuICB0ZXh0OiBzdHJpbmdcbiAgb3B0aW9uczogQXJyYXk8e1xuICAgIGlkOiBzdHJpbmdcbiAgICB0ZXh0OiBzdHJpbmdcbiAgICBzY29yZTogbnVtYmVyXG4gIH0+XG4gIHJlcXVpcmVkOiBib29sZWFuXG4gIG9yZGVyOiBudW1iZXJcbn1cbmltcG9ydCB7IExvYWRpbmcgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvTG9hZGluZydcbmltcG9ydCB7IENhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvQ2FyZCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9CdXR0b24nXG5pbXBvcnQgeyBRdWVzdGlvbm5haXJlUmVuZGVyZXIgfSBmcm9tICcuL1F1ZXN0aW9ubmFpcmVSZW5kZXJlcidcbmltcG9ydCB7IFN0cmVhbVF1ZXN0aW9ubmFpcmVSZW5kZXJlciB9IGZyb20gJy4vU3RyZWFtUXVlc3Rpb25uYWlyZVJlbmRlcmVyJ1xuXG5pbnRlcmZhY2UgUXVlc3Rpb25uYWlyZUxvYWRlclByb3BzIHtcbiAgdmVyc2lvbjogJ3N0YW5kYXJkJyB8ICdwcm9mZXNzaW9uYWwnXG4gIG9yZ2FuaXphdGlvbklkPzogc3RyaW5nXG4gIG9uQ29tcGxldGU/OiAocmVzcG9uc2VzOiBRdWVzdGlvbm5haXJlUmVzcG9uc2VbXSkgPT4gUHJvbWlzZTx2b2lkPlxuICBvbkNvbmZpZ0xvYWRlZD86IChjb25maWc6IFF1ZXN0aW9ubmFpcmVDb25maWcpID0+IHZvaWRcbn1cblxuaW50ZXJmYWNlIExvYWRlclN0YXRlIHtcbiAgY29uZmlnOiBRdWVzdGlvbm5haXJlQ29uZmlnIHwgbnVsbFxuICBsb2FkaW5nOiBib29sZWFuXG4gIGVycm9yOiBzdHJpbmcgfCBudWxsXG4gIHJldHJ5Q291bnQ6IG51bWJlclxuICAvLyDmlrDlop7mtYHlvI/liqDovb3nirbmgIFcbiAgc3RyZWFtTG9hZGluZzogYm9vbGVhblxuICBsb2FkZWRRdWVzdGlvbnM6IFN0cmVhbVF1ZXN0aW9uW11cbiAgdG90YWxFeHBlY3RlZDogbnVtYmVyXG4gIGN1cnJlbnRQcm9ncmVzczoge1xuICAgIGNvbXBsZXRlZDogbnVtYmVyXG4gICAgdG90YWw6IG51bWJlclxuICAgIHBlcmNlbnRhZ2U6IG51bWJlclxuICB9XG4gIHF1ZXN0aW9ubmFpcmVJZDogc3RyaW5nIHwgbnVsbFxufVxuXG4vKipcbiAqIOmXruWNt+WKoOi9veWZqOe7hOS7tlxuICog6LSf6LSj5LuOQVBJ5Yqg6L296Zeu5Y236YWN572u5bm25riy5p+T6Zeu5Y2355WM6Z2iXG4gKi9cbmV4cG9ydCBjb25zdCBRdWVzdGlvbm5haXJlTG9hZGVyOiBSZWFjdC5GQzxRdWVzdGlvbm5haXJlTG9hZGVyUHJvcHM+ID0gKHsgXG4gIHZlcnNpb24sIFxuICBvcmdhbml6YXRpb25JZCwgXG4gIG9uQ29tcGxldGUsXG4gIG9uQ29uZmlnTG9hZGVkIFxufSkgPT4ge1xuICAvLyDnu5/kuIDnirbmgIHnrqHnkIZcbiAgY29uc3QgW3N0YXRlLCBzZXRTdGF0ZV0gPSB1c2VTdGF0ZTxMb2FkZXJTdGF0ZT4oe1xuICAgIGNvbmZpZzogbnVsbCxcbiAgICBsb2FkaW5nOiB0cnVlLFxuICAgIGVycm9yOiBudWxsLFxuICAgIHJldHJ5Q291bnQ6IDAsXG4gICAgLy8g5rWB5byP5Yqg6L2954q25oCB5Yid5aeL5YyWXG4gICAgc3RyZWFtTG9hZGluZzogZmFsc2UsXG4gICAgbG9hZGVkUXVlc3Rpb25zOiBbXSxcbiAgICB0b3RhbEV4cGVjdGVkOiA2MCxcbiAgICBjdXJyZW50UHJvZ3Jlc3M6IHtcbiAgICAgIGNvbXBsZXRlZDogMCxcbiAgICAgIHRvdGFsOiA2MCxcbiAgICAgIHBlcmNlbnRhZ2U6IDBcbiAgICB9LFxuICAgIHF1ZXN0aW9ubmFpcmVJZDogbnVsbFxuICB9KVxuXG4gIC8qKlxuICAgKiDovazmjaJBUEnmlbDmja7kuLpRdWVzdGlvbm5haXJlQ29uZmln5qC85byPXG4gICAqL1xuICBjb25zdCB0cmFuc2Zvcm1BcGlEYXRhID0gdXNlQ2FsbGJhY2soKGFwaURhdGE6IGFueSk6IFF1ZXN0aW9ubmFpcmVDb25maWcgPT4ge1xuICAgIGNvbnN0IHF1ZXN0aW9ubmFpcmVDb25maWc6IFF1ZXN0aW9ubmFpcmVDb25maWcgPSB7XG4gICAgICB2ZXJzaW9uOiBhcGlEYXRhLnZlcnNpb24gfHwgdmVyc2lvbixcbiAgICAgIHRvdGFsX3F1ZXN0aW9uczogYXBpRGF0YS5xdWVzdGlvbnM/Lmxlbmd0aCB8fCAwLFxuICAgICAgZGltZW5zaW9uczoge1xuICAgICAgICBTRjogeyBxdWVzdGlvbnM6IFtdIH0sXG4gICAgICAgIElUOiB7IHF1ZXN0aW9uczogW10gfSxcbiAgICAgICAgTVY6IHsgcXVlc3Rpb25zOiBbXSB9LFxuICAgICAgICBBRDogeyBxdWVzdGlvbnM6IFtdIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmjInnu7TluqbliIbnu4Tpl67pophcbiAgICBpZiAoYXBpRGF0YS5xdWVzdGlvbnMgJiYgQXJyYXkuaXNBcnJheShhcGlEYXRhLnF1ZXN0aW9ucykpIHtcbiAgICAgIGFwaURhdGEucXVlc3Rpb25zLmZvckVhY2goKHE6IGFueSkgPT4ge1xuICAgICAgICAvLyDlpITnkIbnu7TluqblkI3np7DmmKDlsIRcbiAgICAgICAgbGV0IGRpbWVuc2lvbktleTogJ1NGJyB8ICdJVCcgfCAnTVYnIHwgJ0FEJ1xuICAgICAgICBzd2l0Y2ggKHEuZGltZW5zaW9uKSB7XG4gICAgICAgICAgY2FzZSAnUy9GJzpcbiAgICAgICAgICAgIGRpbWVuc2lvbktleSA9ICdTRidcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgY2FzZSAnSS9UJzpcbiAgICAgICAgICAgIGRpbWVuc2lvbktleSA9ICdJVCdcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgY2FzZSAnTS9WJzpcbiAgICAgICAgICAgIGRpbWVuc2lvbktleSA9ICdNVidcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgY2FzZSAnQS9EJzpcbiAgICAgICAgICAgIGRpbWVuc2lvbktleSA9ICdBRCdcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybihg5pyq55+l57u05bqmOiAke3EuZGltZW5zaW9ufe+8jOm7mOiupOS9v+eUqFNGYClcbiAgICAgICAgICAgIGRpbWVuc2lvbktleSA9ICdTRidcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgY29uc3QgY29uZmlnUXVlc3Rpb246IFF1ZXN0aW9uID0ge1xuICAgICAgICAgIGlkOiBxLmlkLFxuICAgICAgICAgIGRpbWVuc2lvbjogZGltZW5zaW9uS2V5LFxuICAgICAgICAgIHN1Yl9kaW1lbnNpb246IHEuc3ViZGltZW5zaW9uIHx8ICcnLFxuICAgICAgICAgIHR5cGU6IHEudHlwZSBhcyAnY2hvaWNlJyB8ICdzY2VuYXJpbycgfCAncmFua2luZycgfCAnc2NhbGUnLFxuICAgICAgICAgIHRleHQ6IHEudGl0bGUgfHwgcS50ZXh0IHx8ICcnLFxuICAgICAgICAgIG9wdGlvbnM6IHEub3B0aW9ucz8ubWFwKChvcHQ6IGFueSwgaW5kZXg6IG51bWJlcikgPT4gKHtcbiAgICAgICAgICAgIGlkOiBvcHQuaWQgfHwgYCR7cS5pZH1fb3B0XyR7aW5kZXh9YCxcbiAgICAgICAgICAgIHRleHQ6IG9wdC50ZXh0IHx8IG9wdCxcbiAgICAgICAgICAgIHZhbHVlOiBvcHQudmFsdWUgfHwgb3B0LFxuICAgICAgICAgICAgc2NvcmU6IG9wdC5zY29yZSB8fCBpbmRleCArIDFcbiAgICAgICAgICB9KSkgfHwgW10sXG4gICAgICAgICAgc2NvcmluZzoge1xuICAgICAgICAgICAgZGltZW5zaW9uX3dlaWdodDogcS53ZWlnaHQgfHwgMSxcbiAgICAgICAgICAgIHN1Yl9kaW1lbnNpb25fd2VpZ2h0OiAxLFxuICAgICAgICAgICAgb3B0aW9uX3Njb3JlczogcS5vcHRpb25zPy5tYXAoKF86IGFueSwgaW5kZXg6IG51bWJlcikgPT4gaW5kZXggKyAxKSB8fCBbXSxcbiAgICAgICAgICAgIHJldmVyc2Vfc2NvcmluZzogZmFsc2VcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIHF1ZXN0aW9ubmFpcmVDb25maWcuZGltZW5zaW9uc1tkaW1lbnNpb25LZXldLnF1ZXN0aW9ucy5wdXNoKGNvbmZpZ1F1ZXN0aW9uKVxuICAgICAgfSlcbiAgICB9XG5cbiAgICByZXR1cm4gcXVlc3Rpb25uYWlyZUNvbmZpZ1xuICB9LCBbdmVyc2lvbl0pXG5cbiAgLyoqXG4gICAqIOa1geW8j+WKoOi9vemXruWNt+mFjee9riAtIOaWsOeahOaguOW/g+aWueazlVxuICAgKi9cbiAgY29uc3QgbG9hZFF1ZXN0aW9ubmFpcmVTdHJlYW1seSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBpZiAoc3RhdGUucmV0cnlDb3VudCA+PSAzKSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIGVycm9yOiAn6YeN6K+V5qyh5pWw6L+H5aSa77yM6K+356iN5ZCO5YaN6K+VJyxcbiAgICAgICAgbG9hZGluZzogZmFsc2UsXG4gICAgICAgIHN0cmVhbUxvYWRpbmc6IGZhbHNlXG4gICAgICB9KSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBsb2FkaW5nOiB0cnVlLFxuICAgICAgc3RyZWFtTG9hZGluZzogdHJ1ZSxcbiAgICAgIGVycm9yOiBudWxsLFxuICAgICAgbG9hZGVkUXVlc3Rpb25zOiBbXSxcbiAgICAgIGN1cnJlbnRQcm9ncmVzczogeyBjb21wbGV0ZWQ6IDAsIHRvdGFsOiA2MCwgcGVyY2VudGFnZTogMCB9XG4gICAgfSkpXG5cbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ+W8gOWni+a1geW8j+WKoOi9vemXruWNt+mFjee9rjonLCB7IHZlcnNpb24sIG9yZ2FuaXphdGlvbklkIH0pXG5cbiAgICAgIC8vIOesrOS4gOatpe+8muWQr+WKqOmXruWNt+eUn+aIkO+8jOiOt+WPluesrOS4gOaJuemimOebrlxuICAgICAgY29uc3QgZmlyc3RCYXRjaFJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvcXVlc3Rpb25uYWlyZS9iYXRjaCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBvcmdhbml6YXRpb25UeXBlOiAndGVjaG5vbG9neScsXG4gICAgICAgICAgdmVyc2lvbixcbiAgICAgICAgICBiYXRjaFNpemU6IDVcbiAgICAgICAgfSlcbiAgICAgIH0pXG5cbiAgICAgIGlmICghZmlyc3RCYXRjaFJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihg5ZCv5Yqo6Zeu5Y2355Sf5oiQ5aSx6LSlOiAke2ZpcnN0QmF0Y2hSZXNwb25zZS5zdGF0dXN9YClcbiAgICAgIH1cblxuICAgICAgY29uc3QgZmlyc3RCYXRjaCA9IGF3YWl0IGZpcnN0QmF0Y2hSZXNwb25zZS5qc29uKClcblxuICAgICAgaWYgKCFmaXJzdEJhdGNoLnN1Y2Nlc3MpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGZpcnN0QmF0Y2guZXJyb3IgfHwgJ+WQr+WKqOmXruWNt+eUn+aIkOWksei0pScpXG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfmlLbliLDnrKzkuIDmibnpopjnm646JywgZmlyc3RCYXRjaC5kYXRhKVxuXG4gICAgICAvLyDmm7TmlrDnirbmgIHvvJrmmL7npLrnrKzkuIDmibnpopjnm65cbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgbG9hZGVkUXVlc3Rpb25zOiBmaXJzdEJhdGNoLmRhdGEucXVlc3Rpb25zLFxuICAgICAgICB0b3RhbEV4cGVjdGVkOiBmaXJzdEJhdGNoLmRhdGEudG90YWxFeHBlY3RlZCxcbiAgICAgICAgY3VycmVudFByb2dyZXNzOiBmaXJzdEJhdGNoLmRhdGEucHJvZ3Jlc3MsXG4gICAgICAgIHF1ZXN0aW9ubmFpcmVJZDogZmlyc3RCYXRjaC5kYXRhLnF1ZXN0aW9ubmFpcmVJZCxcbiAgICAgICAgbG9hZGluZzogZmFsc2UgLy8g56ys5LiA5om55Yqg6L295a6M5oiQ77yM5Y+v5Lul5byA5aeL5pi+56S6XG4gICAgICB9KSlcblxuICAgICAgLy8g56ys5LqM5q2l77ya57un57ut5Yqg6L295Ymp5L2Z6aKY55uuXG4gICAgICBhd2FpdCBsb2FkUmVtYWluaW5nUXVlc3Rpb25zKGZpcnN0QmF0Y2guZGF0YSlcblxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcign5rWB5byP5Yqg6L296Zeu5Y236YWN572u5aSx6LSlOicsIGVycilcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgZXJyb3I6IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAn5Yqg6L296Zeu5Y236YWN572u5aSx6LSlJyxcbiAgICAgICAgbG9hZGluZzogZmFsc2UsXG4gICAgICAgIHN0cmVhbUxvYWRpbmc6IGZhbHNlLCAvLyDnoa7kv53lnKjplJnor6/ml7bkuZ/lgZzmraLmtYHlvI/liqDovb3nirbmgIFcbiAgICAgICAgcmV0cnlDb3VudDogcHJldi5yZXRyeUNvdW50ICsgMVxuICAgICAgfSkpXG4gICAgfVxuICB9LCBbdmVyc2lvbiwgb3JnYW5pemF0aW9uSWQsIHN0YXRlLnJldHJ5Q291bnRdKVxuXG4gIC8qKlxuICAgKiDliqDovb3liankvZnpopjnm65cbiAgICovXG4gIGNvbnN0IGxvYWRSZW1haW5pbmdRdWVzdGlvbnMgPSBhc3luYyAoaW5pdGlhbEJhdGNoOiBhbnkpID0+IHtcbiAgICBsZXQgbmV4dEJhdGNoID0gaW5pdGlhbEJhdGNoLm5leHRCYXRjaFxuICAgIGxldCBzaG91bGRDb250aW51ZUxvYWRpbmcgPSB0cnVlIC8vIOS9v+eUqOWxgOmDqOWPmOmHj+aOp+WItuW+queOr1xuICAgIGxldCBiYXRjaENvdW50ID0gMFxuICAgIGNvbnN0IG1heEJhdGNoZXMgPSAyMCAvLyDpmLLmraLml6DpmZDlvqrnjq/nmoTkv53miqTmnLrliLZcblxuICAgIGNvbnNvbGUubG9nKCflvIDlp4vliqDovb3liankvZnpopjnm67vvIzliJ3lp4tuZXh0QmF0Y2g6JywgbmV4dEJhdGNoKVxuXG4gICAgd2hpbGUgKG5leHRCYXRjaCAmJiBzaG91bGRDb250aW51ZUxvYWRpbmcgJiYgYmF0Y2hDb3VudCA8IG1heEJhdGNoZXMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGJhdGNoQ291bnQrK1xuICAgICAgICBjb25zb2xlLmxvZyhgW+aJueasoSAke2JhdGNoQ291bnR9XSDliqDovb3kuIvkuIDmibnpopjnm646ICR7bmV4dEJhdGNoLmRpbWVuc2lvbn0g5LuOICR7bmV4dEJhdGNoLnN0YXJ0SW5kZXh9YClcblxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKFxuICAgICAgICAgIGAvYXBpL3F1ZXN0aW9ubmFpcmUvYmF0Y2g/cXVlc3Rpb25uYWlyZUlkPSR7aW5pdGlhbEJhdGNoLnF1ZXN0aW9ubmFpcmVJZH0mZGltZW5zaW9uPSR7bmV4dEJhdGNoLmRpbWVuc2lvbn0mc3RhcnRJbmRleD0ke25leHRCYXRjaC5zdGFydEluZGV4fSZiYXRjaFNpemU9NWBcbiAgICAgICAgKVxuXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oYFvmibnmrKEgJHtiYXRjaENvdW50fV0g5om55qyh5Yqg6L295aSx6LSlOiAke3Jlc3BvbnNlLnN0YXR1c33vvIzlgZzmraLliqDovb1gKVxuICAgICAgICAgIHNob3VsZENvbnRpbnVlTG9hZGluZyA9IGZhbHNlXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGJhdGNoRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgICAgIGlmICghYmF0Y2hEYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oYFvmibnmrKEgJHtiYXRjaENvdW50fV0g5om55qyh5pWw5o2u5peg5pWI77yM5YGc5q2i5Yqg6L29OmAsIGJhdGNoRGF0YS5lcnJvcilcbiAgICAgICAgICBzaG91bGRDb250aW51ZUxvYWRpbmcgPSBmYWxzZVxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cblxuICAgICAgICBjb25zb2xlLmxvZyhgW+aJueasoSAke2JhdGNoQ291bnR9XSDmlLbliLDmibnmrKHmlbDmja46YCwge1xuICAgICAgICAgIGRpbWVuc2lvbjogYmF0Y2hEYXRhLmRhdGEuZGltZW5zaW9uLFxuICAgICAgICAgIHF1ZXN0aW9uQ291bnQ6IGJhdGNoRGF0YS5kYXRhLnF1ZXN0aW9ucz8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgcHJvZ3Jlc3M6IGJhdGNoRGF0YS5kYXRhLnByb2dyZXNzLFxuICAgICAgICAgIGhhc05leHRCYXRjaDogISFiYXRjaERhdGEuZGF0YS5uZXh0QmF0Y2hcbiAgICAgICAgfSlcblxuICAgICAgICAvLyDntK/np6/mt7vliqDmlrDpopjnm65cbiAgICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgbG9hZGVkUXVlc3Rpb25zOiBbLi4ucHJldi5sb2FkZWRRdWVzdGlvbnMsIC4uLmJhdGNoRGF0YS5kYXRhLnF1ZXN0aW9uc10sXG4gICAgICAgICAgY3VycmVudFByb2dyZXNzOiBiYXRjaERhdGEuZGF0YS5wcm9ncmVzc1xuICAgICAgICB9KSlcblxuICAgICAgICAvLyDlh4blpIfkuIvkuIDmiblcbiAgICAgICAgbmV4dEJhdGNoID0gYmF0Y2hEYXRhLmRhdGEubmV4dEJhdGNoXG5cbiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5LiL5LiA5om577yM6K+05piO5YWo6YOo5a6M5oiQXG4gICAgICAgIGlmICghbmV4dEJhdGNoKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYFvmibnmrKEgJHtiYXRjaENvdW50fV0g5omA5pyJ6aKY55uu5Yqg6L295a6M5oiQIWApXG4gICAgICAgICAgc2hvdWxkQ29udGludWVMb2FkaW5nID0gZmFsc2VcbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g55+t5pqC5bu26L+f77yM6YG/5YWN6K+35rGC6L+H5LqO6aKR57mBXG4gICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSlcblxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihgW+aJueasoSAke2JhdGNoQ291bnR9XSDliqDovb3mibnmrKHlpLHotKU6YCwgZXJyb3IpXG4gICAgICAgIHNob3VsZENvbnRpbnVlTG9hZGluZyA9IGZhbHNlXG4gICAgICAgIGJyZWFrXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g56Gu5L+d5Zyo5omA5pyJ6YCA5Ye66Lev5b6E6YO95pu05pawc3RyZWFtTG9hZGluZ+eKtuaAgVxuICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBzdHJlYW1Mb2FkaW5nOiBmYWxzZVxuICAgIH0pKVxuXG4gICAgaWYgKGJhdGNoQ291bnQgPj0gbWF4QmF0Y2hlcykge1xuICAgICAgY29uc29sZS53YXJuKCfovr7liLDmnIDlpKfmibnmrKHpmZDliLbvvIzlgZzmraLliqDovb0nKVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGDpopjnm67liqDovb3mtYHnqIvnu5PmnZ/vvIzlhbHlpITnkIYgJHtiYXRjaENvdW50fSDkuKrmibnmrKFgKVxuICB9XG5cbiAgLyoqXG4gICAqIOS8oOe7n+eahOWujOaVtOWKoOi9veaWueazle+8iOS/neeVmeS9nOS4uuWkh+eUqO+8iVxuICAgKiDliqDovb3pl67ljbfphY3nva5cbiAgICovXG4gIGNvbnN0IGxvYWRRdWVzdGlvbm5haXJlQ29uZmlnID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IFxuICAgICAgICAuLi5wcmV2LCBcbiAgICAgICAgbG9hZGluZzogdHJ1ZSwgXG4gICAgICAgIGVycm9yOiBudWxsIFxuICAgICAgfSkpXG5cbiAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vliqDovb3pl67ljbfphY3nva46JywgeyB2ZXJzaW9uLCBvcmdhbml6YXRpb25JZCB9KVxuXG4gICAgICAvLyDliJvlu7pBYm9ydENvbnRyb2xsZXLnlKjkuo7otoXml7bmjqfliLZcbiAgICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKClcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4gY29udHJvbGxlci5hYm9ydCgpLCAxNSAqIDYwICogMTAwMCkgLy8gMTXliIbpkp/otoXml7ZcblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9xdWVzdGlvbm5haXJlL2dlbmVyYXRlJywge1xuICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgICB9LFxuICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAgIHZlcnNpb24sXG4gICAgICAgICAgICBvcmdhbml6YXRpb25UeXBlOiAndGVjaG5vbG9neScsXG4gICAgICAgICAgICBvcmdhbml6YXRpb25JZFxuICAgICAgICAgIH0pLFxuICAgICAgICAgIHNpZ25hbDogY29udHJvbGxlci5zaWduYWxcbiAgICAgICAgfSlcblxuICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKVxuXG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYOeUn+aIkOmXruWNt+Wksei0pTogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICAgICAgaWYgKCFyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihyZXN1bHQuZXJyb3I/Lm1lc3NhZ2UgfHwgJ+eUn+aIkOmXruWNt+Wksei0pScpXG4gICAgICAgIH1cblxuICAgICAgICBjb25zb2xlLmxvZygnQVBJ6L+U5Zue5pWw5o2uOicsIHJlc3VsdC5kYXRhKVxuXG4gICAgICAgIC8vIOi9rOaNokFQSeaVsOaNrlxuICAgICAgICBjb25zdCBxdWVzdGlvbm5haXJlQ29uZmlnID0gdHJhbnNmb3JtQXBpRGF0YShyZXN1bHQuZGF0YSlcblxuICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBjb25maWc6IHF1ZXN0aW9ubmFpcmVDb25maWcsXG4gICAgICAgICAgbG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgcmV0cnlDb3VudDogMFxuICAgICAgICB9KSlcblxuICAgICAgICAvLyDpgJrnn6XniLbnu4Tku7bphY3nva7lt7LliqDovb1cbiAgICAgICAgaWYgKG9uQ29uZmlnTG9hZGVkKSB7XG4gICAgICAgICAgb25Db25maWdMb2FkZWQocXVlc3Rpb25uYWlyZUNvbmZpZylcbiAgICAgICAgfVxuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKVxuICAgICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciAmJiBlcnJvci5uYW1lID09PSAnQWJvcnRFcnJvcicpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+axgui2heaXtu+8jOmXruWNt+eUn+aIkOaXtumXtOi/h+mVv++8jOivt+eojeWQjumHjeivlScpXG4gICAgICAgIH1cbiAgICAgICAgdGhyb3cgZXJyb3JcbiAgICAgIH1cbiAgICAgIFxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcign5Yqg6L296Zeu5Y236YWN572u5aSx6LSlOicsIGVycilcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgZXJyb3I6IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAn5Yqg6L296Zeu5Y236YWN572u5aSx6LSlJyxcbiAgICAgICAgbG9hZGluZzogZmFsc2UsXG4gICAgICAgIHJldHJ5Q291bnQ6IHByZXYucmV0cnlDb3VudCArIDFcbiAgICAgIH0pKVxuICAgIH1cbiAgfSwgW3ZlcnNpb24sIG9yZ2FuaXphdGlvbklkLCBvbkNvbmZpZ0xvYWRlZCwgdHJhbnNmb3JtQXBpRGF0YV0pXG5cbiAgLyoqXG4gICAqIOWkhOeQhumXruWNt+WujOaIkFxuICAgKi9cbiAgY29uc3QgaGFuZGxlUXVlc3Rpb25uYWlyZUNvbXBsZXRlID0gdXNlQ2FsbGJhY2soYXN5bmMgKHJlc3BvbnNlczogUXVlc3Rpb25uYWlyZVJlc3BvbnNlW10pID0+IHtcbiAgICBjb25zb2xlLmxvZygn6Zeu5Y235a6M5oiQ77yM5Zue562U5pWw6YePOicsIHJlc3BvbnNlcy5sZW5ndGgpXG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGlmIChvbkNvbXBsZXRlKSB7XG4gICAgICAgIGF3YWl0IG9uQ29tcGxldGUocmVzcG9uc2VzKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCflpITnkIbpl67ljbflrozmiJDlpLHotKU6JywgZXJyb3IpXG4gICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDplJnor6/lpITnkIbpgLvovpFcbiAgICB9XG4gIH0sIFtvbkNvbXBsZXRlXSlcblxuICAvKipcbiAgICog6YeN6K+V5Yqg6L29XG4gICAqL1xuICBjb25zdCBoYW5kbGVSZXRyeSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoc3RhdGUucmV0cnlDb3VudCA8IDMpIHtcbiAgICAgIGxvYWRRdWVzdGlvbm5haXJlQ29uZmlnKClcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBlcnJvcjogJ+mHjeivleasoeaVsOi/h+Wkmu+8jOivt+WIt+aWsOmhtemdoumHjeivlSdcbiAgICAgIH0pKVxuICAgIH1cbiAgfSwgW2xvYWRRdWVzdGlvbm5haXJlQ29uZmlnLCBzdGF0ZS5yZXRyeUNvdW50XSlcblxuICAvLyDliJ3lp4vljJbliqDovb0gLSDkvb/nlKjmtYHlvI/liqDovb1cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkUXVlc3Rpb25uYWlyZVN0cmVhbWx5KClcbiAgfSwgW2xvYWRRdWVzdGlvbm5haXJlU3RyZWFtbHldKVxuXG4gIC8vIOa4suafk+WIneWni+WKoOi9veeKtuaAge+8iOWPquWcqOesrOS4gOaJuemimOebruWKoOi9veWJjeaYvuekuu+8iVxuICBpZiAoc3RhdGUubG9hZGluZyAmJiBzdGF0ZS5sb2FkZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtWzQwMHB4XVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGZvbnQtbWVkaXVtXCI+5q2j5Zyo55Sf5oiQ56ys5LiA5om56aKY55uuLi4uPC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIG10LTFcIj7niYjmnKw6IHt2ZXJzaW9ufTwvcD5cbiAgICAgICAgICAgIHtvcmdhbml6YXRpb25JZCAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPue7hOe7h0lEOiB7b3JnYW5pemF0aW9uSWR9PC9wPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyDmuLLmn5PplJnor6/nirbmgIFcbiAgaWYgKHN0YXRlLmVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtWzQwMHB4XVwiPlxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJtYXgtdy1tZCB3LWZ1bGxcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC02eGwgbWItNFwiPuKaoO+4jzwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj7liqDovb3lpLHotKU8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00IHRleHQtc21cIj57c3RhdGUuZXJyb3J9PC9wPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUmV0cnl9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3N0YXRlLnJldHJ5Q291bnQgPj0gM31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3N0YXRlLnJldHJ5Q291bnQgPj0gMyA/ICfph43or5XmrKHmlbDlt7LnlKjlrownIDogYOmHjeaWsOWKoOi9vSAoJHtzdGF0ZS5yZXRyeUNvdW50fS8zKWB9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAge3N0YXRlLnJldHJ5Q291bnQgPj0gMyAmJiAoXG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDliLfmlrDpobXpnaJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgdGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgIDxwPueJiOacrDoge3ZlcnNpb259PC9wPlxuICAgICAgICAgICAgICB7b3JnYW5pemF0aW9uSWQgJiYgPHA+57uE57uHSUQ6IHtvcmdhbml6YXRpb25JZH08L3A+fVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIC8vIOa4suafk+a1geW8j+WKoOi9veeKtuaAgSAtIOaYvuekuuW3suWKoOi9veeahOmimOebrlxuICBpZiAoc3RhdGUubG9hZGVkUXVlc3Rpb25zLmxlbmd0aCA+IDApIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgey8qIOa1geW8j+WKoOi9vei/m+W6puaPkOekuiAqL31cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPXtgYm9yZGVyLTIgJHtzdGF0ZS5zdHJlYW1Mb2FkaW5nID8gJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMjAwJyA6ICdiZy1ncmVlbi01MCBib3JkZXItZ3JlZW4tMjAwJ31gfT5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQteGwgbXItMyAke3N0YXRlLnN0cmVhbUxvYWRpbmcgPyAndGV4dC1ibHVlLTUwMCcgOiAndGV4dC1ncmVlbi01MDAnfWB9PlxuICAgICAgICAgICAgICAgIHtzdGF0ZS5zdHJlYW1Mb2FkaW5nID8gJ/CflIQnIDogJ+KchSd9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgJHtzdGF0ZS5zdHJlYW1Mb2FkaW5nID8gJ3RleHQtYmx1ZS05MDAnIDogJ3RleHQtZ3JlZW4tOTAwJ31gfT5cbiAgICAgICAgICAgICAgICAgIHtzdGF0ZS5zdHJlYW1Mb2FkaW5nID8gJ+mXruWNt+eUn+aIkOS4rS4uLicgOiAn6Zeu5Y2355Sf5oiQ5a6M5oiQJ31cbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQtc20gJHtzdGF0ZS5zdHJlYW1Mb2FkaW5nID8gJ3RleHQtYmx1ZS03MDAnIDogJ3RleHQtZ3JlZW4tNzAwJ31gfT5cbiAgICAgICAgICAgICAgICAgIOW3sueUn+aIkCB7c3RhdGUubG9hZGVkUXVlc3Rpb25zLmxlbmd0aH0gLyB7c3RhdGUudG90YWxFeHBlY3RlZH0g6YGT6aKY55uuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIHtzdGF0ZS5zdHJlYW1Mb2FkaW5nICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ibHVlLTIwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke3N0YXRlLmN1cnJlbnRQcm9ncmVzcy5wZXJjZW50YWdlfSVgIH19XG4gICAgICAgICAgICAgICAgICAgICAgPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7c3RhdGUuY3VycmVudFByb2dyZXNzLnBlcmNlbnRhZ2V9JSDlrozmiJBcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodCB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PueJiOacrDoge3ZlcnNpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj7lt7LliqDovb06IHtzdGF0ZS5sb2FkZWRRdWVzdGlvbnMubGVuZ3RofSDpopg8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiDkvb/nlKjmtYHlvI/muLLmn5PlmaggKi99XG4gICAgICAgIDxTdHJlYW1RdWVzdGlvbm5haXJlUmVuZGVyZXJcbiAgICAgICAgICBxdWVzdGlvbnM9e3N0YXRlLmxvYWRlZFF1ZXN0aW9uc31cbiAgICAgICAgICBpc0xvYWRpbmc9e3N0YXRlLnN0cmVhbUxvYWRpbmd9XG4gICAgICAgICAgcHJvZ3Jlc3M9e3N0YXRlLmN1cnJlbnRQcm9ncmVzc31cbiAgICAgICAgICBvbkFuc3dlckNoYW5nZT17KHF1ZXN0aW9uSWQsIGFuc3dlcikgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+etlOahiOWPmOabtDonLCBxdWVzdGlvbklkLCBhbnN3ZXIpXG4gICAgICAgICAgfX1cbiAgICAgICAgICBvbkNvbXBsZXRlPXsocmVzcG9uc2VzKSA9PiB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn6Zeu5Y235a6M5oiQOicsIHJlc3BvbnNlcylcbiAgICAgICAgICAgIGlmIChoYW5kbGVRdWVzdGlvbm5haXJlQ29tcGxldGUpIHtcbiAgICAgICAgICAgICAgLy8g6L2s5o2i5Li65pen5qC85byP5YW85a65XG4gICAgICAgICAgICAgIGNvbnN0IGNvbnZlcnRlZFJlc3BvbnNlcyA9IHJlc3BvbnNlcy5tYXAociA9PiAoe1xuICAgICAgICAgICAgICAgIHF1ZXN0aW9uSWQ6IHIucXVlc3Rpb25JZCxcbiAgICAgICAgICAgICAgICBhbnN3ZXI6IHIuYW5zd2VyLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogci50aW1lc3RhbXBcbiAgICAgICAgICAgICAgfSkpXG4gICAgICAgICAgICAgIGhhbmRsZVF1ZXN0aW9ubmFpcmVDb21wbGV0ZShjb252ZXJ0ZWRSZXNwb25zZXMgYXMgYW55KVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGVcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgLy8g5Lyg57uf55qE5a6M5pW05Yqg6L295oiQ5Yqf54q25oCB77yI5L+d55WZ5YW85a655oCn77yJXG4gIGlmIChzdGF0ZS5jb25maWcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgPFF1ZXN0aW9ubmFpcmVSZW5kZXJlclxuICAgICAgICAgIGNvbmZpZz17c3RhdGUuY29uZmlnfVxuICAgICAgICAgIG9uQ29tcGxldGU9e2hhbmRsZVF1ZXN0aW9ubmFpcmVDb21wbGV0ZX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZVwiXG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gbnVsbFxufVxuXG4vLyDpu5jorqTlr7zlh7rku6Xkv53mjIHlhbzlrrnmgKdcbmV4cG9ydCBkZWZhdWx0IFF1ZXN0aW9ubmFpcmVMb2FkZXJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJDYXJkIiwiQnV0dG9uIiwiUXVlc3Rpb25uYWlyZVJlbmRlcmVyIiwiU3RyZWFtUXVlc3Rpb25uYWlyZVJlbmRlcmVyIiwiUXVlc3Rpb25uYWlyZUxvYWRlciIsInZlcnNpb24iLCJvcmdhbml6YXRpb25JZCIsIm9uQ29tcGxldGUiLCJvbkNvbmZpZ0xvYWRlZCIsInN0YXRlIiwic2V0U3RhdGUiLCJjb25maWciLCJsb2FkaW5nIiwiZXJyb3IiLCJyZXRyeUNvdW50Iiwic3RyZWFtTG9hZGluZyIsImxvYWRlZFF1ZXN0aW9ucyIsInRvdGFsRXhwZWN0ZWQiLCJjdXJyZW50UHJvZ3Jlc3MiLCJjb21wbGV0ZWQiLCJ0b3RhbCIsInBlcmNlbnRhZ2UiLCJxdWVzdGlvbm5haXJlSWQiLCJ0cmFuc2Zvcm1BcGlEYXRhIiwiYXBpRGF0YSIsInF1ZXN0aW9ubmFpcmVDb25maWciLCJ0b3RhbF9xdWVzdGlvbnMiLCJxdWVzdGlvbnMiLCJsZW5ndGgiLCJkaW1lbnNpb25zIiwiU0YiLCJJVCIsIk1WIiwiQUQiLCJBcnJheSIsImlzQXJyYXkiLCJmb3JFYWNoIiwicSIsImRpbWVuc2lvbktleSIsImRpbWVuc2lvbiIsImNvbnNvbGUiLCJ3YXJuIiwiY29uZmlnUXVlc3Rpb24iLCJpZCIsInN1Yl9kaW1lbnNpb24iLCJzdWJkaW1lbnNpb24iLCJ0eXBlIiwidGV4dCIsInRpdGxlIiwib3B0aW9ucyIsIm1hcCIsIm9wdCIsImluZGV4IiwidmFsdWUiLCJzY29yZSIsInNjb3JpbmciLCJkaW1lbnNpb25fd2VpZ2h0Iiwid2VpZ2h0Iiwic3ViX2RpbWVuc2lvbl93ZWlnaHQiLCJvcHRpb25fc2NvcmVzIiwiXyIsInJldmVyc2Vfc2NvcmluZyIsInB1c2giLCJsb2FkUXVlc3Rpb25uYWlyZVN0cmVhbWx5IiwicHJldiIsImxvZyIsImZpcnN0QmF0Y2hSZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwib3JnYW5pemF0aW9uVHlwZSIsImJhdGNoU2l6ZSIsIm9rIiwiRXJyb3IiLCJzdGF0dXMiLCJmaXJzdEJhdGNoIiwianNvbiIsInN1Y2Nlc3MiLCJkYXRhIiwicHJvZ3Jlc3MiLCJsb2FkUmVtYWluaW5nUXVlc3Rpb25zIiwiZXJyIiwibWVzc2FnZSIsImluaXRpYWxCYXRjaCIsIm5leHRCYXRjaCIsInNob3VsZENvbnRpbnVlTG9hZGluZyIsImJhdGNoQ291bnQiLCJtYXhCYXRjaGVzIiwic3RhcnRJbmRleCIsInJlc3BvbnNlIiwiYmF0Y2hEYXRhIiwicXVlc3Rpb25Db3VudCIsImhhc05leHRCYXRjaCIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImxvYWRRdWVzdGlvbm5haXJlQ29uZmlnIiwiY29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsInRpbWVvdXRJZCIsImFib3J0Iiwic2lnbmFsIiwiY2xlYXJUaW1lb3V0Iiwic3RhdHVzVGV4dCIsInJlc3VsdCIsIm5hbWUiLCJoYW5kbGVRdWVzdGlvbm5haXJlQ29tcGxldGUiLCJyZXNwb25zZXMiLCJoYW5kbGVSZXRyeSIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJoMyIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInZhcmlhbnQiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInJlbG9hZCIsInN0eWxlIiwid2lkdGgiLCJpc0xvYWRpbmciLCJvbkFuc3dlckNoYW5nZSIsInF1ZXN0aW9uSWQiLCJhbnN3ZXIiLCJjb252ZXJ0ZWRSZXNwb25zZXMiLCJyIiwidGltZXN0YW1wIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionnaireLoader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionnaireProgress.tsx":
/*!****************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireProgress.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireProgress: () => (/* binding */ QuestionnaireProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressBar */ \"(ssr)/./src/components/questionnaire/ProgressBar.tsx\");\n/* harmony import */ var _QuestionCounter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuestionCounter */ \"(ssr)/./src/components/questionnaire/QuestionCounter.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireProgress auto */ \n\n\n\n/**\n * 问卷进度组件\n * 显示当前进度和问题计数\n */ const QuestionnaireProgress = ({ current, total, progress, dimension = \"SF\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionCounter__WEBPACK_IMPORTED_MODULE_3__.QuestionCounter, {\n                current: current,\n                total: total,\n                dimension: dimension\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_2__.ProgressBar, {\n                progress: progress\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9xdWVzdGlvbm5haXJlL1F1ZXN0aW9ubmFpcmVQcm9ncmVzcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFeUI7QUFDa0I7QUFDUTtBQVNuRDs7O0NBR0MsR0FDTSxNQUFNRyx3QkFBOEQsQ0FBQyxFQUMxRUMsT0FBTyxFQUNQQyxLQUFLLEVBQ0xDLFFBQVEsRUFDUkMsWUFBWSxJQUFJLEVBQ2pCO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDUCw2REFBZUE7Z0JBQUNFLFNBQVNBO2dCQUFTQyxPQUFPQTtnQkFBT0UsV0FBV0E7Ozs7OzswQkFDNUQsOERBQUNOLHFEQUFXQTtnQkFBQ0ssVUFBVUE7Ozs7Ozs7Ozs7OztBQUc3QixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9jb21wb25lbnRzL3F1ZXN0aW9ubmFpcmUvUXVlc3Rpb25uYWlyZVByb2dyZXNzLnRzeD8wYjM5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBQcm9ncmVzc0JhciB9IGZyb20gJy4vUHJvZ3Jlc3NCYXInXG5pbXBvcnQgeyBRdWVzdGlvbkNvdW50ZXIgfSBmcm9tICcuL1F1ZXN0aW9uQ291bnRlcidcblxuaW50ZXJmYWNlIFF1ZXN0aW9ubmFpcmVQcm9ncmVzc1Byb3BzIHtcbiAgY3VycmVudDogbnVtYmVyXG4gIHRvdGFsOiBudW1iZXJcbiAgcHJvZ3Jlc3M6IG51bWJlclxuICBkaW1lbnNpb24/OiAnU0YnIHwgJ0lUJyB8ICdNVicgfCAnQUQnXG59XG5cbi8qKlxuICog6Zeu5Y236L+b5bqm57uE5Lu2XG4gKiDmmL7npLrlvZPliY3ov5vluqblkozpl67popjorqHmlbBcbiAqL1xuZXhwb3J0IGNvbnN0IFF1ZXN0aW9ubmFpcmVQcm9ncmVzczogUmVhY3QuRkM8UXVlc3Rpb25uYWlyZVByb2dyZXNzUHJvcHM+ID0gKHtcbiAgY3VycmVudCxcbiAgdG90YWwsXG4gIHByb2dyZXNzLFxuICBkaW1lbnNpb24gPSAnU0YnXG59KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgIDxRdWVzdGlvbkNvdW50ZXIgY3VycmVudD17Y3VycmVudH0gdG90YWw9e3RvdGFsfSBkaW1lbnNpb249e2RpbWVuc2lvbn0gLz5cbiAgICAgIDxQcm9ncmVzc0JhciBwcm9ncmVzcz17cHJvZ3Jlc3N9IC8+XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJQcm9ncmVzc0JhciIsIlF1ZXN0aW9uQ291bnRlciIsIlF1ZXN0aW9ubmFpcmVQcm9ncmVzcyIsImN1cnJlbnQiLCJ0b3RhbCIsInByb2dyZXNzIiwiZGltZW5zaW9uIiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionnaireProgress.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionnaireRenderer.tsx":
/*!****************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireRenderer.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireRenderer: () => (/* binding */ QuestionnaireRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useQuestionnaire__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useQuestionnaire */ \"(ssr)/./src/hooks/useQuestionnaire.ts\");\n/* harmony import */ var _QuestionDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuestionDisplay */ \"(ssr)/./src/components/questionnaire/QuestionDisplay.tsx\");\n/* harmony import */ var _QuestionnaireProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuestionnaireProgress */ \"(ssr)/./src/components/questionnaire/QuestionnaireProgress.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Loading */ \"(ssr)/./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireRenderer auto */ \n\n\n\n\n\n\n\n/**\n * 问卷渲染器主组件\n * 负责协调各个子组件，保持简洁\n */ const QuestionnaireRenderer = ({ config, initialResponses = [], onComplete, onSave, className })=>{\n    const { currentQuestion, currentQuestionIndex, responses, errors, isLoading, isFirst, isLast, handleNext, handlePrevious, updateResponse, progress } = (0,_hooks_useQuestionnaire__WEBPACK_IMPORTED_MODULE_2__.useQuestionnaire)({\n        config,\n        initialResponses,\n        onComplete,\n        onSave\n    });\n    // 获取当前问题的回答值\n    const getCurrentValue = ()=>{\n        if (!currentQuestion) return undefined;\n        const response = responses.find((r)=>r.questionId === currentQuestion.id);\n        return response?.answer;\n    };\n    // 处理答案更新\n    const handleAnswerChange = (value)=>{\n        if (currentQuestion) {\n            updateResponse(currentQuestion.id, value);\n        }\n    };\n    // 获取当前问题的维度\n    const getCurrentDimension = ()=>{\n        return currentQuestion?.dimension || \"SF\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_6__.Loading, {\n                size: \"lg\",\n                text: \"正在提交问卷...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionnaireProgress__WEBPACK_IMPORTED_MODULE_4__.QuestionnaireProgress, {\n                current: currentQuestionIndex + 1,\n                total: config.total_questions,\n                progress: progress,\n                dimension: getCurrentDimension()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6 mt-4\",\n                children: [\n                    currentQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionDisplay__WEBPACK_IMPORTED_MODULE_3__.QuestionDisplay, {\n                        question: currentQuestion,\n                        value: getCurrentValue(),\n                        onChange: handleAnswerChange,\n                        error: errors[currentQuestion.id]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: \"问题加载中...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8 pt-6 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrevious,\n                                disabled: isFirst || isLoading,\n                                className: \"px-6 py-2\",\n                                children: \"上一题\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"第 \",\n                                    currentQuestionIndex + 1,\n                                    \" 题，共 \",\n                                    config.total_questions,\n                                    \" 题\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: handleNext,\n                                disabled: isLoading,\n                                className: \"px-6 py-2\",\n                                children: isLast ? \"提交问卷\" : \"下一题\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    currentQuestion && errors[currentQuestion.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-red-500\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: errors[currentQuestion.id]\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionnaireRenderer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/questionnaire/StreamQuestionnaireRenderer.tsx ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamQuestionnaireRenderer: () => (/* binding */ StreamQuestionnaireRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamQuestionnaireRenderer auto */ \n\n\n\n/**\n * 流式问卷渲染器\n * 支持动态加载和显示题目\n */ const StreamQuestionnaireRenderer = ({ questions, isLoading, progress, onAnswerChange, onComplete, className = \"\" })=>{\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [wasWaitingForMore, setWasWaitingForMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /**\n   * 监听题目数量变化，自动导航到新加载的题目\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const isLastQuestion = currentQuestionIndex === questions.length - 1;\n        const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total;\n        // 如果之前在等待更多题目，现在有新题目了，自动进入下一题\n        if (wasWaitingForMore && !shouldWaitForMore && questions.length > currentQuestionIndex + 1) {\n            console.log(\"检测到新题目加载，自动进入下一题\");\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n            setWasWaitingForMore(false);\n        } else if (shouldWaitForMore && !wasWaitingForMore) {\n            // 开始等待更多题目\n            setWasWaitingForMore(true);\n        } else if (!shouldWaitForMore && wasWaitingForMore) {\n            // 停止等待\n            setWasWaitingForMore(false);\n        }\n    }, [\n        questions.length,\n        isLoading,\n        currentQuestionIndex,\n        progress.total,\n        wasWaitingForMore\n    ]);\n    /**\n   * 处理答案变更\n   */ const handleAnswerChange = (questionId, answer)=>{\n        const newAnswers = {\n            ...answers,\n            [questionId]: answer\n        };\n        setAnswers(newAnswers);\n        if (onAnswerChange) {\n            onAnswerChange(questionId, answer);\n        }\n    };\n    /**\n   * 下一题\n   */ const handleNext = ()=>{\n        if (currentQuestionIndex < questions.length - 1) {\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n        }\n    };\n    /**\n   * 上一题\n   */ const handlePrevious = ()=>{\n        if (currentQuestionIndex > 0) {\n            setCurrentQuestionIndex(currentQuestionIndex - 1);\n        }\n    };\n    /**\n   * 提交问卷\n   */ const handleSubmit = ()=>{\n        const responses = Object.entries(answers).map(([questionId, answer])=>({\n                questionId,\n                answer,\n                timestamp: new Date()\n            }));\n        if (onComplete) {\n            onComplete(responses);\n        }\n    };\n    // 如果没有题目，显示等待状态\n    if (questions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: `p-8 text-center ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"正在生成第一批题目...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentQuestion = questions[currentQuestionIndex];\n    const isLastQuestion = currentQuestionIndex === questions.length - 1;\n    const canProceed = answers[currentQuestion?.id];\n    // 判断是否应该显示\"等待更多题目\"\n    const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"题目进度\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    currentQuestionIndex + 1,\n                                    \" / \",\n                                    questions.length,\n                                    isLoading && ` (共${progress.total}题)`\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${(currentQuestionIndex + 1) / Math.max(questions.length, progress.total) * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-blue-600 mt-1\",\n                        children: [\n                            \"正在生成更多题目... (\",\n                            progress.percentage,\n                            \"% 完成)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-600 font-medium\",\n                                            children: [\n                                                currentQuestion.dimension,\n                                                \" 维度\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"第 \",\n                                                currentQuestion.order,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 leading-relaxed\",\n                                    children: currentQuestion.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQuestion.options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            name: currentQuestion.id,\n                                            value: option.score,\n                                            checked: answers[currentQuestion.id] === option.score,\n                                            onChange: ()=>handleAnswerChange(currentQuestion.id, option.score),\n                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-gray-700 flex-1\",\n                                            children: option.text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                option.score,\n                                                \"分\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, option.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePrevious,\n                                    disabled: currentQuestionIndex === 0,\n                                    variant: \"outline\",\n                                    children: \"上一题\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: !isLastQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleNext,\n                                        disabled: !canProceed,\n                                        children: \"下一题\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, undefined) : shouldWaitForMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        disabled: true,\n                                        className: \"bg-blue-500 hover:bg-blue-600\",\n                                        children: [\n                                            \"等待更多题目... (\",\n                                            questions.length,\n                                            \"/\",\n                                            progress.total,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleSubmit,\n                                        disabled: !canProceed,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: \"提交问卷\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, undefined),\n            Object.keys(answers).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: [\n                            \"已回答题目 (\",\n                            Object.keys(answers).length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-10 gap-2\",\n                        children: questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentQuestionIndex(index),\n                                className: `\n                  w-8 h-8 rounded text-xs font-medium transition-colors\n                  ${answers[question.id] ? \"bg-green-100 text-green-700 border border-green-300\" : \"bg-gray-100 text-gray-500 border border-gray-300\"}\n                  ${index === currentQuestionIndex ? \"ring-2 ring-blue-500\" : \"hover:bg-gray-200\"}\n                `,\n                                children: index + 1\n                            }, question.id, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Button = ({ children, onClick, disabled = false, variant = \"default\", size = \"md\", className = \"\", type = \"button\" })=>{\n    const baseClasses = \"inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    const variantClasses = {\n        default: \"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500\",\n        outline: \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500\",\n        ghost: \"text-gray-700 hover:bg-gray-100 focus:ring-blue-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm\",\n        md: \"px-4 py-2 text-sm\",\n        lg: \"px-6 py-3 text-base\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled,\n        className: `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Button.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg border border-gray-200 shadow-sm ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardHeader = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `px-6 py-4 border-b border-gray-200 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardContent = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `px-6 py-4 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardTitle = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        className: `text-lg font-semibold text-gray-900 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardDescription = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: `text-sm text-gray-600 mt-1 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Loading.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/Loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loading: () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Loading = ({ size = \"md\", text = \"加载中...\", className = \"\" })=>{\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        md: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col items-center justify-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `animate-spin rounded-full border-b-2 border-blue-600 ${sizeClasses[size]} mb-2`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 text-sm\",\n                children: text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n                lineNumber: 23,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUI7QUFRbEIsTUFBTUMsVUFBa0MsQ0FBQyxFQUM5Q0MsT0FBTyxJQUFJLEVBQ1hDLE9BQU8sUUFBUSxFQUNmQyxZQUFZLEVBQUUsRUFDZjtJQUNDLE1BQU1DLGNBQWM7UUFDbEJDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUwsV0FBVyxDQUFDLDBDQUEwQyxFQUFFQSxVQUFVLENBQUM7OzBCQUN0RSw4REFBQ0s7Z0JBQUlMLFdBQVcsQ0FBQyxxREFBcUQsRUFBRUMsV0FBVyxDQUFDSCxLQUFLLENBQUMsS0FBSyxDQUFDOzs7Ozs7WUFDL0ZDLHNCQUFRLDhEQUFDTztnQkFBRU4sV0FBVTswQkFBeUJEOzs7Ozs7Ozs7Ozs7QUFHckQsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktYXNzZXNzbWVudC1zeXN0ZW0vLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nLnRzeD9kZmQyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIExvYWRpbmdQcm9wcyB7XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZydcbiAgdGV4dD86IHN0cmluZ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGNvbnN0IExvYWRpbmc6IFJlYWN0LkZDPExvYWRpbmdQcm9wcz4gPSAoeyBcbiAgc2l6ZSA9ICdtZCcsIFxuICB0ZXh0ID0gJ+WKoOi9veS4rS4uLicsIFxuICBjbGFzc05hbWUgPSAnJyBcbn0pID0+IHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206ICdoLTQgdy00JyxcbiAgICBtZDogJ2gtOCB3LTgnLCBcbiAgICBsZzogJ2gtMTIgdy0xMidcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciAke2NsYXNzTmFtZX1gfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCAke3NpemVDbGFzc2VzW3NpemVdfSBtYi0yYH0+PC9kaXY+XG4gICAgICB7dGV4dCAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc21cIj57dGV4dH08L3A+fVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMb2FkaW5nIiwic2l6ZSIsInRleHQiLCJjbGFzc05hbWUiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsImRpdiIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useQuestionnaire.ts":
/*!***************************************!*\
  !*** ./src/hooks/useQuestionnaire.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuestionnaire: () => (/* binding */ useQuestionnaire)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useQuestionnaire auto */ \n/**\n * 问卷逻辑管理Hook\n * 将复杂的状态管理逻辑从组件中抽离\n */ function useQuestionnaire({ config, initialResponses, onComplete, onSave }) {\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [responses, setResponses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // 从QuestionnaireConfig中提取所有问题\n    const allQuestions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const questions = [];\n        Object.values(config.dimensions).forEach((dimension)=>{\n            questions.push(...dimension.questions);\n        });\n        return questions;\n    }, [\n        config\n    ]);\n    // 计算派生状态\n    const currentQuestion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>allQuestions[currentQuestionIndex], [\n        allQuestions,\n        currentQuestionIndex\n    ]);\n    const isFirst = currentQuestionIndex === 0;\n    const isLast = currentQuestionIndex === allQuestions.length - 1;\n    const progress = (currentQuestionIndex + 1) / allQuestions.length * 100;\n    // 更新回答 - 修复参数类型\n    const updateResponse = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((questionId, answer)=>{\n        setResponses((prev)=>{\n            const existing = prev.find((r)=>r.questionId === questionId);\n            if (existing) {\n                return prev.map((r)=>r.questionId === questionId ? {\n                        ...r,\n                        answer\n                    } : r);\n            }\n            return [\n                ...prev,\n                {\n                    questionId,\n                    answer\n                }\n            ];\n        });\n        // 清除错误\n        setErrors((prev)=>({\n                ...prev,\n                [questionId]: \"\"\n            }));\n    }, []);\n    // 验证当前问题\n    const validateCurrentQuestion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!currentQuestion) return true;\n        const response = responses.find((r)=>r.questionId === currentQuestion.id);\n        if (!response || response.answer === undefined || response.answer === null || response.answer === \"\") {\n            setErrors((prev)=>({\n                    ...prev,\n                    [currentQuestion.id]: \"请回答此问题后再继续\"\n                }));\n            return false;\n        }\n        return true;\n    }, [\n        currentQuestion,\n        responses\n    ]);\n    // 下一题\n    const handleNext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!validateCurrentQuestion()) return;\n        if (isLast) {\n            setIsLoading(true);\n            try {\n                // 转换为QuestionnaireResponse格式\n                const questionnaireResponse = {\n                    id: `response_${Date.now()}`,\n                    questionnaireId: \"questionnaire_1\",\n                    responses: responses.map((r)=>{\n                        // 找到对应的问题以获取dimension\n                        const question = allQuestions.find((q)=>q.id === r.questionId);\n                        return {\n                            id: `resp_${r.questionId}`,\n                            assessmentId: \"assessment_1\",\n                            questionId: r.questionId,\n                            respondentId: \"user_1\",\n                            answer: r.answer,\n                            dimension: question?.dimension || \"SF\",\n                            createdAt: new Date(),\n                            updatedAt: new Date()\n                        };\n                    }),\n                    metadata: {\n                        startTime: new Date().toISOString(),\n                        endTime: new Date().toISOString(),\n                        totalTimeSpent: 0,\n                        submittedAt: new Date().toISOString(),\n                        version: config.version,\n                        completionRate: 100\n                    },\n                    status: \"completed\",\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                await onComplete([\n                    questionnaireResponse\n                ]);\n            } catch (error) {\n                console.error(\"提交问卷失败:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        } else {\n            setCurrentQuestionIndex((prev)=>prev + 1);\n        }\n    }, [\n        validateCurrentQuestion,\n        isLast,\n        responses,\n        onComplete\n    ]);\n    // 上一题\n    const handlePrevious = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isFirst) {\n            setCurrentQuestionIndex((prev)=>prev - 1);\n        }\n    }, [\n        isFirst\n    ]);\n    return {\n        currentQuestion,\n        currentQuestionIndex,\n        responses,\n        errors,\n        isLoading,\n        isFirst,\n        isLast,\n        progress,\n        handleNext,\n        handlePrevious,\n        updateResponse\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useQuestionnaire.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"72fd08dda53a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YzU3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcyZmQwOGRkYTUzYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"OCTI智能评估系统\",\n    description: \"基于OCTI四维八极理论的智能组织评估平台\",\n    keywords: [\n        \"OCTI\",\n        \"组织评估\",\n        \"智能评估\",\n        \"四维八极\"\n    ],\n    authors: [\n        {\n            name: \"OCTI Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"OCTI智能评估系统\",\n        description: \"基于OCTI四维八极理论的智能组织评估平台\",\n        type: \"website\",\n        locale: \"zh_CN\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} h-full antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"root\",\n                className: \"min-h-full\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/questionnaire/page.tsx":
/*!****************************************!*\
  !*** ./src/app/questionnaire/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fquestionnaire%2Fpage&page=%2Fquestionnaire%2Fpage&appPaths=%2Fquestionnaire%2Fpage&pagePath=private-next-app-dir%2Fquestionnaire%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();