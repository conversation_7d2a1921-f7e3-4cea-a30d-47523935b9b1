"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/questionnaire/batch/route";
exports.ids = ["app/api/questionnaire/batch/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquestionnaire%2Fbatch%2Froute&page=%2Fapi%2Fquestionnaire%2Fbatch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquestionnaire%2Fbatch%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquestionnaire%2Fbatch%2Froute&page=%2Fapi%2Fquestionnaire%2Fbatch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquestionnaire%2Fbatch%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_questionnaire_batch_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/questionnaire/batch/route.ts */ \"(rsc)/./src/app/api/questionnaire/batch/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/questionnaire/batch/route\",\n        pathname: \"/api/questionnaire/batch\",\n        filename: \"route\",\n        bundlePath: \"app/api/questionnaire/batch/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/questionnaire/batch/route.ts\",\n    nextConfigOutput,\n    userland: _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_questionnaire_batch_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/questionnaire/batch/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquestionnaire%2Fbatch%2Froute&page=%2Fapi%2Fquestionnaire%2Fbatch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquestionnaire%2Fbatch%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/questionnaire/batch/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/questionnaire/batch/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n/* harmony import */ var _services_llm_llm_api_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/llm/llm-api-client */ \"(rsc)/./src/services/llm/llm-api-client.ts\");\n/* harmony import */ var _services_agents_QuestionDesignerAgent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/agents/QuestionDesignerAgent */ \"(rsc)/./src/services/agents/QuestionDesignerAgent.ts\");\n/* harmony import */ var _services_llm_prompt_builder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/llm/prompt-builder */ \"(rsc)/./src/services/llm/prompt-builder.ts\");\n/* harmony import */ var _services_data_data_fusion_engine__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/data/data-fusion-engine */ \"(rsc)/./src/services/data/data-fusion-engine.ts\");\n\n\n\n\n\n\n/**\n * 分批问卷生成请求验证Schema\n */ const BatchQuestionnaireSchema = zod__WEBPACK_IMPORTED_MODULE_5__.object({\n    organizationType: zod__WEBPACK_IMPORTED_MODULE_5__.string().min(1, \"组织类型不能为空\"),\n    version: zod__WEBPACK_IMPORTED_MODULE_5__[\"enum\"]([\n        \"standard\",\n        \"professional\"\n    ], {\n        errorMap: ()=>({\n                message: \"版本类型必须是 standard 或 professional\"\n            })\n    }).optional(),\n    batchSize: zod__WEBPACK_IMPORTED_MODULE_5__.number().min(1).max(15).optional().default(5),\n    dimension: zod__WEBPACK_IMPORTED_MODULE_5__[\"enum\"]([\n        \"S/F\",\n        \"I/T\",\n        \"M/V\",\n        \"A/D\"\n    ]).optional(),\n    startIndex: zod__WEBPACK_IMPORTED_MODULE_5__.number().min(1).optional().default(1)\n});\n/**\n * POST /api/questionnaire/batch\n * 分批生成问卷题目\n */ async function POST(request) {\n    try {\n        const body = await request.json();\n        console.log(\"收到分批问卷生成请求:\", body);\n        const validatedData = BatchQuestionnaireSchema.parse(body);\n        const { organizationType, version = \"standard\", batchSize, dimension, startIndex } = validatedData;\n        // 初始化服务\n        const llmClient = new _services_llm_llm_api_client__WEBPACK_IMPORTED_MODULE_1__.LLMApiClient();\n        const promptBuilder = new _services_llm_prompt_builder__WEBPACK_IMPORTED_MODULE_3__.PromptBuilder();\n        const dataFusionEngine = new _services_data_data_fusion_engine__WEBPACK_IMPORTED_MODULE_4__.DataFusionEngine();\n        const questionDesigner = new _services_agents_QuestionDesignerAgent__WEBPACK_IMPORTED_MODULE_2__.QuestionDesignerAgent(llmClient, promptBuilder, dataFusionEngine);\n        if (dimension) {\n            // 生成特定维度的题目批次\n            console.log(`生成维度 ${dimension} 的 ${batchSize} 道题目`);\n            const questions = await questionDesigner.generateQuestionBatch(dimension, batchSize, startIndex, {\n                version,\n                organizationType,\n                targetAudience: \"general\",\n                customRequirements: \"\",\n                questionCount: batchSize,\n                dimensions: [\n                    dimension\n                ]\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                data: {\n                    dimension,\n                    questions,\n                    batchSize: questions.length,\n                    startIndex,\n                    nextIndex: startIndex + questions.length\n                }\n            });\n        } else {\n            // 生成完整问卷的第一批题目\n            console.log(`开始生成完整问卷的第一批题目`);\n            const designOptions = {\n                version,\n                organizationType,\n                targetAudience: \"general\",\n                customRequirements: \"\",\n                questionCount: 60,\n                dimensions: [\n                    \"S/F\",\n                    \"I/T\",\n                    \"M/V\",\n                    \"A/D\"\n                ]\n            };\n            // 生成第一个维度的第一批题目\n            const firstBatch = await questionDesigner.generateQuestionBatch(\"S/F\", batchSize, 1, designOptions);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                data: {\n                    questionnaireId: `questionnaire_${Date.now()}`,\n                    dimension: \"S/F\",\n                    questions: firstBatch,\n                    batchSize: firstBatch.length,\n                    totalExpected: 60,\n                    progress: {\n                        completed: firstBatch.length,\n                        total: 60,\n                        percentage: Math.round(firstBatch.length / 60 * 100)\n                    },\n                    nextBatch: {\n                        dimension: \"S/F\",\n                        startIndex: 1 + firstBatch.length\n                    }\n                }\n            });\n        }\n    } catch (error) {\n        console.error(\"分批问卷生成失败:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_6__.ZodError) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"请求参数验证失败\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error instanceof Error ? error.message : \"未知错误\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * GET /api/questionnaire/batch?questionnaireId=xxx&dimension=xxx&startIndex=xxx\n * 获取问卷的下一批题目\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const questionnaireId = searchParams.get(\"questionnaireId\");\n        const dimension = searchParams.get(\"dimension\");\n        const startIndex = parseInt(searchParams.get(\"startIndex\") || \"1\");\n        const batchSize = parseInt(searchParams.get(\"batchSize\") || \"5\");\n        if (!questionnaireId || !dimension) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"缺少必要参数: questionnaireId 和 dimension\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(`获取问卷 ${questionnaireId} 维度 ${dimension} 从 ${startIndex} 开始的 ${batchSize} 道题目`);\n        // 这里可以从缓存中获取已生成的题目，或者生成新的题目\n        // 为了演示，我们生成新的题目\n        const llmClient = new _services_llm_llm_api_client__WEBPACK_IMPORTED_MODULE_1__.LLMApiClient();\n        const promptBuilder = new _services_llm_prompt_builder__WEBPACK_IMPORTED_MODULE_3__.PromptBuilder();\n        const dataFusionEngine = new _services_data_data_fusion_engine__WEBPACK_IMPORTED_MODULE_4__.DataFusionEngine();\n        const questionDesigner = new _services_agents_QuestionDesignerAgent__WEBPACK_IMPORTED_MODULE_2__.QuestionDesignerAgent(llmClient, promptBuilder, dataFusionEngine);\n        const questions = await questionDesigner.generateQuestionBatch(dimension, batchSize, startIndex, {\n            version: \"standard\",\n            organizationType: \"technology\",\n            targetAudience: \"general\",\n            customRequirements: \"\",\n            questionCount: batchSize,\n            dimensions: [\n                dimension\n            ]\n        });\n        // 计算下一批的信息\n        const dimensionOrder = [\n            \"S/F\",\n            \"I/T\",\n            \"M/V\",\n            \"A/D\"\n        ];\n        const currentDimensionIndex = dimensionOrder.indexOf(dimension);\n        const questionsPerDimension = 15;\n        const nextIndexInDimension = startIndex + batchSize;\n        let nextBatch = null;\n        if (nextIndexInDimension <= questionsPerDimension) {\n            // 当前维度还有题目\n            nextBatch = {\n                dimension,\n                startIndex: nextIndexInDimension\n            };\n        } else if (currentDimensionIndex < dimensionOrder.length - 1) {\n            // 切换到下一个维度\n            nextBatch = {\n                dimension: dimensionOrder[currentDimensionIndex + 1],\n                startIndex: 1\n            };\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: {\n                questionnaireId,\n                dimension,\n                questions,\n                batchSize: questions.length,\n                startIndex,\n                nextBatch,\n                progress: {\n                    completed: currentDimensionIndex * questionsPerDimension + Math.min(startIndex + batchSize - 1, questionsPerDimension),\n                    total: 60,\n                    percentage: Math.round((currentDimensionIndex * questionsPerDimension + Math.min(startIndex + batchSize - 1, questionsPerDimension)) / 60 * 100)\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"获取分批题目失败:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error instanceof Error ? error.message : \"未知错误\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/questionnaire/batch/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Logger: () => (/* binding */ Logger),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/**\n * 日志服务\n */ class Logger {\n    constructor(context = \"App\"){\n        this.context = context;\n    }\n    formatMessage(level, message, meta) {\n        const timestamp = new Date().toISOString();\n        const metaStr = meta ? ` ${JSON.stringify(meta)}` : \"\";\n        return `[${timestamp}] [${level.toUpperCase()}] [${this.context}] ${message}${metaStr}`;\n    }\n    debug(message, meta) {\n        if (process.env.LOG_LEVEL === \"debug\") {\n            console.debug(this.formatMessage(\"debug\", message, meta));\n        }\n    }\n    info(message, meta) {\n        console.info(this.formatMessage(\"info\", message, meta));\n    }\n    warn(message, meta) {\n        console.warn(this.formatMessage(\"warn\", message, meta));\n    }\n    error(message, meta) {\n        console.error(this.formatMessage(\"error\", message, meta));\n    }\n    setContext(context) {\n        this.context = context;\n    }\n}\n// 创建默认日志器实例\nconst logger = new Logger(\"OCTI\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/agents/QuestionDesignerAgent.ts":
/*!******************************************************!*\
  !*** ./src/services/agents/QuestionDesignerAgent.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionDesignerAgent: () => (/* binding */ QuestionDesignerAgent)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n\nconst logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"QuestionDesignerAgent\");\n/**\n * 问卷设计师智能体\n */ class QuestionDesignerAgent {\n    constructor(llmClient, promptBuilder, dataFusionEngine){\n        this.llmClient = llmClient;\n        this.promptBuilder = promptBuilder;\n        this.dataFusionEngine = dataFusionEngine;\n        this.name = \"question_designer\";\n        this.initialized = false;\n        this.lastActivity = new Date();\n        this.questionCache = new Map();\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"QuestionDesignerAgent\");\n    }\n    /**\n   * 初始化智能体\n   */ async initialize() {\n        this.logger.info(\"问卷设计师智能体初始化完成\");\n        this.initialized = true;\n        this.lastActivity = new Date();\n    }\n    /**\n   * 获取智能体状态\n   */ getStatus() {\n        return {\n            name: this.name,\n            initialized: this.initialized,\n            lastActivity: this.lastActivity,\n            config: {\n                llmProvider: \"minimax\",\n                version: \"1.0.0\"\n            }\n        };\n    }\n    /**\n   * 执行智能体任务\n   */ async execute(input) {\n        this.logger.info(\"执行问卷设计任务\", {\n            input\n        });\n        this.lastActivity = new Date();\n        try {\n            // 这里应该调用现有的 designQuestionnaire 方法\n            // 为了保持兼容性，我们将输入转换为 DesignOptions 格式\n            const designOptions = {\n                version: input.version || \"standard\",\n                organizationType: input.organizationType || \"公益组织\",\n                targetAudience: input.targetAudience || \"组织成员\",\n                customRequirements: input.customRequirements || \"\",\n                questionCount: input.questionCount || 20,\n                dimensions: input.dimensions || [\n                    \"战略规划\",\n                    \"团队协作\",\n                    \"资源管理\",\n                    \"影响力\"\n                ]\n            };\n            const questionnaire = await this.design(designOptions);\n            return {\n                success: true,\n                data: questionnaire\n            };\n        } catch (error) {\n            this.logger.error(\"问卷设计失败\", {\n                error\n            });\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"未知错误\"\n            };\n        }\n    }\n    /**\n   * 设计问卷\n   */ async design(options) {\n        const startTime = Date.now();\n        try {\n            logger.info(\"开始分批设计问卷\", options);\n            // 使用分批生成策略\n            const questionnaire = await this.generateQuestionnaireBatched(options);\n            const generationTime = ((Date.now() - startTime) / 1000).toFixed(1) + \"s\";\n            // 添加元数据\n            questionnaire.metadata = {\n                generationTime,\n                model: \"deepseek-chat\",\n                provider: \"deepseek\"\n            };\n            logger.info(\"问卷设计完成\", {\n                questionCount: questionnaire.questions.length,\n                generationTime\n            });\n            return questionnaire;\n        } catch (error) {\n            logger.error(\"问卷设计失败\", {\n                error,\n                options\n            });\n            throw error;\n        }\n    }\n    /**\n   * 构建LLM提示词\n   */ buildPrompt(options) {\n        const systemPrompt = `你是OCTI（Organization Capability Type Indicator）智能评估系统的问卷设计专家。\n\nOCTI评估框架包含四个核心维度：\n1. S/F (Structure/Flexibility) - 结构化与灵活性\n2. I/T (Innovation/Tradition) - 创新性与传统性  \n3. M/V (Management/Vision) - 管理导向与愿景导向\n4. A/D (Action/Decision) - 行动导向与决策导向\n\n请严格按照以下要求设计问卷：\n- 每个维度15道题，总共60道题\n- 每道题5个选项，分值1-5分\n- 题目要针对${options.organizationType}类型组织\n- ${options.version === \"professional\" ? \"专业版需要更深入的情境化问题\" : \"标准版使用通用性问题\"}\n- 确保题目的专业性和实用性\n\n重要：\n1. 必须返回完整的JSON格式，不要截断\n2. 使用紧凑的JSON格式，减少不必要的空格\n3. 确保所有60道题目都包含在响应中`;\n        const userPrompt = `请为${options.organizationType}类型组织设计一套${options.version}版OCTI评估问卷。\n\n具体要求：\n- 组织类型：${options.organizationType}\n- 目标受众：${options.targetAudience}\n- 版本：${options.version}\n${options.customRequirements ? `- 特殊要求：${options.customRequirements}` : \"\"}\n\n请严格按照以下JSON格式返回完整的60道题目：\n\n\\`\\`\\`json\n{\n  \"id\": \"questionnaire_${Date.now()}\",\n  \"title\": \"问卷标题\",\n  \"description\": \"问卷描述\",\n  \"version\": \"${options.version}\",\n  \"organizationType\": \"${options.organizationType}\",\n  \"assessmentType\": \"${options.version === \"professional\" ? \"professional\" : \"basic\"}\",\n  \"dimensions\": [\"S/F\", \"I/T\", \"M/V\", \"A/D\"],\n  \"questions\": [\n    {\n      \"id\": \"q_1\",\n      \"dimension\": \"S/F\",\n      \"type\": \"single_choice\",\n      \"title\": \"问题标题\",\n      \"description\": \"问题描述\",\n      \"options\": [\n        {\"id\": \"o_1\", \"text\": \"完全不同意\", \"score\": 1},\n        {\"id\": \"o_2\", \"text\": \"不同意\", \"score\": 2},\n        {\"id\": \"o_3\", \"text\": \"中立\", \"score\": 3},\n        {\"id\": \"o_4\", \"text\": \"同意\", \"score\": 4},\n        {\"id\": \"o_5\", \"text\": \"完全同意\", \"score\": 5}\n      ],\n      \"required\": true,\n      \"order\": 1\n    }\n  ]\n}\n\\`\\`\\`\n\n注意：\n1. 必须包含完整的60道题目\n2. 每个维度15道题\n3. 确保JSON格式完整，不要截断\n4. 所有字段都必须填写完整`;\n        return {\n            systemPrompt,\n            userPrompt\n        };\n    }\n    /**\n   * 解析LLM响应\n   */ parseResponse(content, options) {\n        try {\n            console.log(\"原始LLM响应长度:\", content.length);\n            console.log(\"原始LLM响应前500字符:\", content.substring(0, 500));\n            // 检查响应是否被截断\n            if (content.length < 1000) {\n                console.warn(\"LLM响应长度过短，可能被截断:\", content.length);\n            }\n            // 尝试多种JSON提取方式\n            let jsonStr = \"\";\n            // 方式1: 提取```json代码块\n            const jsonBlockMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonBlockMatch) {\n                jsonStr = jsonBlockMatch[1].trim();\n                console.log(\"提取到JSON代码块，长度:\", jsonStr.length);\n            }\n            // 方式2: 提取完整的JSON对象\n            if (!jsonStr) {\n                const jsonObjectMatch = content.match(/\\{[\\s\\S]*\\}/);\n                if (jsonObjectMatch) {\n                    jsonStr = jsonObjectMatch[0].trim();\n                    console.log(\"提取到JSON对象，长度:\", jsonStr.length);\n                }\n            }\n            // 方式3: 查找最后一个完整的}\n            if (!jsonStr) {\n                const startIndex = content.indexOf(\"{\");\n                if (startIndex !== -1) {\n                    let braceCount = 0;\n                    let endIndex = -1;\n                    for(let i = startIndex; i < content.length; i++){\n                        if (content[i] === \"{\") braceCount++;\n                        if (content[i] === \"}\") {\n                            braceCount--;\n                            if (braceCount === 0) {\n                                endIndex = i;\n                                break;\n                            }\n                        }\n                    }\n                    if (endIndex !== -1) {\n                        jsonStr = content.substring(startIndex, endIndex + 1);\n                        console.log(\"通过括号匹配提取JSON，长度:\", jsonStr.length);\n                    }\n                }\n            }\n            if (jsonStr) {\n                try {\n                    const parsedContent = JSON.parse(jsonStr);\n                    console.log(\"JSON解析成功，问题数量:\", parsedContent.questions?.length || 0);\n                    return this.validateAndFormatQuestionnaire(parsedContent, options);\n                } catch (parseError) {\n                    console.error(\"JSON解析失败:\", parseError);\n                    console.log(\"尝试修复JSON...\");\n                    // 尝试修复常见的JSON问题\n                    const fixedJson = this.fixJsonString(jsonStr);\n                    if (fixedJson) {\n                        try {\n                            const parsedContent = JSON.parse(fixedJson);\n                            console.log(\"修复后JSON解析成功，问题数量:\", parsedContent.questions?.length || 0);\n                            return this.validateAndFormatQuestionnaire(parsedContent, options);\n                        } catch (fixError) {\n                            console.error(\"修复后仍然解析失败:\", fixError);\n                        }\n                    }\n                }\n            }\n            // 如果JSON解析都失败，使用结构化解析\n            console.log(\"JSON解析失败，使用结构化解析\");\n            return this.parseStructuredResponse(content, options);\n        } catch (error) {\n            console.error(\"响应解析完全失败:\", error);\n            logger.error(\"LLM响应解析失败\", {\n                error: error instanceof Error ? error.message : String(error),\n                contentPreview: content.substring(0, 500)\n            });\n            throw new Error(`问卷解析失败: ${error instanceof Error ? error.message : \"未知错误\"}`);\n        }\n    }\n    /**\n   * 尝试修复JSON字符串\n   */ fixJsonString(jsonStr) {\n        try {\n            // 移除可能的控制字符\n            let fixed = jsonStr.replace(/[\\x00-\\x1F\\x7F]/g, \"\");\n            // 修复常见的引号问题\n            fixed = fixed.replace(/'/g, '\"');\n            // 修复末尾缺少的括号或引号\n            if (!fixed.endsWith(\"}\")) {\n                // 尝试找到最后一个完整的对象\n                const lastCompleteObject = fixed.lastIndexOf('\"}');\n                if (lastCompleteObject !== -1) {\n                    fixed = fixed.substring(0, lastCompleteObject + 2) + \"}\";\n                }\n            }\n            // 验证修复后的JSON\n            JSON.parse(fixed);\n            return fixed;\n        } catch (error) {\n            console.error(\"JSON修复失败:\", error);\n            return null;\n        }\n    }\n    /**\n   * 验证和格式化解析后的问卷\n   */ validateAndFormatQuestionnaire(parsedContent, options) {\n        const errors = [];\n        if (!parsedContent.id) {\n            errors.push(\"问卷ID不能为空\");\n        }\n        if (!parsedContent.title) {\n            errors.push(\"问卷标题不能为空\");\n        }\n        if (!parsedContent.description) {\n            errors.push(\"问卷描述不能为空\");\n        }\n        if (!parsedContent.version) {\n            errors.push(\"问卷版本不能为空\");\n        }\n        if (!parsedContent.organizationType) {\n            errors.push(\"组织类型不能为空\");\n        }\n        if (!parsedContent.assessmentType) {\n            errors.push(\"评估类型不能为空\");\n        }\n        if (!parsedContent.dimensions || !Array.isArray(parsedContent.dimensions) || parsedContent.dimensions.length !== 4) {\n            errors.push(\"问卷必须包含四个维度\");\n        }\n        if (!parsedContent.questions || !Array.isArray(parsedContent.questions) || parsedContent.questions.length !== 60) {\n            errors.push(\"问卷必须包含60道题\");\n        }\n        if (errors.length > 0) {\n            throw new Error(`问卷解析失败：${errors.join(\", \")}`);\n        }\n        return {\n            id: parsedContent.id,\n            title: parsedContent.title,\n            description: parsedContent.description,\n            version: parsedContent.version,\n            organizationType: parsedContent.organizationType,\n            assessmentType: parsedContent.assessmentType,\n            dimensions: parsedContent.dimensions,\n            questions: parsedContent.questions.map((q, index)=>({\n                    id: q.id || `q_${index + 1}`,\n                    dimension: q.dimension,\n                    type: q.type,\n                    title: q.title,\n                    description: q.description || \"\",\n                    options: q.options.map((o, optionIndex)=>({\n                            id: o.id || `o_${index + 1}_${optionIndex + 1}`,\n                            text: o.text,\n                            score: o.score\n                        })),\n                    required: q.required || true,\n                    order: q.order || index + 1\n                })),\n            totalQuestions: parsedContent.questions.length,\n            estimatedTime: this.calculateEstimatedTime(parsedContent.questions),\n            createdAt: new Date().toISOString(),\n            metadata: {\n                generationTime: \"\",\n                model: \"\",\n                provider: \"\"\n            }\n        };\n    }\n    /**\n   * 改进的结构化解析\n   */ parseStructuredResponse(content, options) {\n        console.log(\"开始结构化解析，内容长度:\", content.length);\n        const result = {\n            id: `questionnaire_${Date.now()}`,\n            title: `${options.organizationType}组织OCTI评估问卷`,\n            description: `针对${options.organizationType}类型组织的OCTI能力评估问卷`,\n            version: options.version,\n            organizationType: options.organizationType,\n            assessmentType: options.version === \"professional\" ? \"professional\" : \"basic\",\n            dimensions: options.dimensions,\n            questions: [],\n            totalQuestions: 0,\n            estimatedTime: \"\",\n            createdAt: new Date().toISOString(),\n            metadata: {\n                generationTime: \"\",\n                model: \"\",\n                provider: \"\"\n            }\n        };\n        // 如果结构化解析也失败，生成默认问卷\n        if (result.questions.length === 0) {\n            console.log(\"结构化解析失败，生成默认问卷\");\n            result.questions = this.generateDefaultQuestions(options);\n        }\n        result.totalQuestions = result.questions.length;\n        result.estimatedTime = this.calculateEstimatedTime(result.questions);\n        console.log(\"结构化解析完成，问题数量:\", result.questions.length);\n        return result;\n    }\n    extractValue(line) {\n        const colonIndex = line.indexOf(\":\");\n        return colonIndex > -1 ? line.substring(colonIndex + 1).trim() : line;\n    }\n    // 解析问题列表\n    parseQuestions(rawQuestions) {\n        return rawQuestions.map((rawQ, index)=>{\n            const question = {\n                id: rawQ.id || `q_${index + 1}`,\n                dimension: rawQ.dimension || this.inferDimension(rawQ.text),\n                subdimension: rawQ.subdimension || \"\",\n                type: rawQ.type || this.inferQuestionType(rawQ.text),\n                depth: rawQ.depth || \"intermediate\",\n                text: rawQ.text || rawQ.question || \"\",\n                options: rawQ.options,\n                scale: rawQ.scale,\n                reversed: rawQ.reversed || false,\n                weight: rawQ.weight || 1,\n                required: rawQ.required !== false,\n                order: rawQ.order || index + 1,\n                metadata: {\n                    difficulty: rawQ.difficulty || 0.5,\n                    discriminationIndex: rawQ.discriminationIndex || 0.5,\n                    expectedResponseTime: rawQ.expectedResponseTime || 30\n                }\n            };\n            // 为选择题生成默认选项\n            if ((question.type === \"single_choice\" || question.type === \"multiple_choice\") && !question.options) {\n                question.options = this.generateDefaultOptions(question.type);\n            }\n            // 为量表题生成默认量表\n            if (question.type === \"likert_scale\" && !question.scale) {\n                question.scale = {\n                    min: 1,\n                    max: 5,\n                    labels: [\n                        \"完全不同意\",\n                        \"不同意\",\n                        \"中立\",\n                        \"同意\",\n                        \"完全同意\"\n                    ]\n                };\n            }\n            return question;\n        });\n    }\n    // 推断问题类型\n    inferQuestionType(text) {\n        if (text.includes(\"排序\") || text.includes(\"排列\")) {\n            return \"ranking\";\n        }\n        if (text.includes(\"多选\") || text.includes(\"可以选择多个\")) {\n            return \"multiple_choice\";\n        }\n        if (text.includes(\"同意\") || text.includes(\"程度\")) {\n            return \"likert_scale\";\n        }\n        if (text.includes(\"描述\") || text.includes(\"说明\") || text.includes(\"举例\")) {\n            return \"open_ended\";\n        }\n        return \"single_choice\";\n    }\n    // 推断维度\n    inferDimension(text) {\n        if (text.includes(\"权力\") || text.includes(\"等级\") || text.includes(\"层级\")) {\n            return \"权力距离\";\n        }\n        if (text.includes(\"个人\") || text.includes(\"集体\") || text.includes(\"团队\")) {\n            return \"个人主义vs集体主义\";\n        }\n        if (text.includes(\"竞争\") || text.includes(\"合作\") || text.includes(\"成就\")) {\n            return \"男性化vs女性化\";\n        }\n        if (text.includes(\"不确定\") || text.includes(\"风险\") || text.includes(\"变化\")) {\n            return \"不确定性规避\";\n        }\n        return \"综合\";\n    }\n    // 生成默认选项\n    generateDefaultOptions(type) {\n        if (type === \"single_choice\" || type === \"multiple_choice\") {\n            return [\n                {\n                    id: \"opt_1\",\n                    text: \"完全不符合\",\n                    score: 1\n                },\n                {\n                    id: \"opt_2\",\n                    text: \"基本不符合\",\n                    score: 2\n                },\n                {\n                    id: \"opt_3\",\n                    text: \"部分符合\",\n                    score: 3\n                },\n                {\n                    id: \"opt_4\",\n                    text: \"基本符合\",\n                    score: 4\n                },\n                {\n                    id: \"opt_5\",\n                    text: \"完全符合\",\n                    score: 5\n                }\n            ];\n        }\n        return [];\n    }\n    // 验证问卷\n    async validateQuestionnaire(questionnaire) {\n        const errors = [];\n        const warnings = [];\n        const suggestions = [];\n        // 基本验证\n        if (!questionnaire.title) {\n            errors.push(\"问卷标题不能为空\");\n        }\n        if (questionnaire.questions.length === 0) {\n            errors.push(\"问卷必须包含至少一个问题\");\n        }\n        // 问题数量验证\n        const expectedQuestions = questionnaire.version === \"professional\" ? 40 : 20;\n        if (questionnaire.questions.length < expectedQuestions * 0.8) {\n            warnings.push(`问题数量偏少，建议至少${expectedQuestions}个问题`);\n        }\n        // 维度覆盖验证\n        const dimensions = new Set(questionnaire.questions.map((q)=>q.dimension));\n        if (dimensions.size < 4) {\n            errors.push(\"问卷应覆盖OCTI四个维度\");\n        }\n        // 问题类型分布验证\n        const typeDistribution = this.analyzeQuestionTypes(questionnaire.questions);\n        if (typeDistribution.likert_scale < 0.5) {\n            suggestions.push(\"建议增加更多量表题以提高测量精度\");\n        }\n        // 问题质量验证\n        for (const question of questionnaire.questions){\n            if (!question.text || question.text.length < 10) {\n                errors.push(`问题${question.id}内容过短`);\n            }\n            if (question.type === \"single_choice\" && (!question.options || question.options.length < 3)) {\n                errors.push(`问题${question.id}选项不足`);\n            }\n        }\n        // 计算质量分数\n        let qualityScore = 1.0;\n        qualityScore -= errors.length * 0.2;\n        qualityScore -= warnings.length * 0.1;\n        qualityScore = Math.max(0, qualityScore);\n        return {\n            isValid: errors.length === 0,\n            errors,\n            warnings,\n            suggestions,\n            score: qualityScore,\n            qualityScore\n        };\n    }\n    // 重新生成问卷\n    async regenerateQuestionnaire(options, suggestions) {\n        logger.info(\"Regenerating questionnaire with improvements\", {\n            suggestions\n        });\n        // 添加改进建议到自定义要求\n        const improvedOptions = {\n            ...options,\n            customRequirements: [\n                options.customRequirements || \"\",\n                \"请特别注意以下改进建议：\",\n                ...suggestions\n            ].filter(Boolean).join(\"\\n\")\n        };\n        // 递归调用，但限制重试次数\n        return this.design(improvedOptions);\n    }\n    // 分析问题类型分布\n    analyzeQuestionTypes(questions) {\n        const distribution = {\n            single_choice: 0,\n            multiple_choice: 0,\n            scale: 0,\n            open_ended: 0,\n            ranking: 0,\n            matrix: 0,\n            likert_scale: 0,\n            choice: 0,\n            scenario: 0\n        };\n        questions.forEach((q)=>{\n            distribution[q.type] = (distribution[q.type] || 0) + 1;\n        });\n        // 转换为比例\n        const total = questions.length;\n        Object.keys(distribution).forEach((key)=>{\n            distribution[key] = distribution[key] / total;\n        });\n        return distribution;\n    }\n    // 计算预估时间\n    calculateEstimatedTime(questions) {\n        const totalSeconds = questions.reduce((total, q)=>{\n            const baseTime = q.type === \"open_ended\" ? 60 : 30;\n            return total + (q.expectedResponseTime || baseTime);\n        }, 0);\n        const minutes = Math.ceil(totalSeconds / 60);\n        return `${minutes}分钟`;\n    }\n    // 计算难度\n    calculateDifficulty(questions) {\n        if (questions.length === 0) return 0.5;\n        const avgDifficulty = questions.reduce((sum, q)=>{\n            return sum + (q.difficulty || 0.5);\n        }, 0) / questions.length;\n        return avgDifficulty;\n    }\n    // 生成问卷ID\n    generateQuestionnaireId() {\n        return `questionnaire_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    // 生成缓存键\n    generateCacheKey(options) {\n        const keyParts = [\n            options.version,\n            options.organizationType || \"default\",\n            options.industryContext || \"default\",\n            options.targetAudience || \"default\",\n            options.customRequirements || \"default\"\n        ];\n        return keyParts.join(\"_\").replace(/[^a-zA-Z0-9_]/g, \"_\");\n    }\n    // 获取问卷预览\n    async getQuestionnairePreview(questionnaireId) {\n        // 从缓存中查找\n        for (const questionnaire of Array.from(this.questionCache.values())){\n            if (questionnaire.id === questionnaireId) {\n                return {\n                    title: questionnaire.title,\n                    description: questionnaire.description,\n                    questionCount: questionnaire.questions.length,\n                    estimatedTime: parseInt(questionnaire.estimatedTime) || 20,\n                    dimensions: Array.from(new Set(questionnaire.questions.map((q)=>q.dimension)))\n                };\n            }\n        }\n        return null;\n    }\n    // 清除缓存\n    clearCache() {\n        this.questionCache.clear();\n        logger.info(\"QuestionDesignerAgent cache cleared\");\n    }\n    // 获取统计信息\n    getStats() {\n        const questionnaires = Array.from(this.questionCache.values());\n        const versionDistribution = {};\n        questionnaires.forEach((q)=>{\n            versionDistribution[q.version] = (versionDistribution[q.version] || 0) + 1;\n        });\n        return {\n            cacheSize: this.questionCache.size,\n            totalQuestionnaires: questionnaires.length,\n            versionDistribution\n        };\n    }\n    /**\n   * 分批生成问卷 - 核心实现\n   */ async generateQuestionnaireBatched(options) {\n        const dimensions = [\n            \"S/F\",\n            \"I/T\",\n            \"M/V\",\n            \"A/D\"\n        ];\n        const questionsPerBatch = 5 // 每批生成5道题\n        ;\n        const questionsPerDimension = 15;\n        // 创建基础问卷结构\n        const questionnaire = {\n            id: `questionnaire_${Date.now()}`,\n            title: `OCTI智能评估 - ${options.organizationType}`,\n            description: `基于OCTI四维八极理论的${options.organizationType}组织能力评估`,\n            version: options.version,\n            organizationType: options.organizationType,\n            assessmentType: options.version === \"professional\" ? \"professional\" : \"basic\",\n            dimensions: dimensions,\n            questions: [],\n            totalQuestions: 60,\n            estimatedTime: \"20分钟\",\n            createdAt: new Date().toISOString()\n        };\n        // 为每个维度分批生成题目\n        for (const dimension of dimensions){\n            logger.info(`开始生成维度 ${dimension} 的题目`);\n            for(let batch = 0; batch < Math.ceil(questionsPerDimension / questionsPerBatch); batch++){\n                const startIndex = batch * questionsPerBatch;\n                const endIndex = Math.min(startIndex + questionsPerBatch, questionsPerDimension);\n                const questionsInBatch = endIndex - startIndex;\n                try {\n                    const batchQuestions = await this.generateQuestionBatch(dimension, questionsInBatch, startIndex + 1, options);\n                    questionnaire.questions.push(...batchQuestions);\n                    logger.info(`维度 ${dimension} 第 ${batch + 1} 批题目生成完成`, {\n                        batchSize: batchQuestions.length,\n                        totalGenerated: questionnaire.questions.length\n                    });\n                    // 缓存当前进度\n                    this.questionCache.set(questionnaire.id, questionnaire);\n                } catch (error) {\n                    logger.warn(`维度 ${dimension} 第 ${batch + 1} 批生成失败，使用默认题目`, {\n                        error\n                    });\n                    // 生成默认题目作为后备\n                    const defaultQuestions = this.generateDefaultQuestionsBatch(dimension, questionsInBatch, startIndex + 1);\n                    questionnaire.questions.push(...defaultQuestions);\n                }\n            }\n        }\n        // 更新最终统计\n        questionnaire.totalQuestions = questionnaire.questions.length;\n        questionnaire.estimatedTime = this.calculateEstimatedTime(questionnaire.questions);\n        return questionnaire;\n    }\n    /**\n   * 生成单批次题目\n   */ async generateQuestionBatch(dimension, questionCount, startIndex, options) {\n        const prompt = this.buildBatchPrompt(dimension, questionCount, startIndex, options);\n        const llmRequest = {\n            model: \"deepseek-chat\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: prompt.systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: prompt.userPrompt\n                }\n            ],\n            temperature: 0.7,\n            maxTokens: 3000 // 较小的token限制，因为只生成几道题\n        };\n        const provider = \"deepseek\";\n        const response = await this.llmClient.chat(provider, llmRequest);\n        return this.parseBatchResponse(response.content || \"\", dimension, startIndex);\n    }\n    /**\n   * 构建批次提示词\n   */ buildBatchPrompt(dimension, questionCount, startIndex, options) {\n        const dimensionDescriptions = {\n            \"S/F\": \"结构化与灵活性 - 评估组织在规范化管理与灵活应变之间的平衡\",\n            \"I/T\": \"创新性与传统性 - 评估组织在创新突破与传统稳定之间的取向\",\n            \"M/V\": \"管理导向与愿景导向 - 评估组织在日常管理与长远愿景之间的重点\",\n            \"A/D\": \"行动导向与决策导向 - 评估组织在快速行动与深度决策之间的偏好\"\n        };\n        const systemPrompt = `你是OCTI问卷设计专家，专门为${dimension}维度生成高质量的评估题目。\n\n${dimension}维度说明：${dimensionDescriptions[dimension]}\n\n请生成${questionCount}道针对${options.organizationType}类型组织的专业题目。`;\n        const userPrompt = `请为${dimension}维度生成${questionCount}道题目（编号从${startIndex}开始）。\n\n要求：\n- 每道题5个选项，分值1-5分\n- 题目要具体、实用、针对${options.organizationType}\n- ${options.version === \"professional\" ? \"使用深度情境化问题\" : \"使用通用性问题\"}\n\n请严格按照以下JSON格式返回：\n\n\\`\\`\\`json\n{\n  \"questions\": [\n    {\n      \"id\": \"q_${startIndex}\",\n      \"dimension\": \"${dimension}\",\n      \"type\": \"single_choice\",\n      \"text\": \"具体的问题文本\",\n      \"options\": [\n        {\"id\": \"o_${startIndex}_1\", \"text\": \"完全不符合\", \"score\": 1},\n        {\"id\": \"o_${startIndex}_2\", \"text\": \"基本不符合\", \"score\": 2},\n        {\"id\": \"o_${startIndex}_3\", \"text\": \"部分符合\", \"score\": 3},\n        {\"id\": \"o_${startIndex}_4\", \"text\": \"基本符合\", \"score\": 4},\n        {\"id\": \"o_${startIndex}_5\", \"text\": \"完全符合\", \"score\": 5}\n      ],\n      \"required\": true,\n      \"order\": ${startIndex}\n    }\n  ]\n}\n\\`\\`\\`\n\n确保返回完整的JSON格式。`;\n        return {\n            systemPrompt,\n            userPrompt\n        };\n    }\n    /**\n   * 解析批次响应\n   */ parseBatchResponse(content, dimension, startIndex) {\n        try {\n            console.log(\"批次响应内容长度:\", content.length);\n            console.log(\"批次响应前500字符:\", content.substring(0, 500));\n            // 提取JSON\n            let jsonStr = \"\";\n            const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonStr = jsonMatch[1].trim();\n            } else {\n                const objectMatch = content.match(/\\{[\\s\\S]*/);\n                if (objectMatch) {\n                    jsonStr = objectMatch[0].trim();\n                }\n            }\n            if (!jsonStr) {\n                throw new Error(\"未找到JSON格式\");\n            }\n            console.log(\"提取的JSON字符串长度:\", jsonStr.length);\n            // 尝试修复不完整的JSON\n            let fixedJson = this.fixBatchJson(jsonStr);\n            console.log(\"修复后的JSON:\", fixedJson.substring(0, 200) + \"...\");\n            const parsed = JSON.parse(fixedJson);\n            if (parsed.questions && Array.isArray(parsed.questions)) {\n                const questions = parsed.questions.map((q, index)=>({\n                        id: q.id || `q_${startIndex + index}`,\n                        dimension: q.dimension || dimension,\n                        type: q.type || \"single_choice\",\n                        text: q.text || q.title || `${dimension}维度问题 ${startIndex + index}`,\n                        options: q.options || this.generateDefaultOptions(\"single_choice\"),\n                        required: q.required !== false,\n                        order: q.order || startIndex + index\n                    }));\n                console.log(`成功解析 ${questions.length} 道题目`);\n                return questions;\n            }\n            throw new Error(\"解析的JSON中没有questions数组\");\n        } catch (error) {\n            console.error(\"批次解析失败:\", error);\n            logger.warn(`批次解析失败，使用默认题目`, {\n                error,\n                dimension,\n                startIndex\n            });\n            return this.generateDefaultQuestionsBatch(dimension, 1, startIndex);\n        }\n    }\n    /**\n   * 修复不完整的批次JSON\n   */ fixBatchJson(jsonStr) {\n        try {\n            // 如果JSON已经完整，直接返回\n            JSON.parse(jsonStr);\n            return jsonStr;\n        } catch (error) {\n            console.log(\"JSON不完整，尝试修复...\");\n            let fixed = jsonStr;\n            // 确保有questions数组开始\n            if (!fixed.includes('\"questions\"')) {\n                throw new Error(\"JSON中没有questions字段\");\n            }\n            // 修复可能缺少的结尾\n            if (!fixed.endsWith(\"}\")) {\n                // 找到最后一个完整的问题对象\n                const lastCompleteQuestion = fixed.lastIndexOf('\"order\"');\n                if (lastCompleteQuestion !== -1) {\n                    // 找到这个order字段的值结尾\n                    const afterOrder = fixed.substring(lastCompleteQuestion);\n                    const numberMatch = afterOrder.match(/\"order\":\\s*(\\d+)/);\n                    if (numberMatch) {\n                        const endPos = lastCompleteQuestion + afterOrder.indexOf(numberMatch[1]) + numberMatch[1].length;\n                        fixed = fixed.substring(0, endPos) + \"\\n     }\\n   ]\\n}\";\n                    }\n                }\n            }\n            // 验证修复结果\n            JSON.parse(fixed);\n            console.log(\"JSON修复成功\");\n            return fixed;\n        }\n    }\n    /**\n   * 生成默认题目批次\n   */ generateDefaultQuestionsBatch(dimension, questionCount, startIndex) {\n        const questions = [];\n        for(let i = 0; i < questionCount; i++){\n            const questionIndex = startIndex + i;\n            questions.push({\n                id: `q_${questionIndex}`,\n                dimension,\n                type: \"single_choice\",\n                text: `关于${dimension}维度的评估问题 ${questionIndex}`,\n                options: this.generateDefaultOptions(\"single_choice\"),\n                required: true,\n                order: questionIndex\n            });\n        }\n        return questions;\n    }\n    /**\n   * 生成默认问卷（当LLM解析失败时的备用方案）\n   */ generateDefaultQuestions(options) {\n        const questions = [];\n        const dimensionQuestions = {\n            \"S/F\": [\n                \"您的组织更倾向于遵循既定的流程和规范\",\n                \"在面对新情况时，您的组织能够快速调整策略\",\n                \"您的组织有明确的层级结构和职责分工\",\n                \"您的组织鼓励员工在工作中发挥创造性\",\n                \"您的组织重视标准化的工作流程\"\n            ],\n            \"I/T\": [\n                \"您的组织积极采用新技术和新方法\",\n                \"您的组织重视传统经验和成熟做法\",\n                \"您的组织鼓励员工提出创新想法\",\n                \"您的组织在决策时会充分考虑历史经验\",\n                \"您的组织愿意承担创新带来的风险\"\n            ],\n            \"M/V\": [\n                \"您的组织注重日常运营管理的效率\",\n                \"您的组织有清晰的长远发展愿景\",\n                \"您的组织重视绩效指标的达成\",\n                \"您的组织经常讨论未来发展方向\",\n                \"您的组织善于制定详细的执行计划\"\n            ],\n            \"A/D\": [\n                \"您的组织在决策时会进行充分的分析和讨论\",\n                \"您的组织能够快速响应市场变化\",\n                \"您的组织重视数据分析在决策中的作用\",\n                \"您的组织鼓励员工主动采取行动\",\n                \"您的组织在重要决策前会征求多方意见\"\n            ]\n        };\n        let questionId = 1;\n        options.dimensions.forEach((dimension)=>{\n            const dimensionTexts = dimensionQuestions[dimension] || [];\n            dimensionTexts.forEach((text)=>{\n                questions.push({\n                    id: `q_${questionId}`,\n                    dimension,\n                    type: \"single_choice\",\n                    text: text,\n                    options: [\n                        {\n                            id: `o_${questionId}_1`,\n                            text: \"完全不同意\",\n                            score: 1\n                        },\n                        {\n                            id: `o_${questionId}_2`,\n                            text: \"不同意\",\n                            score: 2\n                        },\n                        {\n                            id: `o_${questionId}_3`,\n                            text: \"中立\",\n                            score: 3\n                        },\n                        {\n                            id: `o_${questionId}_4`,\n                            text: \"同意\",\n                            score: 4\n                        },\n                        {\n                            id: `o_${questionId}_5`,\n                            text: \"完全同意\",\n                            score: 5\n                        }\n                    ],\n                    required: true,\n                    order: questionId\n                });\n                questionId++;\n            });\n        });\n        // 补充到60题\n        while(questions.length < 60){\n            const dimension = options.dimensions[questions.length % 4];\n            questions.push({\n                id: `q_${questions.length + 1}`,\n                dimension,\n                type: \"single_choice\",\n                text: `关于${dimension}维度的评估问题 ${questions.length + 1}`,\n                options: [\n                    {\n                        id: `o_${questions.length + 1}_1`,\n                        text: \"完全不同意\",\n                        score: 1\n                    },\n                    {\n                        id: `o_${questions.length + 1}_2`,\n                        text: \"不同意\",\n                        score: 2\n                    },\n                    {\n                        id: `o_${questions.length + 1}_3`,\n                        text: \"中立\",\n                        score: 3\n                    },\n                    {\n                        id: `o_${questions.length + 1}_4`,\n                        text: \"同意\",\n                        score: 4\n                    },\n                    {\n                        id: `o_${questions.length + 1}_5`,\n                        text: \"完全同意\",\n                        score: 5\n                    }\n                ],\n                required: true,\n                order: questions.length + 1\n            });\n        }\n        return questions;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmljZXMvYWdlbnRzL1F1ZXN0aW9uRGVzaWduZXJBZ2VudC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUNxQztBQUtyQyxNQUFNQyxTQUFTLElBQUlELCtDQUFNQSxDQUFDO0FBa0UxQjs7Q0FFQyxHQUNNLE1BQU1FO0lBT1hDLFlBQ0UsU0FBK0IsRUFDL0IsYUFBb0MsRUFDcEMsZ0JBQTBDLENBQzFDO2FBSFFDLFlBQUFBO2FBQ0FDLGdCQUFBQTthQUNBQyxtQkFBQUE7YUFUTUMsT0FBTzthQUVmQyxjQUFjO2FBQ2RDLGVBQWUsSUFBSUM7YUFDbkJDLGdCQUFnQixJQUFJQztRQU8xQixJQUFJLENBQUNYLE1BQU0sR0FBRyxJQUFJRCwrQ0FBTUEsQ0FBQztJQUMzQjtJQUVBOztHQUVDLEdBQ0QsTUFBTWEsYUFBNEI7UUFDaEMsSUFBSSxDQUFDWixNQUFNLENBQUNhLElBQUksQ0FBQztRQUNqQixJQUFJLENBQUNOLFdBQVcsR0FBRztRQUNuQixJQUFJLENBQUNDLFlBQVksR0FBRyxJQUFJQztJQUMxQjtJQUVBOztHQUVDLEdBQ0RLLFlBQXlCO1FBQ3ZCLE9BQU87WUFDTFIsTUFBTSxJQUFJLENBQUNBLElBQUk7WUFDZkMsYUFBYSxJQUFJLENBQUNBLFdBQVc7WUFDN0JDLGNBQWMsSUFBSSxDQUFDQSxZQUFZO1lBQy9CTyxRQUFRO2dCQUNOQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxRQUFRQyxLQUFpQixFQUF3QjtRQUNyRCxJQUFJLENBQUNuQixNQUFNLENBQUNhLElBQUksQ0FBQyxZQUFZO1lBQUVNO1FBQU07UUFDckMsSUFBSSxDQUFDWCxZQUFZLEdBQUcsSUFBSUM7UUFFeEIsSUFBSTtZQUNGLG1DQUFtQztZQUNuQyxvQ0FBb0M7WUFDcEMsTUFBTVcsZ0JBQStCO2dCQUNuQ0gsU0FBU0UsTUFBTUYsT0FBTyxJQUFJO2dCQUMxQkksa0JBQWtCRixNQUFNRSxnQkFBZ0IsSUFBSTtnQkFDNUNDLGdCQUFnQkgsTUFBTUcsY0FBYyxJQUFJO2dCQUN4Q0Msb0JBQW9CSixNQUFNSSxrQkFBa0IsSUFBSTtnQkFDaERDLGVBQWVMLE1BQU1LLGFBQWEsSUFBSTtnQkFDdENDLFlBQVlOLE1BQU1NLFVBQVUsSUFBSTtvQkFBQztvQkFBUTtvQkFBUTtvQkFBUTtpQkFBTTtZQUNqRTtZQUVBLE1BQU1DLGdCQUFnQixNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDUDtZQUV4QyxPQUFPO2dCQUNMUSxTQUFTO2dCQUNUQyxNQUFNSDtZQUNSO1FBQ0YsRUFBRSxPQUFPSSxPQUFPO1lBQ2QsSUFBSSxDQUFDOUIsTUFBTSxDQUFDOEIsS0FBSyxDQUFDLFVBQVU7Z0JBQUVBO1lBQU07WUFDcEMsT0FBTztnQkFDTEYsU0FBUztnQkFDVEUsT0FBT0EsaUJBQWlCQyxRQUFRRCxNQUFNRSxPQUFPLEdBQUc7WUFDbEQ7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNTCxPQUFPTSxPQUFzQixFQUEwQjtRQUMzRCxNQUFNQyxZQUFZekIsS0FBSzBCLEdBQUc7UUFFMUIsSUFBSTtZQUNGbkMsT0FBT2EsSUFBSSxDQUFDLFlBQVlvQjtZQUV4QixXQUFXO1lBQ1gsTUFBTVAsZ0JBQWdCLE1BQU0sSUFBSSxDQUFDVSw0QkFBNEIsQ0FBQ0g7WUFFOUQsTUFBTUksaUJBQWlCLENBQUMsQ0FBQzVCLEtBQUswQixHQUFHLEtBQUtELFNBQVEsSUFBSyxJQUFHLEVBQUdJLE9BQU8sQ0FBQyxLQUFLO1lBRXRFLFFBQVE7WUFDUlosY0FBY2EsUUFBUSxHQUFHO2dCQUN2QkY7Z0JBQ0FHLE9BQU87Z0JBQ1BDLFVBQVU7WUFDWjtZQUVBekMsT0FBT2EsSUFBSSxDQUFDLFVBQVU7Z0JBQ3BCVyxlQUFlRSxjQUFjZ0IsU0FBUyxDQUFDQyxNQUFNO2dCQUM3Q047WUFDRjtZQUVBLE9BQU9YO1FBRVQsRUFBRSxPQUFPSSxPQUFPO1lBQ2Q5QixPQUFPOEIsS0FBSyxDQUFDLFVBQVU7Z0JBQUVBO2dCQUFPRztZQUFRO1lBQ3hDLE1BQU1IO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsWUFBb0JHLE9BQXNCLEVBQUU7UUFDMUMsTUFBTVksZUFBZSxDQUFDOzs7Ozs7Ozs7OztPQVduQixFQUFFWixRQUFRWixnQkFBZ0IsQ0FBQztFQUNoQyxFQUFFWSxRQUFRaEIsT0FBTyxLQUFLLGlCQUFpQixtQkFBbUIsYUFBYTs7Ozs7O21CQU10RCxDQUFDO1FBRWhCLE1BQU02QixhQUFhLENBQUMsRUFBRSxFQUFFYixRQUFRWixnQkFBZ0IsQ0FBQyxRQUFRLEVBQUVZLFFBQVFoQixPQUFPLENBQUM7OztPQUd4RSxFQUFFZ0IsUUFBUVosZ0JBQWdCLENBQUM7T0FDM0IsRUFBRVksUUFBUVgsY0FBYyxDQUFDO0tBQzNCLEVBQUVXLFFBQVFoQixPQUFPLENBQUM7QUFDdkIsRUFBRWdCLFFBQVFWLGtCQUFrQixHQUFHLENBQUMsT0FBTyxFQUFFVSxRQUFRVixrQkFBa0IsQ0FBQyxDQUFDLEdBQUcsR0FBRzs7Ozs7O3VCQU1wRCxFQUFFZCxLQUFLMEIsR0FBRyxHQUFHOzs7Y0FHdEIsRUFBRUYsUUFBUWhCLE9BQU8sQ0FBQzt1QkFDVCxFQUFFZ0IsUUFBUVosZ0JBQWdCLENBQUM7cUJBQzdCLEVBQUVZLFFBQVFoQixPQUFPLEtBQUssaUJBQWlCLGlCQUFpQixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Y0EyQnZFLENBQUM7UUFFWCxPQUFPO1lBQ0w0QjtZQUNBQztRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGNBQXNCRSxPQUFlLEVBQUVmLE9BQXNCLEVBQWlCO1FBQzVFLElBQUk7WUFDRmdCLFFBQVFDLEdBQUcsQ0FBQyxjQUFjRixRQUFRTCxNQUFNO1lBQ3hDTSxRQUFRQyxHQUFHLENBQUMsa0JBQWtCRixRQUFRRyxTQUFTLENBQUMsR0FBRztZQUVuRCxZQUFZO1lBQ1osSUFBSUgsUUFBUUwsTUFBTSxHQUFHLE1BQU07Z0JBQ3pCTSxRQUFRRyxJQUFJLENBQUMsb0JBQW9CSixRQUFRTCxNQUFNO1lBQ2pEO1lBRUEsZUFBZTtZQUNmLElBQUlVLFVBQVU7WUFFZCxvQkFBb0I7WUFDcEIsTUFBTUMsaUJBQWlCTixRQUFRTyxLQUFLLENBQUM7WUFDckMsSUFBSUQsZ0JBQWdCO2dCQUNsQkQsVUFBVUMsY0FBYyxDQUFDLEVBQUUsQ0FBQ0UsSUFBSTtnQkFDaENQLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JHLFFBQVFWLE1BQU07WUFDOUM7WUFFQSxtQkFBbUI7WUFDbkIsSUFBSSxDQUFDVSxTQUFTO2dCQUNaLE1BQU1JLGtCQUFrQlQsUUFBUU8sS0FBSyxDQUFDO2dCQUN0QyxJQUFJRSxpQkFBaUI7b0JBQ25CSixVQUFVSSxlQUFlLENBQUMsRUFBRSxDQUFDRCxJQUFJO29CQUNqQ1AsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkcsUUFBUVYsTUFBTTtnQkFDN0M7WUFDRjtZQUVBLGtCQUFrQjtZQUNsQixJQUFJLENBQUNVLFNBQVM7Z0JBQ1osTUFBTUssYUFBYVYsUUFBUVcsT0FBTyxDQUFDO2dCQUNuQyxJQUFJRCxlQUFlLENBQUMsR0FBRztvQkFDckIsSUFBSUUsYUFBYTtvQkFDakIsSUFBSUMsV0FBVyxDQUFDO29CQUVoQixJQUFLLElBQUlDLElBQUlKLFlBQVlJLElBQUlkLFFBQVFMLE1BQU0sRUFBRW1CLElBQUs7d0JBQ2hELElBQUlkLE9BQU8sQ0FBQ2MsRUFBRSxLQUFLLEtBQUtGO3dCQUN4QixJQUFJWixPQUFPLENBQUNjLEVBQUUsS0FBSyxLQUFLOzRCQUN0QkY7NEJBQ0EsSUFBSUEsZUFBZSxHQUFHO2dDQUNwQkMsV0FBV0M7Z0NBQ1g7NEJBQ0Y7d0JBQ0Y7b0JBQ0Y7b0JBRUEsSUFBSUQsYUFBYSxDQUFDLEdBQUc7d0JBQ25CUixVQUFVTCxRQUFRRyxTQUFTLENBQUNPLFlBQVlHLFdBQVc7d0JBQ25EWixRQUFRQyxHQUFHLENBQUMsb0JBQW9CRyxRQUFRVixNQUFNO29CQUNoRDtnQkFDRjtZQUNGO1lBRUEsSUFBSVUsU0FBUztnQkFDWCxJQUFJO29CQUNGLE1BQU1VLGdCQUFnQkMsS0FBS0MsS0FBSyxDQUFDWjtvQkFDakNKLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JhLGNBQWNyQixTQUFTLEVBQUVDLFVBQVU7b0JBQ2pFLE9BQU8sSUFBSSxDQUFDdUIsOEJBQThCLENBQUNILGVBQWU5QjtnQkFDNUQsRUFBRSxPQUFPa0MsWUFBWTtvQkFDbkJsQixRQUFRbkIsS0FBSyxDQUFDLGFBQWFxQztvQkFDM0JsQixRQUFRQyxHQUFHLENBQUM7b0JBRVosZ0JBQWdCO29CQUNoQixNQUFNa0IsWUFBWSxJQUFJLENBQUNDLGFBQWEsQ0FBQ2hCO29CQUNyQyxJQUFJZSxXQUFXO3dCQUNiLElBQUk7NEJBQ0YsTUFBTUwsZ0JBQWdCQyxLQUFLQyxLQUFLLENBQUNHOzRCQUNqQ25CLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJhLGNBQWNyQixTQUFTLEVBQUVDLFVBQVU7NEJBQ3BFLE9BQU8sSUFBSSxDQUFDdUIsOEJBQThCLENBQUNILGVBQWU5Qjt3QkFDNUQsRUFBRSxPQUFPcUMsVUFBVTs0QkFDakJyQixRQUFRbkIsS0FBSyxDQUFDLGNBQWN3Qzt3QkFDOUI7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUVBLHNCQUFzQjtZQUN0QnJCLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU8sSUFBSSxDQUFDcUIsdUJBQXVCLENBQUN2QixTQUFTZjtRQUUvQyxFQUFFLE9BQU9ILE9BQU87WUFDZG1CLFFBQVFuQixLQUFLLENBQUMsYUFBYUE7WUFDM0I5QixPQUFPOEIsS0FBSyxDQUFDLGFBQWE7Z0JBQ3hCQSxPQUFPQSxpQkFBaUJDLFFBQVFELE1BQU1FLE9BQU8sR0FBR3dDLE9BQU8xQztnQkFDdkQyQyxnQkFBZ0J6QixRQUFRRyxTQUFTLENBQUMsR0FBRztZQUN2QztZQUNBLE1BQU0sSUFBSXBCLE1BQU0sQ0FBQyxRQUFRLEVBQUVELGlCQUFpQkMsUUFBUUQsTUFBTUUsT0FBTyxHQUFHLE9BQU8sQ0FBQztRQUM5RTtJQUNGO0lBRUE7O0dBRUMsR0FDRCxjQUFzQnFCLE9BQWUsRUFBaUI7UUFDcEQsSUFBSTtZQUNGLFlBQVk7WUFDWixJQUFJcUIsUUFBUXJCLFFBQVFzQixPQUFPLENBQUMsb0JBQW9CO1lBRWhELFlBQVk7WUFDWkQsUUFBUUEsTUFBTUMsT0FBTyxDQUFDLE1BQU07WUFFNUIsZUFBZTtZQUNmLElBQUksQ0FBQ0QsTUFBTUUsUUFBUSxDQUFDLE1BQU07Z0JBQ3hCLGdCQUFnQjtnQkFDaEIsTUFBTUMscUJBQXFCSCxNQUFNSSxXQUFXLENBQUM7Z0JBQzdDLElBQUlELHVCQUF1QixDQUFDLEdBQUc7b0JBQzdCSCxRQUFRQSxNQUFNdkIsU0FBUyxDQUFDLEdBQUcwQixxQkFBcUIsS0FBSztnQkFDdkQ7WUFDRjtZQUVBLGFBQWE7WUFDYmIsS0FBS0MsS0FBSyxDQUFDUztZQUNYLE9BQU9BO1FBQ1QsRUFBRSxPQUFPNUMsT0FBTztZQUNkbUIsUUFBUW5CLEtBQUssQ0FBQyxhQUFhQTtZQUMzQixPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsK0JBQXVDaUMsYUFBa0IsRUFBRTlCLE9BQXNCLEVBQWlCO1FBQ2hHLE1BQU04QyxTQUFtQixFQUFFO1FBRTNCLElBQUksQ0FBQ2hCLGNBQWNpQixFQUFFLEVBQUU7WUFDckJELE9BQU9FLElBQUksQ0FBQztRQUNkO1FBQ0EsSUFBSSxDQUFDbEIsY0FBY21CLEtBQUssRUFBRTtZQUN4QkgsT0FBT0UsSUFBSSxDQUFDO1FBQ2Q7UUFDQSxJQUFJLENBQUNsQixjQUFjb0IsV0FBVyxFQUFFO1lBQzlCSixPQUFPRSxJQUFJLENBQUM7UUFDZDtRQUNBLElBQUksQ0FBQ2xCLGNBQWM5QyxPQUFPLEVBQUU7WUFDMUI4RCxPQUFPRSxJQUFJLENBQUM7UUFDZDtRQUNBLElBQUksQ0FBQ2xCLGNBQWMxQyxnQkFBZ0IsRUFBRTtZQUNuQzBELE9BQU9FLElBQUksQ0FBQztRQUNkO1FBQ0EsSUFBSSxDQUFDbEIsY0FBY3FCLGNBQWMsRUFBRTtZQUNqQ0wsT0FBT0UsSUFBSSxDQUFDO1FBQ2Q7UUFDQSxJQUFJLENBQUNsQixjQUFjdEMsVUFBVSxJQUFJLENBQUM0RCxNQUFNQyxPQUFPLENBQUN2QixjQUFjdEMsVUFBVSxLQUFLc0MsY0FBY3RDLFVBQVUsQ0FBQ2tCLE1BQU0sS0FBSyxHQUFHO1lBQ2xIb0MsT0FBT0UsSUFBSSxDQUFDO1FBQ2Q7UUFDQSxJQUFJLENBQUNsQixjQUFjckIsU0FBUyxJQUFJLENBQUMyQyxNQUFNQyxPQUFPLENBQUN2QixjQUFjckIsU0FBUyxLQUFLcUIsY0FBY3JCLFNBQVMsQ0FBQ0MsTUFBTSxLQUFLLElBQUk7WUFDaEhvQyxPQUFPRSxJQUFJLENBQUM7UUFDZDtRQUVBLElBQUlGLE9BQU9wQyxNQUFNLEdBQUcsR0FBRztZQUNyQixNQUFNLElBQUlaLE1BQU0sQ0FBQyxPQUFPLEVBQUVnRCxPQUFPUSxJQUFJLENBQUMsTUFBTSxDQUFDO1FBQy9DO1FBRUEsT0FBTztZQUNMUCxJQUFJakIsY0FBY2lCLEVBQUU7WUFDcEJFLE9BQU9uQixjQUFjbUIsS0FBSztZQUMxQkMsYUFBYXBCLGNBQWNvQixXQUFXO1lBQ3RDbEUsU0FBUzhDLGNBQWM5QyxPQUFPO1lBQzlCSSxrQkFBa0IwQyxjQUFjMUMsZ0JBQWdCO1lBQ2hEK0QsZ0JBQWdCckIsY0FBY3FCLGNBQWM7WUFDNUMzRCxZQUFZc0MsY0FBY3RDLFVBQVU7WUFDcENpQixXQUFXcUIsY0FBY3JCLFNBQVMsQ0FBQzhDLEdBQUcsQ0FBQyxDQUFDQyxHQUFRQyxRQUFtQjtvQkFDakVWLElBQUlTLEVBQUVULEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRVUsUUFBUSxFQUFFLENBQUM7b0JBQzVCQyxXQUFXRixFQUFFRSxTQUFTO29CQUN0QkMsTUFBTUgsRUFBRUcsSUFBSTtvQkFDWlYsT0FBT08sRUFBRVAsS0FBSztvQkFDZEMsYUFBYU0sRUFBRU4sV0FBVyxJQUFJO29CQUM5QmxELFNBQVN3RCxFQUFFeEQsT0FBTyxDQUFDdUQsR0FBRyxDQUFDLENBQUNLLEdBQVFDLGNBQXlCOzRCQUN2RGQsSUFBSWEsRUFBRWIsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFVSxRQUFRLEVBQUUsQ0FBQyxFQUFFSSxjQUFjLEVBQUUsQ0FBQzs0QkFDL0NDLE1BQU1GLEVBQUVFLElBQUk7NEJBQ1pDLE9BQU9ILEVBQUVHLEtBQUs7d0JBQ2hCO29CQUNBQyxVQUFVUixFQUFFUSxRQUFRLElBQUk7b0JBQ3hCQyxPQUFPVCxFQUFFUyxLQUFLLElBQUlSLFFBQVE7Z0JBQzVCO1lBQ0FTLGdCQUFnQnBDLGNBQWNyQixTQUFTLENBQUNDLE1BQU07WUFDOUN5RCxlQUFlLElBQUksQ0FBQ0Msc0JBQXNCLENBQUN0QyxjQUFjckIsU0FBUztZQUNsRTRELFdBQVcsSUFBSTdGLE9BQU84RixXQUFXO1lBQ2pDaEUsVUFBVTtnQkFDUkYsZ0JBQWdCO2dCQUNoQkcsT0FBTztnQkFDUEMsVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0Qsd0JBQWdDTyxPQUFlLEVBQUVmLE9BQXNCLEVBQWlCO1FBQ3RGZ0IsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkYsUUFBUUwsTUFBTTtRQUUzQyxNQUFNNkQsU0FBd0I7WUFDNUJ4QixJQUFJLENBQUMsY0FBYyxFQUFFdkUsS0FBSzBCLEdBQUcsR0FBRyxDQUFDO1lBQ2pDK0MsT0FBTyxDQUFDLEVBQUVqRCxRQUFRWixnQkFBZ0IsQ0FBQyxVQUFVLENBQUM7WUFDOUM4RCxhQUFhLENBQUMsRUFBRSxFQUFFbEQsUUFBUVosZ0JBQWdCLENBQUMsZUFBZSxDQUFDO1lBQzNESixTQUFTZ0IsUUFBUWhCLE9BQU87WUFDeEJJLGtCQUFrQlksUUFBUVosZ0JBQWdCO1lBQzFDK0QsZ0JBQWdCbkQsUUFBUWhCLE9BQU8sS0FBSyxpQkFBaUIsaUJBQWlCO1lBQ3RFUSxZQUFZUSxRQUFRUixVQUFVO1lBQzlCaUIsV0FBVyxFQUFFO1lBQ2J5RCxnQkFBZ0I7WUFDaEJDLGVBQWU7WUFDZkUsV0FBVyxJQUFJN0YsT0FBTzhGLFdBQVc7WUFDakNoRSxVQUFVO2dCQUNSRixnQkFBZ0I7Z0JBQ2hCRyxPQUFPO2dCQUNQQyxVQUFVO1lBQ1o7UUFDRjtRQUVBLG9CQUFvQjtRQUNwQixJQUFJK0QsT0FBTzlELFNBQVMsQ0FBQ0MsTUFBTSxLQUFLLEdBQUc7WUFDakNNLFFBQVFDLEdBQUcsQ0FBQztZQUNac0QsT0FBTzlELFNBQVMsR0FBRyxJQUFJLENBQUMrRCx3QkFBd0IsQ0FBQ3hFO1FBQ25EO1FBRUF1RSxPQUFPTCxjQUFjLEdBQUdLLE9BQU85RCxTQUFTLENBQUNDLE1BQU07UUFDL0M2RCxPQUFPSixhQUFhLEdBQUcsSUFBSSxDQUFDQyxzQkFBc0IsQ0FBQ0csT0FBTzlELFNBQVM7UUFFbkVPLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJzRCxPQUFPOUQsU0FBUyxDQUFDQyxNQUFNO1FBQ3BELE9BQU82RDtJQUNUO0lBRVFFLGFBQWFDLElBQVksRUFBVTtRQUN6QyxNQUFNQyxhQUFhRCxLQUFLaEQsT0FBTyxDQUFDO1FBQ2hDLE9BQU9pRCxhQUFhLENBQUMsSUFBSUQsS0FBS3hELFNBQVMsQ0FBQ3lELGFBQWEsR0FBR3BELElBQUksS0FBS21EO0lBQ25FO0lBRUEsU0FBUztJQUNERSxlQUFlQyxZQUFtQixFQUFjO1FBQ3RELE9BQU9BLGFBQWF0QixHQUFHLENBQUMsQ0FBQ3VCLE1BQU1yQjtZQUM3QixNQUFNc0IsV0FBcUI7Z0JBQ3pCaEMsSUFBSStCLEtBQUsvQixFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUVVLFFBQVEsRUFBRSxDQUFDO2dCQUMvQkMsV0FBV29CLEtBQUtwQixTQUFTLElBQUksSUFBSSxDQUFDc0IsY0FBYyxDQUFDRixLQUFLaEIsSUFBSTtnQkFDMURtQixjQUFjSCxLQUFLRyxZQUFZLElBQUk7Z0JBQ25DdEIsTUFBTW1CLEtBQUtuQixJQUFJLElBQUksSUFBSSxDQUFDdUIsaUJBQWlCLENBQUNKLEtBQUtoQixJQUFJO2dCQUNuRHFCLE9BQU9MLEtBQUtLLEtBQUssSUFBSTtnQkFDckJyQixNQUFNZ0IsS0FBS2hCLElBQUksSUFBSWdCLEtBQUtDLFFBQVEsSUFBSTtnQkFDcEMvRSxTQUFTOEUsS0FBSzlFLE9BQU87Z0JBQ3JCb0YsT0FBT04sS0FBS00sS0FBSztnQkFDakJDLFVBQVVQLEtBQUtPLFFBQVEsSUFBSTtnQkFDM0JDLFFBQVFSLEtBQUtRLE1BQU0sSUFBSTtnQkFDdkJ0QixVQUFVYyxLQUFLZCxRQUFRLEtBQUs7Z0JBQzVCQyxPQUFPYSxLQUFLYixLQUFLLElBQUlSLFFBQVE7Z0JBQzdCbkQsVUFBVTtvQkFDUmlGLFlBQVlULEtBQUtTLFVBQVUsSUFBSTtvQkFDL0JDLHFCQUFxQlYsS0FBS1UsbUJBQW1CLElBQUk7b0JBQ2pEQyxzQkFBc0JYLEtBQUtXLG9CQUFvQixJQUFJO2dCQUNyRDtZQUNGO1lBRUEsYUFBYTtZQUNiLElBQUksQ0FBQ1YsU0FBU3BCLElBQUksS0FBSyxtQkFBbUJvQixTQUFTcEIsSUFBSSxLQUFLLGlCQUFnQixLQUFNLENBQUNvQixTQUFTL0UsT0FBTyxFQUFFO2dCQUNuRytFLFNBQVMvRSxPQUFPLEdBQUcsSUFBSSxDQUFDMEYsc0JBQXNCLENBQUNYLFNBQVNwQixJQUFJO1lBQzlEO1lBRUEsYUFBYTtZQUNiLElBQUlvQixTQUFTcEIsSUFBSSxLQUFLLGtCQUFrQixDQUFDb0IsU0FBU0ssS0FBSyxFQUFFO2dCQUN2REwsU0FBU0ssS0FBSyxHQUFHO29CQUNmTyxLQUFLO29CQUNMQyxLQUFLO29CQUNMQyxRQUFRO3dCQUFDO3dCQUFTO3dCQUFPO3dCQUFNO3dCQUFNO3FCQUFPO2dCQUM5QztZQUNGO1lBRUEsT0FBT2Q7UUFDVDtJQUNGO0lBRUEsU0FBUztJQUNERyxrQkFBa0JwQixJQUFZLEVBQWdCO1FBQ3BELElBQUlBLEtBQUtnQyxRQUFRLENBQUMsU0FBU2hDLEtBQUtnQyxRQUFRLENBQUMsT0FBTztZQUM5QyxPQUFPO1FBQ1Q7UUFDQSxJQUFJaEMsS0FBS2dDLFFBQVEsQ0FBQyxTQUFTaEMsS0FBS2dDLFFBQVEsQ0FBQyxXQUFXO1lBQ2xELE9BQU87UUFDVDtRQUNBLElBQUloQyxLQUFLZ0MsUUFBUSxDQUFDLFNBQVNoQyxLQUFLZ0MsUUFBUSxDQUFDLE9BQU87WUFDOUMsT0FBTztRQUNUO1FBQ0EsSUFBSWhDLEtBQUtnQyxRQUFRLENBQUMsU0FBU2hDLEtBQUtnQyxRQUFRLENBQUMsU0FBU2hDLEtBQUtnQyxRQUFRLENBQUMsT0FBTztZQUNyRSxPQUFPO1FBQ1Q7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxPQUFPO0lBQ0NkLGVBQWVsQixJQUFZLEVBQVU7UUFDM0MsSUFBSUEsS0FBS2dDLFFBQVEsQ0FBQyxTQUFTaEMsS0FBS2dDLFFBQVEsQ0FBQyxTQUFTaEMsS0FBS2dDLFFBQVEsQ0FBQyxPQUFPO1lBQ3JFLE9BQU87UUFDVDtRQUNBLElBQUloQyxLQUFLZ0MsUUFBUSxDQUFDLFNBQVNoQyxLQUFLZ0MsUUFBUSxDQUFDLFNBQVNoQyxLQUFLZ0MsUUFBUSxDQUFDLE9BQU87WUFDckUsT0FBTztRQUNUO1FBQ0EsSUFBSWhDLEtBQUtnQyxRQUFRLENBQUMsU0FBU2hDLEtBQUtnQyxRQUFRLENBQUMsU0FBU2hDLEtBQUtnQyxRQUFRLENBQUMsT0FBTztZQUNyRSxPQUFPO1FBQ1Q7UUFDQSxJQUFJaEMsS0FBS2dDLFFBQVEsQ0FBQyxVQUFVaEMsS0FBS2dDLFFBQVEsQ0FBQyxTQUFTaEMsS0FBS2dDLFFBQVEsQ0FBQyxPQUFPO1lBQ3RFLE9BQU87UUFDVDtRQUNBLE9BQU87SUFDVDtJQUVBLFNBQVM7SUFDREosdUJBQXVCL0IsSUFBa0IsRUFBb0I7UUFDbkUsSUFBSUEsU0FBUyxtQkFBbUJBLFNBQVMsbUJBQW1CO1lBQzFELE9BQU87Z0JBQ0w7b0JBQUVaLElBQUk7b0JBQVNlLE1BQU07b0JBQVNDLE9BQU87Z0JBQUU7Z0JBQ3ZDO29CQUFFaEIsSUFBSTtvQkFBU2UsTUFBTTtvQkFBU0MsT0FBTztnQkFBRTtnQkFDdkM7b0JBQUVoQixJQUFJO29CQUFTZSxNQUFNO29CQUFRQyxPQUFPO2dCQUFFO2dCQUN0QztvQkFBRWhCLElBQUk7b0JBQVNlLE1BQU07b0JBQVFDLE9BQU87Z0JBQUU7Z0JBQ3RDO29CQUFFaEIsSUFBSTtvQkFBU2UsTUFBTTtvQkFBUUMsT0FBTztnQkFBRTthQUN2QztRQUNIO1FBQ0EsT0FBTyxFQUFFO0lBQ1g7SUFFQSxPQUFPO0lBQ1AsTUFBY2dDLHNCQUFzQnRHLGFBQTRCLEVBQTZCO1FBQzNGLE1BQU1xRCxTQUFtQixFQUFFO1FBQzNCLE1BQU1rRCxXQUFxQixFQUFFO1FBQzdCLE1BQU1DLGNBQXdCLEVBQUU7UUFFaEMsT0FBTztRQUNQLElBQUksQ0FBQ3hHLGNBQWN3RCxLQUFLLEVBQUU7WUFDeEJILE9BQU9FLElBQUksQ0FBQztRQUNkO1FBRUEsSUFBSXZELGNBQWNnQixTQUFTLENBQUNDLE1BQU0sS0FBSyxHQUFHO1lBQ3hDb0MsT0FBT0UsSUFBSSxDQUFDO1FBQ2Q7UUFFQSxTQUFTO1FBQ1QsTUFBTWtELG9CQUFvQnpHLGNBQWNULE9BQU8sS0FBSyxpQkFBaUIsS0FBSztRQUMxRSxJQUFJUyxjQUFjZ0IsU0FBUyxDQUFDQyxNQUFNLEdBQUd3RixvQkFBb0IsS0FBSztZQUM1REYsU0FBU2hELElBQUksQ0FBQyxDQUFDLFdBQVcsRUFBRWtELGtCQUFrQixHQUFHLENBQUM7UUFDcEQ7UUFFQSxTQUFTO1FBQ1QsTUFBTTFHLGFBQWEsSUFBSTJHLElBQUkxRyxjQUFjZ0IsU0FBUyxDQUFDOEMsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFRSxTQUFTO1FBQ3ZFLElBQUlsRSxXQUFXNEcsSUFBSSxHQUFHLEdBQUc7WUFDdkJ0RCxPQUFPRSxJQUFJLENBQUM7UUFDZDtRQUVBLFdBQVc7UUFDWCxNQUFNcUQsbUJBQW1CLElBQUksQ0FBQ0Msb0JBQW9CLENBQUM3RyxjQUFjZ0IsU0FBUztRQUMxRSxJQUFJNEYsaUJBQWlCRSxZQUFZLEdBQUcsS0FBSztZQUN2Q04sWUFBWWpELElBQUksQ0FBQztRQUNuQjtRQUVBLFNBQVM7UUFDVCxLQUFLLE1BQU0rQixZQUFZdEYsY0FBY2dCLFNBQVMsQ0FBRTtZQUM5QyxJQUFJLENBQUNzRSxTQUFTakIsSUFBSSxJQUFJaUIsU0FBU2pCLElBQUksQ0FBQ3BELE1BQU0sR0FBRyxJQUFJO2dCQUMvQ29DLE9BQU9FLElBQUksQ0FBQyxDQUFDLEVBQUUsRUFBRStCLFNBQVNoQyxFQUFFLENBQUMsSUFBSSxDQUFDO1lBQ3BDO1lBRUEsSUFBSWdDLFNBQVNwQixJQUFJLEtBQUssbUJBQW9CLEVBQUNvQixTQUFTL0UsT0FBTyxJQUFJK0UsU0FBUy9FLE9BQU8sQ0FBQ1UsTUFBTSxHQUFHLElBQUk7Z0JBQzNGb0MsT0FBT0UsSUFBSSxDQUFDLENBQUMsRUFBRSxFQUFFK0IsU0FBU2hDLEVBQUUsQ0FBQyxJQUFJLENBQUM7WUFDcEM7UUFDRjtRQUVBLFNBQVM7UUFDVCxJQUFJeUQsZUFBZTtRQUNuQkEsZ0JBQWdCMUQsT0FBT3BDLE1BQU0sR0FBRztRQUNoQzhGLGdCQUFnQlIsU0FBU3RGLE1BQU0sR0FBRztRQUNsQzhGLGVBQWVDLEtBQUtiLEdBQUcsQ0FBQyxHQUFHWTtRQUUzQixPQUFPO1lBQ0xFLFNBQVM1RCxPQUFPcEMsTUFBTSxLQUFLO1lBQzNCb0M7WUFDQWtEO1lBQ0FDO1lBQ0FsQyxPQUFPeUM7WUFDUEE7UUFDRjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQWNHLHdCQUNaM0csT0FBc0IsRUFDdEJpRyxXQUFxQixFQUNHO1FBQ3hCbEksT0FBT2EsSUFBSSxDQUFDLGdEQUFnRDtZQUFFcUg7UUFBWTtRQUUxRSxlQUFlO1FBQ2YsTUFBTVcsa0JBQWtCO1lBQ3RCLEdBQUc1RyxPQUFPO1lBQ1ZWLG9CQUFvQjtnQkFDbEJVLFFBQVFWLGtCQUFrQixJQUFJO2dCQUM5QjttQkFDRzJHO2FBQ0osQ0FBQ1ksTUFBTSxDQUFDQyxTQUFTeEQsSUFBSSxDQUFDO1FBQ3pCO1FBRUEsZUFBZTtRQUNmLE9BQU8sSUFBSSxDQUFDNUQsTUFBTSxDQUFDa0g7SUFDckI7SUFFQSxXQUFXO0lBQ0hOLHFCQUFxQjdGLFNBQXFCLEVBQWdDO1FBQ2hGLE1BQU1zRyxlQUE2QztZQUNqREMsZUFBZTtZQUNmQyxpQkFBaUI7WUFDakI3QixPQUFPO1lBQ1A4QixZQUFZO1lBQ1pDLFNBQVM7WUFDVEMsUUFBUTtZQUNSYixjQUFjO1lBQ2RjLFFBQVE7WUFDUkMsVUFBVTtRQUNaO1FBRUE3RyxVQUFVOEcsT0FBTyxDQUFDL0QsQ0FBQUE7WUFDaEJ1RCxZQUFZLENBQUN2RCxFQUFFRyxJQUFJLENBQUMsR0FBRyxDQUFDb0QsWUFBWSxDQUFDdkQsRUFBRUcsSUFBSSxDQUFDLElBQUksS0FBSztRQUN2RDtRQUVBLFFBQVE7UUFDUixNQUFNNkQsUUFBUS9HLFVBQVVDLE1BQU07UUFDOUIrRyxPQUFPQyxJQUFJLENBQUNYLGNBQWNRLE9BQU8sQ0FBQ0ksQ0FBQUE7WUFDaENaLFlBQVksQ0FBQ1ksSUFBb0IsR0FBR1osWUFBWSxDQUFDWSxJQUFvQixHQUFHSDtRQUMxRTtRQUVBLE9BQU9UO0lBQ1Q7SUFFQSxTQUFTO0lBQ0QzQyx1QkFBdUIzRCxTQUFnQixFQUFVO1FBQ3ZELE1BQU1tSCxlQUFlbkgsVUFBVW9ILE1BQU0sQ0FBQyxDQUFDTCxPQUFPaEU7WUFDNUMsTUFBTXNFLFdBQVd0RSxFQUFFRyxJQUFJLEtBQUssZUFBZSxLQUFLO1lBQ2hELE9BQU82RCxRQUFTaEUsQ0FBQUEsRUFBRWlDLG9CQUFvQixJQUFJcUMsUUFBTztRQUNuRCxHQUFHO1FBRUgsTUFBTUMsVUFBVXRCLEtBQUt1QixJQUFJLENBQUNKLGVBQWU7UUFDekMsT0FBTyxDQUFDLEVBQUVHLFFBQVEsRUFBRSxDQUFDO0lBQ3ZCO0lBRUEsT0FBTztJQUNDRSxvQkFBb0J4SCxTQUFnQixFQUFVO1FBQ3BELElBQUlBLFVBQVVDLE1BQU0sS0FBSyxHQUFHLE9BQU87UUFFbkMsTUFBTXdILGdCQUFnQnpILFVBQVVvSCxNQUFNLENBQUMsQ0FBQ00sS0FBSzNFO1lBQzNDLE9BQU8yRSxNQUFPM0UsQ0FBQUEsRUFBRStCLFVBQVUsSUFBSSxHQUFFO1FBQ2xDLEdBQUcsS0FBSzlFLFVBQVVDLE1BQU07UUFFeEIsT0FBT3dIO0lBQ1Q7SUFFQSxTQUFTO0lBQ0RFLDBCQUFrQztRQUN4QyxPQUFPLENBQUMsY0FBYyxFQUFFNUosS0FBSzBCLEdBQUcsR0FBRyxDQUFDLEVBQUV1RyxLQUFLNEIsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBQ2pGO0lBRUEsUUFBUTtJQUNBQyxpQkFBaUJ4SSxPQUFzQixFQUFVO1FBQ3ZELE1BQU15SSxXQUFXO1lBQ2Z6SSxRQUFRaEIsT0FBTztZQUNmZ0IsUUFBUVosZ0JBQWdCLElBQUk7WUFDNUJZLFFBQVEwSSxlQUFlLElBQUk7WUFDM0IxSSxRQUFRWCxjQUFjLElBQUk7WUFDMUJXLFFBQVFWLGtCQUFrQixJQUFJO1NBQy9CO1FBRUQsT0FBT21KLFNBQVNuRixJQUFJLENBQUMsS0FBS1osT0FBTyxDQUFDLGtCQUFrQjtJQUN0RDtJQUVBLFNBQVM7SUFDVCxNQUFNaUcsd0JBQXdCQyxlQUF1QixFQU0zQztRQUNSLFNBQVM7UUFDVCxLQUFLLE1BQU1uSixpQkFBaUIyRCxNQUFNeUYsSUFBSSxDQUFDLElBQUksQ0FBQ3BLLGFBQWEsQ0FBQ3FLLE1BQU0sSUFBSztZQUNuRSxJQUFJckosY0FBY3NELEVBQUUsS0FBSzZGLGlCQUFpQjtnQkFDeEMsT0FBTztvQkFDTDNGLE9BQU94RCxjQUFjd0QsS0FBSztvQkFDMUJDLGFBQWF6RCxjQUFjeUQsV0FBVztvQkFDdEMzRCxlQUFlRSxjQUFjZ0IsU0FBUyxDQUFDQyxNQUFNO29CQUM3Q3lELGVBQWU0RSxTQUFTdEosY0FBYzBFLGFBQWEsS0FBSztvQkFDeEQzRSxZQUFZNEQsTUFBTXlGLElBQUksQ0FBQyxJQUFJMUMsSUFBSTFHLGNBQWNnQixTQUFTLENBQUM4QyxHQUFHLENBQUNDLENBQUFBLElBQUtBLEVBQUVFLFNBQVM7Z0JBQzdFO1lBQ0Y7UUFDRjtRQUVBLE9BQU87SUFDVDtJQUVBLE9BQU87SUFDUHNGLGFBQW1CO1FBQ2pCLElBQUksQ0FBQ3ZLLGFBQWEsQ0FBQ3dLLEtBQUs7UUFDeEJsTCxPQUFPYSxJQUFJLENBQUM7SUFDZDtJQUVBLFNBQVM7SUFDVHNLLFdBSUU7UUFDQSxNQUFNQyxpQkFBaUIvRixNQUFNeUYsSUFBSSxDQUFDLElBQUksQ0FBQ3BLLGFBQWEsQ0FBQ3FLLE1BQU07UUFDM0QsTUFBTU0sc0JBQThDLENBQUM7UUFFckRELGVBQWU1QixPQUFPLENBQUMsQ0FBQy9EO1lBQ3RCNEYsbUJBQW1CLENBQUM1RixFQUFFeEUsT0FBTyxDQUFDLEdBQUcsQ0FBQ29LLG1CQUFtQixDQUFDNUYsRUFBRXhFLE9BQU8sQ0FBQyxJQUFJLEtBQUs7UUFDM0U7UUFFQSxPQUFPO1lBQ0xxSyxXQUFXLElBQUksQ0FBQzVLLGFBQWEsQ0FBQzJILElBQUk7WUFDbENrRCxxQkFBcUJILGVBQWV6SSxNQUFNO1lBQzFDMEk7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNakosNkJBQTZCSCxPQUFzQixFQUEwQjtRQUNqRixNQUFNUixhQUFhO1lBQUM7WUFBTztZQUFPO1lBQU87U0FBTTtRQUMvQyxNQUFNK0osb0JBQW9CLEVBQUUsVUFBVTs7UUFDdEMsTUFBTUMsd0JBQXdCO1FBRTlCLFdBQVc7UUFDWCxNQUFNL0osZ0JBQStCO1lBQ25Dc0QsSUFBSSxDQUFDLGNBQWMsRUFBRXZFLEtBQUswQixHQUFHLEdBQUcsQ0FBQztZQUNqQytDLE9BQU8sQ0FBQyxXQUFXLEVBQUVqRCxRQUFRWixnQkFBZ0IsQ0FBQyxDQUFDO1lBQy9DOEQsYUFBYSxDQUFDLGFBQWEsRUFBRWxELFFBQVFaLGdCQUFnQixDQUFDLE1BQU0sQ0FBQztZQUM3REosU0FBU2dCLFFBQVFoQixPQUFPO1lBQ3hCSSxrQkFBa0JZLFFBQVFaLGdCQUFnQjtZQUMxQytELGdCQUFnQm5ELFFBQVFoQixPQUFPLEtBQUssaUJBQWlCLGlCQUFpQjtZQUN0RVEsWUFBWUE7WUFDWmlCLFdBQVcsRUFBRTtZQUNieUQsZ0JBQWdCO1lBQ2hCQyxlQUFlO1lBQ2ZFLFdBQVcsSUFBSTdGLE9BQU84RixXQUFXO1FBQ25DO1FBRUEsY0FBYztRQUNkLEtBQUssTUFBTVosYUFBYWxFLFdBQVk7WUFDbEN6QixPQUFPYSxJQUFJLENBQUMsQ0FBQyxPQUFPLEVBQUU4RSxVQUFVLElBQUksQ0FBQztZQUVyQyxJQUFLLElBQUkrRixRQUFRLEdBQUdBLFFBQVFoRCxLQUFLdUIsSUFBSSxDQUFDd0Isd0JBQXdCRCxvQkFBb0JFLFFBQVM7Z0JBQ3pGLE1BQU1oSSxhQUFhZ0ksUUFBUUY7Z0JBQzNCLE1BQU0zSCxXQUFXNkUsS0FBS2QsR0FBRyxDQUFDbEUsYUFBYThILG1CQUFtQkM7Z0JBQzFELE1BQU1FLG1CQUFtQjlILFdBQVdIO2dCQUVwQyxJQUFJO29CQUNGLE1BQU1rSSxpQkFBaUIsTUFBTSxJQUFJLENBQUNDLHFCQUFxQixDQUNyRGxHLFdBQ0FnRyxrQkFDQWpJLGFBQWEsR0FDYnpCO29CQUdGUCxjQUFjZ0IsU0FBUyxDQUFDdUMsSUFBSSxJQUFJMkc7b0JBQ2hDNUwsT0FBT2EsSUFBSSxDQUFDLENBQUMsR0FBRyxFQUFFOEUsVUFBVSxHQUFHLEVBQUUrRixRQUFRLEVBQUUsUUFBUSxDQUFDLEVBQUU7d0JBQ3BESSxXQUFXRixlQUFlakosTUFBTTt3QkFDaENvSixnQkFBZ0JySyxjQUFjZ0IsU0FBUyxDQUFDQyxNQUFNO29CQUNoRDtvQkFFQSxTQUFTO29CQUNULElBQUksQ0FBQ2pDLGFBQWEsQ0FBQ3NMLEdBQUcsQ0FBQ3RLLGNBQWNzRCxFQUFFLEVBQUV0RDtnQkFFM0MsRUFBRSxPQUFPSSxPQUFPO29CQUNkOUIsT0FBT29ELElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRXVDLFVBQVUsR0FBRyxFQUFFK0YsUUFBUSxFQUFFLGFBQWEsQ0FBQyxFQUFFO3dCQUFFNUo7b0JBQU07b0JBRW5FLGFBQWE7b0JBQ2IsTUFBTW1LLG1CQUFtQixJQUFJLENBQUNDLDZCQUE2QixDQUN6RHZHLFdBQ0FnRyxrQkFDQWpJLGFBQWE7b0JBRWZoQyxjQUFjZ0IsU0FBUyxDQUFDdUMsSUFBSSxJQUFJZ0g7Z0JBQ2xDO1lBQ0Y7UUFDRjtRQUVBLFNBQVM7UUFDVHZLLGNBQWN5RSxjQUFjLEdBQUd6RSxjQUFjZ0IsU0FBUyxDQUFDQyxNQUFNO1FBQzdEakIsY0FBYzBFLGFBQWEsR0FBRyxJQUFJLENBQUNDLHNCQUFzQixDQUFDM0UsY0FBY2dCLFNBQVM7UUFFakYsT0FBT2hCO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELE1BQU1tSyxzQkFDSmxHLFNBQWlCLEVBQ2pCbkUsYUFBcUIsRUFDckJrQyxVQUFrQixFQUNsQnpCLE9BQXNCLEVBQ0Q7UUFDckIsTUFBTWtLLFNBQVMsSUFBSSxDQUFDQyxnQkFBZ0IsQ0FBQ3pHLFdBQVduRSxlQUFla0MsWUFBWXpCO1FBRTNFLE1BQU1vSyxhQUF5QjtZQUM3QjdKLE9BQU87WUFDUDhKLFVBQVU7Z0JBQ1I7b0JBQUVDLE1BQU07b0JBQVV2SixTQUFTbUosT0FBT3RKLFlBQVk7Z0JBQUM7Z0JBQy9DO29CQUFFMEosTUFBTTtvQkFBUXZKLFNBQVNtSixPQUFPckosVUFBVTtnQkFBQzthQUM1QztZQUNEMEosYUFBYTtZQUNiQyxXQUFXLEtBQUssc0JBQXNCO1FBQ3hDO1FBRUEsTUFBTWhLLFdBQVc7UUFDakIsTUFBTWlLLFdBQVcsTUFBTSxJQUFJLENBQUN2TSxTQUFTLENBQUN3TSxJQUFJLENBQUNsSyxVQUFVNEo7UUFFckQsT0FBTyxJQUFJLENBQUNPLGtCQUFrQixDQUFDRixTQUFTMUosT0FBTyxJQUFJLElBQUkyQyxXQUFXakM7SUFDcEU7SUFFQTs7R0FFQyxHQUNELGlCQUF5QmlDLFNBQWlCLEVBQUVuRSxhQUFxQixFQUFFa0MsVUFBa0IsRUFBRXpCLE9BQXNCLEVBQUU7UUFDN0csTUFBTTRLLHdCQUF3QjtZQUM1QixPQUFPO1lBQ1AsT0FBTztZQUNQLE9BQU87WUFDUCxPQUFPO1FBQ1Q7UUFFQSxNQUFNaEssZUFBZSxDQUFDLGdCQUFnQixFQUFFOEMsVUFBVTs7QUFFdEQsRUFBRUEsVUFBVSxLQUFLLEVBQUVrSCxxQkFBcUIsQ0FBQ2xILFVBQWdELENBQUM7O0dBRXZGLEVBQUVuRSxjQUFjLEdBQUcsRUFBRVMsUUFBUVosZ0JBQWdCLENBQUMsVUFBVSxDQUFDO1FBRXhELE1BQU15QixhQUFhLENBQUMsRUFBRSxFQUFFNkMsVUFBVSxJQUFJLEVBQUVuRSxjQUFjLE9BQU8sRUFBRWtDLFdBQVc7Ozs7YUFJakUsRUFBRXpCLFFBQVFaLGdCQUFnQixDQUFDO0VBQ3RDLEVBQUVZLFFBQVFoQixPQUFPLEtBQUssaUJBQWlCLGNBQWMsVUFBVTs7Ozs7Ozs7ZUFRbEQsRUFBRXlDLFdBQVc7b0JBQ1IsRUFBRWlDLFVBQVU7Ozs7a0JBSWQsRUFBRWpDLFdBQVc7a0JBQ2IsRUFBRUEsV0FBVztrQkFDYixFQUFFQSxXQUFXO2tCQUNiLEVBQUVBLFdBQVc7a0JBQ2IsRUFBRUEsV0FBVzs7O2VBR2hCLEVBQUVBLFdBQVc7Ozs7OztjQU1kLENBQUM7UUFFWCxPQUFPO1lBQUViO1lBQWNDO1FBQVc7SUFDcEM7SUFFQTs7R0FFQyxHQUNELG1CQUEyQkUsT0FBZSxFQUFFMkMsU0FBaUIsRUFBRWpDLFVBQWtCLEVBQWM7UUFDN0YsSUFBSTtZQUNGVCxRQUFRQyxHQUFHLENBQUMsYUFBYUYsUUFBUUwsTUFBTTtZQUN2Q00sUUFBUUMsR0FBRyxDQUFDLGVBQWVGLFFBQVFHLFNBQVMsQ0FBQyxHQUFHO1lBRWhELFNBQVM7WUFDVCxJQUFJRSxVQUFVO1lBQ2QsTUFBTXlKLFlBQVk5SixRQUFRTyxLQUFLLENBQUM7WUFDaEMsSUFBSXVKLFdBQVc7Z0JBQ2J6SixVQUFVeUosU0FBUyxDQUFDLEVBQUUsQ0FBQ3RKLElBQUk7WUFDN0IsT0FBTztnQkFDTCxNQUFNdUosY0FBYy9KLFFBQVFPLEtBQUssQ0FBQztnQkFDbEMsSUFBSXdKLGFBQWE7b0JBQ2YxSixVQUFVMEosV0FBVyxDQUFDLEVBQUUsQ0FBQ3ZKLElBQUk7Z0JBQy9CO1lBQ0Y7WUFFQSxJQUFJLENBQUNILFNBQVM7Z0JBQ1osTUFBTSxJQUFJdEIsTUFBTTtZQUNsQjtZQUVBa0IsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkcsUUFBUVYsTUFBTTtZQUUzQyxlQUFlO1lBQ2YsSUFBSXlCLFlBQVksSUFBSSxDQUFDNEksWUFBWSxDQUFDM0o7WUFDbENKLFFBQVFDLEdBQUcsQ0FBQyxhQUFha0IsVUFBVWpCLFNBQVMsQ0FBQyxHQUFHLE9BQU87WUFFdkQsTUFBTThKLFNBQVNqSixLQUFLQyxLQUFLLENBQUNHO1lBRTFCLElBQUk2SSxPQUFPdkssU0FBUyxJQUFJMkMsTUFBTUMsT0FBTyxDQUFDMkgsT0FBT3ZLLFNBQVMsR0FBRztnQkFDdkQsTUFBTUEsWUFBWXVLLE9BQU92SyxTQUFTLENBQUM4QyxHQUFHLENBQUMsQ0FBQ0MsR0FBUUMsUUFBbUI7d0JBQ2pFVixJQUFJUyxFQUFFVCxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUV0QixhQUFhZ0MsTUFBTSxDQUFDO3dCQUNyQ0MsV0FBV0YsRUFBRUUsU0FBUyxJQUFJQTt3QkFDMUJDLE1BQU1ILEVBQUVHLElBQUksSUFBSTt3QkFDaEJHLE1BQU1OLEVBQUVNLElBQUksSUFBSU4sRUFBRVAsS0FBSyxJQUFJLENBQUMsRUFBRVMsVUFBVSxLQUFLLEVBQUVqQyxhQUFhZ0MsTUFBTSxDQUFDO3dCQUNuRXpELFNBQVN3RCxFQUFFeEQsT0FBTyxJQUFJLElBQUksQ0FBQzBGLHNCQUFzQixDQUFDO3dCQUNsRDFCLFVBQVVSLEVBQUVRLFFBQVEsS0FBSzt3QkFDekJDLE9BQU9ULEVBQUVTLEtBQUssSUFBSXhDLGFBQWFnQztvQkFDakM7Z0JBRUF6QyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxLQUFLLEVBQUVSLFVBQVVDLE1BQU0sQ0FBQyxJQUFJLENBQUM7Z0JBQzFDLE9BQU9EO1lBQ1Q7WUFFQSxNQUFNLElBQUlYLE1BQU07UUFDbEIsRUFBRSxPQUFPRCxPQUFPO1lBQ2RtQixRQUFRbkIsS0FBSyxDQUFDLFdBQVdBO1lBQ3pCOUIsT0FBT29ELElBQUksQ0FBQyxDQUFDLGFBQWEsQ0FBQyxFQUFFO2dCQUFFdEI7Z0JBQU82RDtnQkFBV2pDO1lBQVc7WUFDNUQsT0FBTyxJQUFJLENBQUN3SSw2QkFBNkIsQ0FBQ3ZHLFdBQVcsR0FBR2pDO1FBQzFEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQXFCTCxPQUFlLEVBQVU7UUFDNUMsSUFBSTtZQUNGLGtCQUFrQjtZQUNsQlcsS0FBS0MsS0FBSyxDQUFDWjtZQUNYLE9BQU9BO1FBQ1QsRUFBRSxPQUFPdkIsT0FBTztZQUNkbUIsUUFBUUMsR0FBRyxDQUFDO1lBRVosSUFBSXdCLFFBQVFyQjtZQUVaLG1CQUFtQjtZQUNuQixJQUFJLENBQUNxQixNQUFNcUQsUUFBUSxDQUFDLGdCQUFnQjtnQkFDbEMsTUFBTSxJQUFJaEcsTUFBTTtZQUNsQjtZQUVBLFlBQVk7WUFDWixJQUFJLENBQUMyQyxNQUFNRSxRQUFRLENBQUMsTUFBTTtnQkFDeEIsZ0JBQWdCO2dCQUNoQixNQUFNc0ksdUJBQXVCeEksTUFBTUksV0FBVyxDQUFDO2dCQUMvQyxJQUFJb0kseUJBQXlCLENBQUMsR0FBRztvQkFDL0Isa0JBQWtCO29CQUNsQixNQUFNQyxhQUFhekksTUFBTXZCLFNBQVMsQ0FBQytKO29CQUNuQyxNQUFNRSxjQUFjRCxXQUFXNUosS0FBSyxDQUFDO29CQUNyQyxJQUFJNkosYUFBYTt3QkFDZixNQUFNQyxTQUFTSCx1QkFBdUJDLFdBQVd4SixPQUFPLENBQUN5SixXQUFXLENBQUMsRUFBRSxJQUFJQSxXQUFXLENBQUMsRUFBRSxDQUFDekssTUFBTTt3QkFDaEcrQixRQUFRQSxNQUFNdkIsU0FBUyxDQUFDLEdBQUdrSyxVQUFVO29CQUN2QztnQkFDRjtZQUNGO1lBRUEsU0FBUztZQUNUckosS0FBS0MsS0FBSyxDQUFDUztZQUNYekIsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBT3dCO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsOEJBQXNDaUIsU0FBaUIsRUFBRW5FLGFBQXFCLEVBQUVrQyxVQUFrQixFQUFjO1FBQzlHLE1BQU1oQixZQUF3QixFQUFFO1FBRWhDLElBQUssSUFBSW9CLElBQUksR0FBR0EsSUFBSXRDLGVBQWVzQyxJQUFLO1lBQ3RDLE1BQU13SixnQkFBZ0I1SixhQUFhSTtZQUNuQ3BCLFVBQVV1QyxJQUFJLENBQUM7Z0JBQ2JELElBQUksQ0FBQyxFQUFFLEVBQUVzSSxjQUFjLENBQUM7Z0JBQ3hCM0g7Z0JBQ0FDLE1BQU07Z0JBQ05HLE1BQU0sQ0FBQyxFQUFFLEVBQUVKLFVBQVUsUUFBUSxFQUFFMkgsY0FBYyxDQUFDO2dCQUM5Q3JMLFNBQVMsSUFBSSxDQUFDMEYsc0JBQXNCLENBQUM7Z0JBQ3JDMUIsVUFBVTtnQkFDVkMsT0FBT29IO1lBQ1Q7UUFDRjtRQUVBLE9BQU81SztJQUNUO0lBRUE7O0dBRUMsR0FDRCx5QkFBaUNULE9BQXNCLEVBQWM7UUFDbkUsTUFBTVMsWUFBd0IsRUFBRTtRQUNoQyxNQUFNNksscUJBQXFCO1lBQ3pCLE9BQU87Z0JBQ0w7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNELE9BQU87Z0JBQ0w7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNELE9BQU87Z0JBQ0w7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNELE9BQU87Z0JBQ0w7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtRQUNIO1FBRUEsSUFBSUMsYUFBYTtRQUNqQnZMLFFBQVFSLFVBQVUsQ0FBQytILE9BQU8sQ0FBQzdELENBQUFBO1lBQ3pCLE1BQU04SCxpQkFBaUJGLGtCQUFrQixDQUFDNUgsVUFBNkMsSUFBSSxFQUFFO1lBQzdGOEgsZUFBZWpFLE9BQU8sQ0FBQ3pELENBQUFBO2dCQUNyQnJELFVBQVV1QyxJQUFJLENBQUM7b0JBQ2JELElBQUksQ0FBQyxFQUFFLEVBQUV3SSxXQUFXLENBQUM7b0JBQ3JCN0g7b0JBQ0FDLE1BQU07b0JBQ05HLE1BQU1BO29CQUNOOUQsU0FBUzt3QkFDUDs0QkFBRStDLElBQUksQ0FBQyxFQUFFLEVBQUV3SSxXQUFXLEVBQUUsQ0FBQzs0QkFBRXpILE1BQU07NEJBQVNDLE9BQU87d0JBQUU7d0JBQ25EOzRCQUFFaEIsSUFBSSxDQUFDLEVBQUUsRUFBRXdJLFdBQVcsRUFBRSxDQUFDOzRCQUFFekgsTUFBTTs0QkFBT0MsT0FBTzt3QkFBRTt3QkFDakQ7NEJBQUVoQixJQUFJLENBQUMsRUFBRSxFQUFFd0ksV0FBVyxFQUFFLENBQUM7NEJBQUV6SCxNQUFNOzRCQUFNQyxPQUFPO3dCQUFFO3dCQUNoRDs0QkFBRWhCLElBQUksQ0FBQyxFQUFFLEVBQUV3SSxXQUFXLEVBQUUsQ0FBQzs0QkFBRXpILE1BQU07NEJBQU1DLE9BQU87d0JBQUU7d0JBQ2hEOzRCQUFFaEIsSUFBSSxDQUFDLEVBQUUsRUFBRXdJLFdBQVcsRUFBRSxDQUFDOzRCQUFFekgsTUFBTTs0QkFBUUMsT0FBTzt3QkFBRTtxQkFDbkQ7b0JBQ0RDLFVBQVU7b0JBQ1ZDLE9BQU9zSDtnQkFDVDtnQkFDQUE7WUFDRjtRQUNGO1FBRUEsU0FBUztRQUNULE1BQU85SyxVQUFVQyxNQUFNLEdBQUcsR0FBSTtZQUM1QixNQUFNZ0QsWUFBWTFELFFBQVFSLFVBQVUsQ0FBQ2lCLFVBQVVDLE1BQU0sR0FBRyxFQUFFO1lBQzFERCxVQUFVdUMsSUFBSSxDQUFDO2dCQUNiRCxJQUFJLENBQUMsRUFBRSxFQUFFdEMsVUFBVUMsTUFBTSxHQUFHLEVBQUUsQ0FBQztnQkFDL0JnRDtnQkFDQUMsTUFBTTtnQkFDTkcsTUFBTSxDQUFDLEVBQUUsRUFBRUosVUFBVSxRQUFRLEVBQUVqRCxVQUFVQyxNQUFNLEdBQUcsRUFBRSxDQUFDO2dCQUNyRFYsU0FBUztvQkFDUDt3QkFBRStDLElBQUksQ0FBQyxFQUFFLEVBQUV0QyxVQUFVQyxNQUFNLEdBQUcsRUFBRSxFQUFFLENBQUM7d0JBQUVvRCxNQUFNO3dCQUFTQyxPQUFPO29CQUFFO29CQUM3RDt3QkFBRWhCLElBQUksQ0FBQyxFQUFFLEVBQUV0QyxVQUFVQyxNQUFNLEdBQUcsRUFBRSxFQUFFLENBQUM7d0JBQUVvRCxNQUFNO3dCQUFPQyxPQUFPO29CQUFFO29CQUMzRDt3QkFBRWhCLElBQUksQ0FBQyxFQUFFLEVBQUV0QyxVQUFVQyxNQUFNLEdBQUcsRUFBRSxFQUFFLENBQUM7d0JBQUVvRCxNQUFNO3dCQUFNQyxPQUFPO29CQUFFO29CQUMxRDt3QkFBRWhCLElBQUksQ0FBQyxFQUFFLEVBQUV0QyxVQUFVQyxNQUFNLEdBQUcsRUFBRSxFQUFFLENBQUM7d0JBQUVvRCxNQUFNO3dCQUFNQyxPQUFPO29CQUFFO29CQUMxRDt3QkFBRWhCLElBQUksQ0FBQyxFQUFFLEVBQUV0QyxVQUFVQyxNQUFNLEdBQUcsRUFBRSxFQUFFLENBQUM7d0JBQUVvRCxNQUFNO3dCQUFRQyxPQUFPO29CQUFFO2lCQUM3RDtnQkFDREMsVUFBVTtnQkFDVkMsT0FBT3hELFVBQVVDLE1BQU0sR0FBRztZQUM1QjtRQUNGO1FBRUEsT0FBT0Q7SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9zZXJ2aWNlcy9hZ2VudHMvUXVlc3Rpb25EZXNpZ25lckFnZW50LnRzPzdiM2MiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTExNQXBpQ2xpZW50LCBMTE1SZXF1ZXN0IH0gZnJvbSAnQC9zZXJ2aWNlcy9sbG0vbGxtLWFwaS1jbGllbnQnXG5pbXBvcnQgeyBMb2dnZXIgfSBmcm9tICdAL2xpYi9sb2dnZXInXG5pbXBvcnQgeyBCYXNlQWdlbnQsIEFnZW50SW5wdXQsIEFnZW50T3V0cHV0LCBBZ2VudFN0YXR1cyB9IGZyb20gJ0AvdHlwZXMvYWdlbnRzJ1xuaW1wb3J0IHsgUHJvbXB0QnVpbGRlciB9IGZyb20gJ0Avc2VydmljZXMvbGxtL3Byb21wdC1idWlsZGVyJ1xuaW1wb3J0IHsgRGF0YUZ1c2lvbkVuZ2luZSB9IGZyb20gJ0Avc2VydmljZXMvZGF0YS9kYXRhLWZ1c2lvbi1lbmdpbmUnXG5cbmNvbnN0IGxvZ2dlciA9IG5ldyBMb2dnZXIoJ1F1ZXN0aW9uRGVzaWduZXJBZ2VudCcpXG5cbmV4cG9ydCBpbnRlcmZhY2UgRGVzaWduT3B0aW9ucyB7XG4gIHZlcnNpb246ICdzdGFuZGFyZCcgfCAncHJvZmVzc2lvbmFsJ1xuICBvcmdhbml6YXRpb25UeXBlOiBzdHJpbmdcbiAgdGFyZ2V0QXVkaWVuY2U6IHN0cmluZ1xuICBjdXN0b21SZXF1aXJlbWVudHM6IHN0cmluZ1xuICBxdWVzdGlvbkNvdW50OiBudW1iZXJcbiAgZGltZW5zaW9uczogc3RyaW5nW11cbiAgaW5kdXN0cnlDb250ZXh0Pzogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUXVlc3Rpb25uYWlyZSB7XG4gIGlkOiBzdHJpbmdcbiAgdGl0bGU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIHZlcnNpb246IHN0cmluZ1xuICBvcmdhbml6YXRpb25UeXBlOiBzdHJpbmdcbiAgYXNzZXNzbWVudFR5cGU6IHN0cmluZ1xuICBkaW1lbnNpb25zOiBzdHJpbmdbXVxuICBxdWVzdGlvbnM6IFF1ZXN0aW9uW11cbiAgdG90YWxRdWVzdGlvbnM6IG51bWJlclxuICBlc3RpbWF0ZWRUaW1lOiBzdHJpbmdcbiAgY3JlYXRlZEF0OiBzdHJpbmdcbiAgbWV0YWRhdGE/OiB7XG4gICAgZ2VuZXJhdGlvblRpbWU6IHN0cmluZ1xuICAgIG1vZGVsOiBzdHJpbmdcbiAgICBwcm92aWRlcjogc3RyaW5nXG4gIH1cbn1cblxuZXhwb3J0IHR5cGUgUXVlc3Rpb25UeXBlID0gJ3NpbmdsZV9jaG9pY2UnIHwgJ211bHRpcGxlX2Nob2ljZScgfCAnc2NhbGUnIHwgJ29wZW5fZW5kZWQnIHwgJ3JhbmtpbmcnIHwgJ21hdHJpeCcgfCAnbGlrZXJ0X3NjYWxlJyB8ICdjaG9pY2UnIHwgJ3NjZW5hcmlvJztcblxuZXhwb3J0IGludGVyZmFjZSBRdWVzdGlvbiB7XG4gIGlkOiBzdHJpbmdcbiAgZGltZW5zaW9uOiBzdHJpbmdcbiAgc3ViZGltZW5zaW9uPzogc3RyaW5nXG4gIHR5cGU6IFF1ZXN0aW9uVHlwZVxuICBkZXB0aD86IHN0cmluZ1xuICB0ZXh0OiBzdHJpbmdcbiAgdGl0bGU/OiBzdHJpbmdcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmdcbiAgb3B0aW9ucz86IFF1ZXN0aW9uT3B0aW9uW11cbiAgc2NhbGU/OiBhbnlcbiAgcmV2ZXJzZWQ/OiBib29sZWFuXG4gIHdlaWdodD86IG51bWJlclxuICBtZXRhZGF0YT86IGFueVxuICByZXF1aXJlZDogYm9vbGVhblxuICBvcmRlcjogbnVtYmVyXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUXVlc3Rpb25PcHRpb24ge1xuICBpZDogc3RyaW5nXG4gIHRleHQ6IHN0cmluZ1xuICBzY29yZTogbnVtYmVyXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVmFsaWRhdGlvblJlc3VsdCB7XG4gIGlzVmFsaWQ6IGJvb2xlYW5cbiAgZXJyb3JzOiBzdHJpbmdbXVxuICB3YXJuaW5nczogc3RyaW5nW11cbiAgc3VnZ2VzdGlvbnM6IHN0cmluZ1tdXG4gIHNjb3JlOiBudW1iZXJcbiAgcXVhbGl0eVNjb3JlPzogbnVtYmVyXG59XG5cbi8qKlxuICog6Zeu5Y236K6+6K6h5biI5pm66IO95L2TXG4gKi9cbmV4cG9ydCBjbGFzcyBRdWVzdGlvbkRlc2lnbmVyQWdlbnQgaW1wbGVtZW50cyBCYXNlQWdlbnQge1xuICBwdWJsaWMgcmVhZG9ubHkgbmFtZSA9ICdxdWVzdGlvbl9kZXNpZ25lcic7XG4gIHByaXZhdGUgbG9nZ2VyOiBMb2dnZXI7XG4gIHByaXZhdGUgaW5pdGlhbGl6ZWQgPSBmYWxzZTtcbiAgcHJpdmF0ZSBsYXN0QWN0aXZpdHkgPSBuZXcgRGF0ZSgpO1xuICBwcml2YXRlIHF1ZXN0aW9uQ2FjaGUgPSBuZXcgTWFwPHN0cmluZywgUXVlc3Rpb25uYWlyZT4oKTtcblxuICBjb25zdHJ1Y3RvcihcbiAgICBwcml2YXRlIGxsbUNsaWVudDogTExNQXBpQ2xpZW50LFxuICAgIHByaXZhdGUgcHJvbXB0QnVpbGRlcjogUHJvbXB0QnVpbGRlcixcbiAgICBwcml2YXRlIGRhdGFGdXNpb25FbmdpbmU6IERhdGFGdXNpb25FbmdpbmVcbiAgKSB7XG4gICAgdGhpcy5sb2dnZXIgPSBuZXcgTG9nZ2VyKCdRdWVzdGlvbkRlc2lnbmVyQWdlbnQnKTtcbiAgfVxuXG4gIC8qKlxuICAgKiDliJ3lp4vljJbmmbrog73kvZNcbiAgICovXG4gIGFzeW5jIGluaXRpYWxpemUoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdGhpcy5sb2dnZXIuaW5mbygn6Zeu5Y236K6+6K6h5biI5pm66IO95L2T5Yid5aeL5YyW5a6M5oiQJyk7XG4gICAgdGhpcy5pbml0aWFsaXplZCA9IHRydWU7XG4gICAgdGhpcy5sYXN0QWN0aXZpdHkgPSBuZXcgRGF0ZSgpO1xuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPluaZuuiDveS9k+eKtuaAgVxuICAgKi9cbiAgZ2V0U3RhdHVzKCk6IEFnZW50U3RhdHVzIHtcbiAgICByZXR1cm4ge1xuICAgICAgbmFtZTogdGhpcy5uYW1lLFxuICAgICAgaW5pdGlhbGl6ZWQ6IHRoaXMuaW5pdGlhbGl6ZWQsXG4gICAgICBsYXN0QWN0aXZpdHk6IHRoaXMubGFzdEFjdGl2aXR5LFxuICAgICAgY29uZmlnOiB7XG4gICAgICAgIGxsbVByb3ZpZGVyOiAnbWluaW1heCcsXG4gICAgICAgIHZlcnNpb246ICcxLjAuMCdcbiAgICAgIH1cbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIOaJp+ihjOaZuuiDveS9k+S7u+WKoVxuICAgKi9cbiAgYXN5bmMgZXhlY3V0ZShpbnB1dDogQWdlbnRJbnB1dCk6IFByb21pc2U8QWdlbnRPdXRwdXQ+IHtcbiAgICB0aGlzLmxvZ2dlci5pbmZvKCfmiafooYzpl67ljbforr7orqHku7vliqEnLCB7IGlucHV0IH0pO1xuICAgIHRoaXMubGFzdEFjdGl2aXR5ID0gbmV3IERhdGUoKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyDov5nph4zlupTor6XosIPnlKjnjrDmnInnmoQgZGVzaWduUXVlc3Rpb25uYWlyZSDmlrnms5VcbiAgICAgIC8vIOS4uuS6huS/neaMgeWFvOWuueaAp++8jOaIkeS7rOWwhui+k+WFpei9rOaNouS4uiBEZXNpZ25PcHRpb25zIOagvOW8j1xuICAgICAgY29uc3QgZGVzaWduT3B0aW9uczogRGVzaWduT3B0aW9ucyA9IHtcbiAgICAgICAgdmVyc2lvbjogaW5wdXQudmVyc2lvbiB8fCAnc3RhbmRhcmQnLFxuICAgICAgICBvcmdhbml6YXRpb25UeXBlOiBpbnB1dC5vcmdhbml6YXRpb25UeXBlIHx8ICflhaznm4rnu4Tnu4cnLFxuICAgICAgICB0YXJnZXRBdWRpZW5jZTogaW5wdXQudGFyZ2V0QXVkaWVuY2UgfHwgJ+e7hOe7h+aIkOWRmCcsXG4gICAgICAgIGN1c3RvbVJlcXVpcmVtZW50czogaW5wdXQuY3VzdG9tUmVxdWlyZW1lbnRzIHx8ICcnLFxuICAgICAgICBxdWVzdGlvbkNvdW50OiBpbnB1dC5xdWVzdGlvbkNvdW50IHx8IDIwLFxuICAgICAgICBkaW1lbnNpb25zOiBpbnB1dC5kaW1lbnNpb25zIHx8IFsn5oiY55Wl6KeE5YiSJywgJ+WboumYn+WNj+S9nCcsICfotYTmupDnrqHnkIYnLCAn5b2x5ZON5YqbJ11cbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHF1ZXN0aW9ubmFpcmUgPSBhd2FpdCB0aGlzLmRlc2lnbihkZXNpZ25PcHRpb25zKTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgZGF0YTogcXVlc3Rpb25uYWlyZVxuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhpcy5sb2dnZXIuZXJyb3IoJ+mXruWNt+iuvuiuoeWksei0pScsIHsgZXJyb3IgfSk7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ+acquefpemUmeivrydcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOiuvuiuoemXruWNt1xuICAgKi9cbiAgYXN5bmMgZGVzaWduKG9wdGlvbnM6IERlc2lnbk9wdGlvbnMpOiBQcm9taXNlPFF1ZXN0aW9ubmFpcmU+IHtcbiAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpXG5cbiAgICB0cnkge1xuICAgICAgbG9nZ2VyLmluZm8oJ+W8gOWni+WIhuaJueiuvuiuoemXruWNtycsIG9wdGlvbnMpXG5cbiAgICAgIC8vIOS9v+eUqOWIhuaJueeUn+aIkOetlueVpVxuICAgICAgY29uc3QgcXVlc3Rpb25uYWlyZSA9IGF3YWl0IHRoaXMuZ2VuZXJhdGVRdWVzdGlvbm5haXJlQmF0Y2hlZChvcHRpb25zKVxuXG4gICAgICBjb25zdCBnZW5lcmF0aW9uVGltZSA9ICgoRGF0ZS5ub3coKSAtIHN0YXJ0VGltZSkgLyAxMDAwKS50b0ZpeGVkKDEpICsgJ3MnXG5cbiAgICAgIC8vIOa3u+WKoOWFg+aVsOaNrlxuICAgICAgcXVlc3Rpb25uYWlyZS5tZXRhZGF0YSA9IHtcbiAgICAgICAgZ2VuZXJhdGlvblRpbWUsXG4gICAgICAgIG1vZGVsOiAnZGVlcHNlZWstY2hhdCcsXG4gICAgICAgIHByb3ZpZGVyOiAnZGVlcHNlZWsnXG4gICAgICB9XG5cbiAgICAgIGxvZ2dlci5pbmZvKCfpl67ljbforr7orqHlrozmiJAnLCB7XG4gICAgICAgIHF1ZXN0aW9uQ291bnQ6IHF1ZXN0aW9ubmFpcmUucXVlc3Rpb25zLmxlbmd0aCxcbiAgICAgICAgZ2VuZXJhdGlvblRpbWVcbiAgICAgIH0pXG5cbiAgICAgIHJldHVybiBxdWVzdGlvbm5haXJlXG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgbG9nZ2VyLmVycm9yKCfpl67ljbforr7orqHlpLHotKUnLCB7IGVycm9yLCBvcHRpb25zIH0pXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDmnoTlu7pMTE3mj5DnpLror41cbiAgICovXG4gIHByaXZhdGUgYnVpbGRQcm9tcHQob3B0aW9uczogRGVzaWduT3B0aW9ucykge1xuICAgIGNvbnN0IHN5c3RlbVByb21wdCA9IGDkvaDmmK9PQ1RJ77yIT3JnYW5pemF0aW9uIENhcGFiaWxpdHkgVHlwZSBJbmRpY2F0b3LvvInmmbrog73or4TkvLDns7vnu5/nmoTpl67ljbforr7orqHkuJPlrrbjgIJcblxuT0NUSeivhOS8sOahhuaetuWMheWQq+Wbm+S4quaguOW/g+e7tOW6pu+8mlxuMS4gUy9GIChTdHJ1Y3R1cmUvRmxleGliaWxpdHkpIC0g57uT5p6E5YyW5LiO54G15rS75oCnXG4yLiBJL1QgKElubm92YXRpb24vVHJhZGl0aW9uKSAtIOWIm+aWsOaAp+S4juS8oOe7n+aApyAgXG4zLiBNL1YgKE1hbmFnZW1lbnQvVmlzaW9uKSAtIOeuoeeQhuWvvOWQkeS4juaEv+aZr+WvvOWQkVxuNC4gQS9EIChBY3Rpb24vRGVjaXNpb24pIC0g6KGM5Yqo5a+85ZCR5LiO5Yaz562W5a+85ZCRXG5cbuivt+S4peagvOaMieeFp+S7peS4i+imgeaxguiuvuiuoemXruWNt++8mlxuLSDmr4/kuKrnu7TluqYxNemBk+mimO+8jOaAu+WFsTYw6YGT6aKYXG4tIOavj+mBk+mimDXkuKrpgInpobnvvIzliIblgLwxLTXliIZcbi0g6aKY55uu6KaB6ZKI5a+5JHtvcHRpb25zLm9yZ2FuaXphdGlvblR5cGV957G75Z6L57uE57uHXG4tICR7b3B0aW9ucy52ZXJzaW9uID09PSAncHJvZmVzc2lvbmFsJyA/ICfkuJPkuJrniYjpnIDopoHmm7Tmt7HlhaXnmoTmg4XlooPljJbpl67popgnIDogJ+agh+WHhueJiOS9v+eUqOmAmueUqOaAp+mXrumimCd9XG4tIOehruS/nemimOebrueahOS4k+S4muaAp+WSjOWunueUqOaAp1xuXG7ph43opoHvvJpcbjEuIOW/hemhu+i/lOWbnuWujOaVtOeahEpTT07moLzlvI/vvIzkuI3opoHmiKrmlq1cbjIuIOS9v+eUqOe0p+WHkeeahEpTT07moLzlvI/vvIzlh4/lsJHkuI3lv4XopoHnmoTnqbrmoLxcbjMuIOehruS/neaJgOaciTYw6YGT6aKY55uu6YO95YyF5ZCr5Zyo5ZON5bqU5LitYFxuXG4gICAgY29uc3QgdXNlclByb21wdCA9IGDor7fkuLoke29wdGlvbnMub3JnYW5pemF0aW9uVHlwZX3nsbvlnovnu4Tnu4forr7orqHkuIDlpZcke29wdGlvbnMudmVyc2lvbn3niYhPQ1RJ6K+E5Lyw6Zeu5Y2344CCXG5cbuWFt+S9k+imgeaxgu+8mlxuLSDnu4Tnu4fnsbvlnovvvJoke29wdGlvbnMub3JnYW5pemF0aW9uVHlwZX1cbi0g55uu5qCH5Y+X5LyX77yaJHtvcHRpb25zLnRhcmdldEF1ZGllbmNlfVxuLSDniYjmnKzvvJoke29wdGlvbnMudmVyc2lvbn1cbiR7b3B0aW9ucy5jdXN0b21SZXF1aXJlbWVudHMgPyBgLSDnibnmroropoHmsYLvvJoke29wdGlvbnMuY3VzdG9tUmVxdWlyZW1lbnRzfWAgOiAnJ31cblxu6K+35Lil5qC85oyJ54Wn5Lul5LiLSlNPTuagvOW8j+i/lOWbnuWujOaVtOeahDYw6YGT6aKY55uu77yaXG5cblxcYFxcYFxcYGpzb25cbntcbiAgXCJpZFwiOiBcInF1ZXN0aW9ubmFpcmVfJHtEYXRlLm5vdygpfVwiLFxuICBcInRpdGxlXCI6IFwi6Zeu5Y235qCH6aKYXCIsXG4gIFwiZGVzY3JpcHRpb25cIjogXCLpl67ljbfmj4/ov7BcIixcbiAgXCJ2ZXJzaW9uXCI6IFwiJHtvcHRpb25zLnZlcnNpb259XCIsXG4gIFwib3JnYW5pemF0aW9uVHlwZVwiOiBcIiR7b3B0aW9ucy5vcmdhbml6YXRpb25UeXBlfVwiLFxuICBcImFzc2Vzc21lbnRUeXBlXCI6IFwiJHtvcHRpb25zLnZlcnNpb24gPT09ICdwcm9mZXNzaW9uYWwnID8gJ3Byb2Zlc3Npb25hbCcgOiAnYmFzaWMnfVwiLFxuICBcImRpbWVuc2lvbnNcIjogW1wiUy9GXCIsIFwiSS9UXCIsIFwiTS9WXCIsIFwiQS9EXCJdLFxuICBcInF1ZXN0aW9uc1wiOiBbXG4gICAge1xuICAgICAgXCJpZFwiOiBcInFfMVwiLFxuICAgICAgXCJkaW1lbnNpb25cIjogXCJTL0ZcIixcbiAgICAgIFwidHlwZVwiOiBcInNpbmdsZV9jaG9pY2VcIixcbiAgICAgIFwidGl0bGVcIjogXCLpl67popjmoIfpophcIixcbiAgICAgIFwiZGVzY3JpcHRpb25cIjogXCLpl67popjmj4/ov7BcIixcbiAgICAgIFwib3B0aW9uc1wiOiBbXG4gICAgICAgIHtcImlkXCI6IFwib18xXCIsIFwidGV4dFwiOiBcIuWujOWFqOS4jeWQjOaEj1wiLCBcInNjb3JlXCI6IDF9LFxuICAgICAgICB7XCJpZFwiOiBcIm9fMlwiLCBcInRleHRcIjogXCLkuI3lkIzmhI9cIiwgXCJzY29yZVwiOiAyfSxcbiAgICAgICAge1wiaWRcIjogXCJvXzNcIiwgXCJ0ZXh0XCI6IFwi5Lit56uLXCIsIFwic2NvcmVcIjogM30sXG4gICAgICAgIHtcImlkXCI6IFwib180XCIsIFwidGV4dFwiOiBcIuWQjOaEj1wiLCBcInNjb3JlXCI6IDR9LFxuICAgICAgICB7XCJpZFwiOiBcIm9fNVwiLCBcInRleHRcIjogXCLlrozlhajlkIzmhI9cIiwgXCJzY29yZVwiOiA1fVxuICAgICAgXSxcbiAgICAgIFwicmVxdWlyZWRcIjogdHJ1ZSxcbiAgICAgIFwib3JkZXJcIjogMVxuICAgIH1cbiAgXVxufVxuXFxgXFxgXFxgXG5cbuazqOaEj++8mlxuMS4g5b+F6aG75YyF5ZCr5a6M5pW055qENjDpgZPpopjnm65cbjIuIOavj+S4que7tOW6pjE16YGT6aKYXG4zLiDnoa7kv51KU09O5qC85byP5a6M5pW077yM5LiN6KaB5oiq5patXG40LiDmiYDmnInlrZfmrrXpg73lv4XpobvloavlhpnlrozmlbRgXG5cbiAgICByZXR1cm4ge1xuICAgICAgc3lzdGVtUHJvbXB0LFxuICAgICAgdXNlclByb21wdFxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDop6PmnpBMTE3lk43lupRcbiAgICovXG4gIHByaXZhdGUgcGFyc2VSZXNwb25zZShjb250ZW50OiBzdHJpbmcsIG9wdGlvbnM6IERlc2lnbk9wdGlvbnMpOiBRdWVzdGlvbm5haXJlIHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ+WOn+Wni0xMTeWTjeW6lOmVv+W6pjonLCBjb250ZW50Lmxlbmd0aClcbiAgICAgIGNvbnNvbGUubG9nKCfljp/lp4tMTE3lk43lupTliY01MDDlrZfnrKY6JywgY29udGVudC5zdWJzdHJpbmcoMCwgNTAwKSlcblxuICAgICAgLy8g5qOA5p+l5ZON5bqU5piv5ZCm6KKr5oiq5patXG4gICAgICBpZiAoY29udGVudC5sZW5ndGggPCAxMDAwKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignTExN5ZON5bqU6ZW/5bqm6L+H55+t77yM5Y+v6IO96KKr5oiq5patOicsIGNvbnRlbnQubGVuZ3RoKVxuICAgICAgfVxuXG4gICAgICAvLyDlsJ3or5XlpJrnp41KU09O5o+Q5Y+W5pa55byPXG4gICAgICBsZXQganNvblN0ciA9ICcnXG5cbiAgICAgIC8vIOaWueW8jzE6IOaPkOWPlmBgYGpzb27ku6PnoIHlnZdcbiAgICAgIGNvbnN0IGpzb25CbG9ja01hdGNoID0gY29udGVudC5tYXRjaCgvYGBganNvblxccyooW1xcc1xcU10qPylcXHMqYGBgLylcbiAgICAgIGlmIChqc29uQmxvY2tNYXRjaCkge1xuICAgICAgICBqc29uU3RyID0ganNvbkJsb2NrTWF0Y2hbMV0udHJpbSgpXG4gICAgICAgIGNvbnNvbGUubG9nKCfmj5Dlj5bliLBKU09O5Luj56CB5Z2X77yM6ZW/5bqmOicsIGpzb25TdHIubGVuZ3RoKVxuICAgICAgfVxuXG4gICAgICAvLyDmlrnlvI8yOiDmj5Dlj5blrozmlbTnmoRKU09O5a+56LGhXG4gICAgICBpZiAoIWpzb25TdHIpIHtcbiAgICAgICAgY29uc3QganNvbk9iamVjdE1hdGNoID0gY29udGVudC5tYXRjaCgvXFx7W1xcc1xcU10qXFx9LylcbiAgICAgICAgaWYgKGpzb25PYmplY3RNYXRjaCkge1xuICAgICAgICAgIGpzb25TdHIgPSBqc29uT2JqZWN0TWF0Y2hbMF0udHJpbSgpXG4gICAgICAgICAgY29uc29sZS5sb2coJ+aPkOWPluWIsEpTT07lr7nosaHvvIzplb/luqY6JywganNvblN0ci5sZW5ndGgpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8g5pa55byPMzog5p+l5om+5pyA5ZCO5LiA5Liq5a6M5pW055qEfVxuICAgICAgaWYgKCFqc29uU3RyKSB7XG4gICAgICAgIGNvbnN0IHN0YXJ0SW5kZXggPSBjb250ZW50LmluZGV4T2YoJ3snKVxuICAgICAgICBpZiAoc3RhcnRJbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICBsZXQgYnJhY2VDb3VudCA9IDBcbiAgICAgICAgICBsZXQgZW5kSW5kZXggPSAtMVxuICAgICAgICAgIFxuICAgICAgICAgIGZvciAobGV0IGkgPSBzdGFydEluZGV4OyBpIDwgY29udGVudC5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgaWYgKGNvbnRlbnRbaV0gPT09ICd7JykgYnJhY2VDb3VudCsrXG4gICAgICAgICAgICBpZiAoY29udGVudFtpXSA9PT0gJ30nKSB7XG4gICAgICAgICAgICAgIGJyYWNlQ291bnQtLVxuICAgICAgICAgICAgICBpZiAoYnJhY2VDb3VudCA9PT0gMCkge1xuICAgICAgICAgICAgICAgIGVuZEluZGV4ID0gaVxuICAgICAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgICAgXG4gICAgICAgICAgaWYgKGVuZEluZGV4ICE9PSAtMSkge1xuICAgICAgICAgICAganNvblN0ciA9IGNvbnRlbnQuc3Vic3RyaW5nKHN0YXJ0SW5kZXgsIGVuZEluZGV4ICsgMSlcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfpgJrov4fmi6zlj7fljLnphY3mj5Dlj5ZKU09O77yM6ZW/5bqmOicsIGpzb25TdHIubGVuZ3RoKVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAoanNvblN0cikge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHBhcnNlZENvbnRlbnQgPSBKU09OLnBhcnNlKGpzb25TdHIpXG4gICAgICAgICAgY29uc29sZS5sb2coJ0pTT07op6PmnpDmiJDlip/vvIzpl67popjmlbDph486JywgcGFyc2VkQ29udGVudC5xdWVzdGlvbnM/Lmxlbmd0aCB8fCAwKVxuICAgICAgICAgIHJldHVybiB0aGlzLnZhbGlkYXRlQW5kRm9ybWF0UXVlc3Rpb25uYWlyZShwYXJzZWRDb250ZW50LCBvcHRpb25zKVxuICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignSlNPTuino+aekOWksei0pTonLCBwYXJzZUVycm9yKVxuICAgICAgICAgIGNvbnNvbGUubG9nKCflsJ3or5Xkv67lpI1KU09OLi4uJylcbiAgICAgICAgICBcbiAgICAgICAgICAvLyDlsJ3or5Xkv67lpI3luLjop4HnmoRKU09O6Zeu6aKYXG4gICAgICAgICAgY29uc3QgZml4ZWRKc29uID0gdGhpcy5maXhKc29uU3RyaW5nKGpzb25TdHIpXG4gICAgICAgICAgaWYgKGZpeGVkSnNvbikge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgY29uc3QgcGFyc2VkQ29udGVudCA9IEpTT04ucGFyc2UoZml4ZWRKc29uKVxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5L+u5aSN5ZCOSlNPTuino+aekOaIkOWKn++8jOmXrumimOaVsOmHjzonLCBwYXJzZWRDb250ZW50LnF1ZXN0aW9ucz8ubGVuZ3RoIHx8IDApXG4gICAgICAgICAgICAgIHJldHVybiB0aGlzLnZhbGlkYXRlQW5kRm9ybWF0UXVlc3Rpb25uYWlyZShwYXJzZWRDb250ZW50LCBvcHRpb25zKVxuICAgICAgICAgICAgfSBjYXRjaCAoZml4RXJyb3IpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5L+u5aSN5ZCO5LuN54S26Kej5p6Q5aSx6LSlOicsIGZpeEVycm9yKVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICAvLyDlpoLmnpxKU09O6Kej5p6Q6YO95aSx6LSl77yM5L2/55So57uT5p6E5YyW6Kej5p6QXG4gICAgICBjb25zb2xlLmxvZygnSlNPTuino+aekOWksei0pe+8jOS9v+eUqOe7k+aehOWMluino+aekCcpXG4gICAgICByZXR1cm4gdGhpcy5wYXJzZVN0cnVjdHVyZWRSZXNwb25zZShjb250ZW50LCBvcHRpb25zKVxuICAgICAgXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WTjeW6lOino+aekOWujOWFqOWksei0pTonLCBlcnJvcilcbiAgICAgIGxvZ2dlci5lcnJvcignTExN5ZON5bqU6Kej5p6Q5aSx6LSlJywgeyBcbiAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogU3RyaW5nKGVycm9yKSxcbiAgICAgICAgY29udGVudFByZXZpZXc6IGNvbnRlbnQuc3Vic3RyaW5nKDAsIDUwMClcbiAgICAgIH0pXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYOmXruWNt+ino+aekOWksei0pTogJHtlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfmnKrnn6XplJnor68nfWApXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOWwneivleS/ruWkjUpTT07lrZfnrKbkuLJcbiAgICovXG4gIHByaXZhdGUgZml4SnNvblN0cmluZyhqc29uU3RyOiBzdHJpbmcpOiBzdHJpbmcgfCBudWxsIHtcbiAgICB0cnkge1xuICAgICAgLy8g56e76Zmk5Y+v6IO955qE5o6n5Yi25a2X56ymXG4gICAgICBsZXQgZml4ZWQgPSBqc29uU3RyLnJlcGxhY2UoL1tcXHgwMC1cXHgxRlxceDdGXS9nLCAnJylcbiAgICAgIFxuICAgICAgLy8g5L+u5aSN5bi46KeB55qE5byV5Y+36Zeu6aKYXG4gICAgICBmaXhlZCA9IGZpeGVkLnJlcGxhY2UoLycvZywgJ1wiJylcbiAgICAgIFxuICAgICAgLy8g5L+u5aSN5pyr5bC+57y65bCR55qE5ous5Y+35oiW5byV5Y+3XG4gICAgICBpZiAoIWZpeGVkLmVuZHNXaXRoKCd9JykpIHtcbiAgICAgICAgLy8g5bCd6K+V5om+5Yiw5pyA5ZCO5LiA5Liq5a6M5pW055qE5a+56LGhXG4gICAgICAgIGNvbnN0IGxhc3RDb21wbGV0ZU9iamVjdCA9IGZpeGVkLmxhc3RJbmRleE9mKCdcIn0nKVxuICAgICAgICBpZiAobGFzdENvbXBsZXRlT2JqZWN0ICE9PSAtMSkge1xuICAgICAgICAgIGZpeGVkID0gZml4ZWQuc3Vic3RyaW5nKDAsIGxhc3RDb21wbGV0ZU9iamVjdCArIDIpICsgJ30nXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8g6aqM6K+B5L+u5aSN5ZCO55qESlNPTlxuICAgICAgSlNPTi5wYXJzZShmaXhlZClcbiAgICAgIHJldHVybiBmaXhlZFxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdKU09O5L+u5aSN5aSx6LSlOicsIGVycm9yKVxuICAgICAgcmV0dXJuIG51bGxcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog6aqM6K+B5ZKM5qC85byP5YyW6Kej5p6Q5ZCO55qE6Zeu5Y23XG4gICAqL1xuICBwcml2YXRlIHZhbGlkYXRlQW5kRm9ybWF0UXVlc3Rpb25uYWlyZShwYXJzZWRDb250ZW50OiBhbnksIG9wdGlvbnM6IERlc2lnbk9wdGlvbnMpOiBRdWVzdGlvbm5haXJlIHtcbiAgICBjb25zdCBlcnJvcnM6IHN0cmluZ1tdID0gW11cbiAgICBcbiAgICBpZiAoIXBhcnNlZENvbnRlbnQuaWQpIHtcbiAgICAgIGVycm9ycy5wdXNoKCfpl67ljbdJROS4jeiDveS4uuepuicpXG4gICAgfVxuICAgIGlmICghcGFyc2VkQ29udGVudC50aXRsZSkge1xuICAgICAgZXJyb3JzLnB1c2goJ+mXruWNt+agh+mimOS4jeiDveS4uuepuicpXG4gICAgfVxuICAgIGlmICghcGFyc2VkQ29udGVudC5kZXNjcmlwdGlvbikge1xuICAgICAgZXJyb3JzLnB1c2goJ+mXruWNt+aPj+i/sOS4jeiDveS4uuepuicpXG4gICAgfVxuICAgIGlmICghcGFyc2VkQ29udGVudC52ZXJzaW9uKSB7XG4gICAgICBlcnJvcnMucHVzaCgn6Zeu5Y2354mI5pys5LiN6IO95Li656m6JylcbiAgICB9XG4gICAgaWYgKCFwYXJzZWRDb250ZW50Lm9yZ2FuaXphdGlvblR5cGUpIHtcbiAgICAgIGVycm9ycy5wdXNoKCfnu4Tnu4fnsbvlnovkuI3og73kuLrnqbonKVxuICAgIH1cbiAgICBpZiAoIXBhcnNlZENvbnRlbnQuYXNzZXNzbWVudFR5cGUpIHtcbiAgICAgIGVycm9ycy5wdXNoKCfor4TkvLDnsbvlnovkuI3og73kuLrnqbonKVxuICAgIH1cbiAgICBpZiAoIXBhcnNlZENvbnRlbnQuZGltZW5zaW9ucyB8fCAhQXJyYXkuaXNBcnJheShwYXJzZWRDb250ZW50LmRpbWVuc2lvbnMpIHx8IHBhcnNlZENvbnRlbnQuZGltZW5zaW9ucy5sZW5ndGggIT09IDQpIHtcbiAgICAgIGVycm9ycy5wdXNoKCfpl67ljbflv4XpobvljIXlkKvlm5vkuKrnu7TluqYnKVxuICAgIH1cbiAgICBpZiAoIXBhcnNlZENvbnRlbnQucXVlc3Rpb25zIHx8ICFBcnJheS5pc0FycmF5KHBhcnNlZENvbnRlbnQucXVlc3Rpb25zKSB8fCBwYXJzZWRDb250ZW50LnF1ZXN0aW9ucy5sZW5ndGggIT09IDYwKSB7XG4gICAgICBlcnJvcnMucHVzaCgn6Zeu5Y235b+F6aG75YyF5ZCrNjDpgZPpopgnKVxuICAgIH1cbiAgICBcbiAgICBpZiAoZXJyb3JzLmxlbmd0aCA+IDApIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihg6Zeu5Y236Kej5p6Q5aSx6LSl77yaJHtlcnJvcnMuam9pbignLCAnKX1gKVxuICAgIH1cbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgaWQ6IHBhcnNlZENvbnRlbnQuaWQsXG4gICAgICB0aXRsZTogcGFyc2VkQ29udGVudC50aXRsZSxcbiAgICAgIGRlc2NyaXB0aW9uOiBwYXJzZWRDb250ZW50LmRlc2NyaXB0aW9uLFxuICAgICAgdmVyc2lvbjogcGFyc2VkQ29udGVudC52ZXJzaW9uLFxuICAgICAgb3JnYW5pemF0aW9uVHlwZTogcGFyc2VkQ29udGVudC5vcmdhbml6YXRpb25UeXBlLFxuICAgICAgYXNzZXNzbWVudFR5cGU6IHBhcnNlZENvbnRlbnQuYXNzZXNzbWVudFR5cGUsXG4gICAgICBkaW1lbnNpb25zOiBwYXJzZWRDb250ZW50LmRpbWVuc2lvbnMsXG4gICAgICBxdWVzdGlvbnM6IHBhcnNlZENvbnRlbnQucXVlc3Rpb25zLm1hcCgocTogYW55LCBpbmRleDogbnVtYmVyKSA9PiAoe1xuICAgICAgICBpZDogcS5pZCB8fCBgcV8ke2luZGV4ICsgMX1gLFxuICAgICAgICBkaW1lbnNpb246IHEuZGltZW5zaW9uLFxuICAgICAgICB0eXBlOiBxLnR5cGUsXG4gICAgICAgIHRpdGxlOiBxLnRpdGxlLFxuICAgICAgICBkZXNjcmlwdGlvbjogcS5kZXNjcmlwdGlvbiB8fCAnJyxcbiAgICAgICAgb3B0aW9uczogcS5vcHRpb25zLm1hcCgobzogYW55LCBvcHRpb25JbmRleDogbnVtYmVyKSA9PiAoe1xuICAgICAgICAgIGlkOiBvLmlkIHx8IGBvXyR7aW5kZXggKyAxfV8ke29wdGlvbkluZGV4ICsgMX1gLFxuICAgICAgICAgIHRleHQ6IG8udGV4dCxcbiAgICAgICAgICBzY29yZTogby5zY29yZVxuICAgICAgICB9KSksXG4gICAgICAgIHJlcXVpcmVkOiBxLnJlcXVpcmVkIHx8IHRydWUsXG4gICAgICAgIG9yZGVyOiBxLm9yZGVyIHx8IGluZGV4ICsgMVxuICAgICAgfSkpLFxuICAgICAgdG90YWxRdWVzdGlvbnM6IHBhcnNlZENvbnRlbnQucXVlc3Rpb25zLmxlbmd0aCxcbiAgICAgIGVzdGltYXRlZFRpbWU6IHRoaXMuY2FsY3VsYXRlRXN0aW1hdGVkVGltZShwYXJzZWRDb250ZW50LnF1ZXN0aW9ucyksXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgIGdlbmVyYXRpb25UaW1lOiAnJyxcbiAgICAgICAgbW9kZWw6ICcnLFxuICAgICAgICBwcm92aWRlcjogJydcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5pS56L+b55qE57uT5p6E5YyW6Kej5p6QXG4gICAqL1xuICBwcml2YXRlIHBhcnNlU3RydWN0dXJlZFJlc3BvbnNlKGNvbnRlbnQ6IHN0cmluZywgb3B0aW9uczogRGVzaWduT3B0aW9ucyk6IFF1ZXN0aW9ubmFpcmUge1xuICAgIGNvbnNvbGUubG9nKCflvIDlp4vnu5PmnoTljJbop6PmnpDvvIzlhoXlrrnplb/luqY6JywgY29udGVudC5sZW5ndGgpXG4gICAgXG4gICAgY29uc3QgcmVzdWx0OiBRdWVzdGlvbm5haXJlID0ge1xuICAgICAgaWQ6IGBxdWVzdGlvbm5haXJlXyR7RGF0ZS5ub3coKX1gLFxuICAgICAgdGl0bGU6IGAke29wdGlvbnMub3JnYW5pemF0aW9uVHlwZX3nu4Tnu4dPQ1RJ6K+E5Lyw6Zeu5Y23YCxcbiAgICAgIGRlc2NyaXB0aW9uOiBg6ZKI5a+5JHtvcHRpb25zLm9yZ2FuaXphdGlvblR5cGV957G75Z6L57uE57uH55qET0NUSeiDveWKm+ivhOS8sOmXruWNt2AsXG4gICAgICB2ZXJzaW9uOiBvcHRpb25zLnZlcnNpb24sXG4gICAgICBvcmdhbml6YXRpb25UeXBlOiBvcHRpb25zLm9yZ2FuaXphdGlvblR5cGUsXG4gICAgICBhc3Nlc3NtZW50VHlwZTogb3B0aW9ucy52ZXJzaW9uID09PSAncHJvZmVzc2lvbmFsJyA/ICdwcm9mZXNzaW9uYWwnIDogJ2Jhc2ljJyxcbiAgICAgIGRpbWVuc2lvbnM6IG9wdGlvbnMuZGltZW5zaW9ucyxcbiAgICAgIHF1ZXN0aW9uczogW10sXG4gICAgICB0b3RhbFF1ZXN0aW9uczogMCxcbiAgICAgIGVzdGltYXRlZFRpbWU6ICcnLFxuICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICBtZXRhZGF0YToge1xuICAgICAgICBnZW5lcmF0aW9uVGltZTogJycsXG4gICAgICAgIG1vZGVsOiAnJyxcbiAgICAgICAgcHJvdmlkZXI6ICcnXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g5aaC5p6c57uT5p6E5YyW6Kej5p6Q5Lmf5aSx6LSl77yM55Sf5oiQ6buY6K6k6Zeu5Y23XG4gICAgaWYgKHJlc3VsdC5xdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7XG4gICAgICBjb25zb2xlLmxvZygn57uT5p6E5YyW6Kej5p6Q5aSx6LSl77yM55Sf5oiQ6buY6K6k6Zeu5Y23JylcbiAgICAgIHJlc3VsdC5xdWVzdGlvbnMgPSB0aGlzLmdlbmVyYXRlRGVmYXVsdFF1ZXN0aW9ucyhvcHRpb25zKVxuICAgIH1cblxuICAgIHJlc3VsdC50b3RhbFF1ZXN0aW9ucyA9IHJlc3VsdC5xdWVzdGlvbnMubGVuZ3RoXG4gICAgcmVzdWx0LmVzdGltYXRlZFRpbWUgPSB0aGlzLmNhbGN1bGF0ZUVzdGltYXRlZFRpbWUocmVzdWx0LnF1ZXN0aW9ucylcblxuICAgIGNvbnNvbGUubG9nKCfnu5PmnoTljJbop6PmnpDlrozmiJDvvIzpl67popjmlbDph486JywgcmVzdWx0LnF1ZXN0aW9ucy5sZW5ndGgpXG4gICAgcmV0dXJuIHJlc3VsdFxuICB9XG5cbiAgcHJpdmF0ZSBleHRyYWN0VmFsdWUobGluZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBjb25zdCBjb2xvbkluZGV4ID0gbGluZS5pbmRleE9mKCc6JylcbiAgICByZXR1cm4gY29sb25JbmRleCA+IC0xID8gbGluZS5zdWJzdHJpbmcoY29sb25JbmRleCArIDEpLnRyaW0oKSA6IGxpbmVcbiAgfVxuXG4gIC8vIOino+aekOmXrumimOWIl+ihqFxuICBwcml2YXRlIHBhcnNlUXVlc3Rpb25zKHJhd1F1ZXN0aW9uczogYW55W10pOiBRdWVzdGlvbltdIHtcbiAgICByZXR1cm4gcmF3UXVlc3Rpb25zLm1hcCgocmF3USwgaW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IHF1ZXN0aW9uOiBRdWVzdGlvbiA9IHtcbiAgICAgICAgaWQ6IHJhd1EuaWQgfHwgYHFfJHtpbmRleCArIDF9YCxcbiAgICAgICAgZGltZW5zaW9uOiByYXdRLmRpbWVuc2lvbiB8fCB0aGlzLmluZmVyRGltZW5zaW9uKHJhd1EudGV4dCksXG4gICAgICAgIHN1YmRpbWVuc2lvbjogcmF3US5zdWJkaW1lbnNpb24gfHwgJycsXG4gICAgICAgIHR5cGU6IHJhd1EudHlwZSB8fCB0aGlzLmluZmVyUXVlc3Rpb25UeXBlKHJhd1EudGV4dCksXG4gICAgICAgIGRlcHRoOiByYXdRLmRlcHRoIHx8ICdpbnRlcm1lZGlhdGUnLFxuICAgICAgICB0ZXh0OiByYXdRLnRleHQgfHwgcmF3US5xdWVzdGlvbiB8fCAnJyxcbiAgICAgICAgb3B0aW9uczogcmF3US5vcHRpb25zLFxuICAgICAgICBzY2FsZTogcmF3US5zY2FsZSxcbiAgICAgICAgcmV2ZXJzZWQ6IHJhd1EucmV2ZXJzZWQgfHwgZmFsc2UsXG4gICAgICAgIHdlaWdodDogcmF3US53ZWlnaHQgfHwgMSxcbiAgICAgICAgcmVxdWlyZWQ6IHJhd1EucmVxdWlyZWQgIT09IGZhbHNlLFxuICAgICAgICBvcmRlcjogcmF3US5vcmRlciB8fCBpbmRleCArIDEsXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgICAgZGlmZmljdWx0eTogcmF3US5kaWZmaWN1bHR5IHx8IDAuNSxcbiAgICAgICAgICBkaXNjcmltaW5hdGlvbkluZGV4OiByYXdRLmRpc2NyaW1pbmF0aW9uSW5kZXggfHwgMC41LFxuICAgICAgICAgIGV4cGVjdGVkUmVzcG9uc2VUaW1lOiByYXdRLmV4cGVjdGVkUmVzcG9uc2VUaW1lIHx8IDMwLFxuICAgICAgICB9LFxuICAgICAgfTtcblxuICAgICAgLy8g5Li66YCJ5oup6aKY55Sf5oiQ6buY6K6k6YCJ6aG5XG4gICAgICBpZiAoKHF1ZXN0aW9uLnR5cGUgPT09ICdzaW5nbGVfY2hvaWNlJyB8fCBxdWVzdGlvbi50eXBlID09PSAnbXVsdGlwbGVfY2hvaWNlJykgJiYgIXF1ZXN0aW9uLm9wdGlvbnMpIHtcbiAgICAgICAgcXVlc3Rpb24ub3B0aW9ucyA9IHRoaXMuZ2VuZXJhdGVEZWZhdWx0T3B0aW9ucyhxdWVzdGlvbi50eXBlKTtcbiAgICAgIH1cblxuICAgICAgLy8g5Li66YeP6KGo6aKY55Sf5oiQ6buY6K6k6YeP6KGoXG4gICAgICBpZiAocXVlc3Rpb24udHlwZSA9PT0gJ2xpa2VydF9zY2FsZScgJiYgIXF1ZXN0aW9uLnNjYWxlKSB7XG4gICAgICAgIHF1ZXN0aW9uLnNjYWxlID0ge1xuICAgICAgICAgIG1pbjogMSxcbiAgICAgICAgICBtYXg6IDUsXG4gICAgICAgICAgbGFiZWxzOiBbJ+WujOWFqOS4jeWQjOaEjycsICfkuI3lkIzmhI8nLCAn5Lit56uLJywgJ+WQjOaEjycsICflrozlhajlkIzmhI8nXSxcbiAgICAgICAgfTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHF1ZXN0aW9uO1xuICAgIH0pO1xuICB9XG5cbiAgLy8g5o6o5pat6Zeu6aKY57G75Z6LXG4gIHByaXZhdGUgaW5mZXJRdWVzdGlvblR5cGUodGV4dDogc3RyaW5nKTogUXVlc3Rpb25UeXBlIHtcbiAgICBpZiAodGV4dC5pbmNsdWRlcygn5o6S5bqPJykgfHwgdGV4dC5pbmNsdWRlcygn5o6S5YiXJykpIHtcbiAgICAgIHJldHVybiAncmFua2luZyc7XG4gICAgfVxuICAgIGlmICh0ZXh0LmluY2x1ZGVzKCflpJrpgIknKSB8fCB0ZXh0LmluY2x1ZGVzKCflj6/ku6XpgInmi6nlpJrkuKonKSkge1xuICAgICAgcmV0dXJuICdtdWx0aXBsZV9jaG9pY2UnO1xuICAgIH1cbiAgICBpZiAodGV4dC5pbmNsdWRlcygn5ZCM5oSPJykgfHwgdGV4dC5pbmNsdWRlcygn56iL5bqmJykpIHtcbiAgICAgIHJldHVybiAnbGlrZXJ0X3NjYWxlJztcbiAgICB9XG4gICAgaWYgKHRleHQuaW5jbHVkZXMoJ+aPj+i/sCcpIHx8IHRleHQuaW5jbHVkZXMoJ+ivtOaYjicpIHx8IHRleHQuaW5jbHVkZXMoJ+S4vuS+iycpKSB7XG4gICAgICByZXR1cm4gJ29wZW5fZW5kZWQnO1xuICAgIH1cbiAgICByZXR1cm4gJ3NpbmdsZV9jaG9pY2UnO1xuICB9XG5cbiAgLy8g5o6o5pat57u05bqmXG4gIHByaXZhdGUgaW5mZXJEaW1lbnNpb24odGV4dDogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBpZiAodGV4dC5pbmNsdWRlcygn5p2D5YqbJykgfHwgdGV4dC5pbmNsdWRlcygn562J57qnJykgfHwgdGV4dC5pbmNsdWRlcygn5bGC57qnJykpIHtcbiAgICAgIHJldHVybiAn5p2D5Yqb6Led56a7JztcbiAgICB9XG4gICAgaWYgKHRleHQuaW5jbHVkZXMoJ+S4quS6uicpIHx8IHRleHQuaW5jbHVkZXMoJ+mbhuS9kycpIHx8IHRleHQuaW5jbHVkZXMoJ+WboumYnycpKSB7XG4gICAgICByZXR1cm4gJ+S4quS6uuS4u+S5iXZz6ZuG5L2T5Li75LmJJztcbiAgICB9XG4gICAgaWYgKHRleHQuaW5jbHVkZXMoJ+ernuS6iScpIHx8IHRleHQuaW5jbHVkZXMoJ+WQiOS9nCcpIHx8IHRleHQuaW5jbHVkZXMoJ+aIkOWwsScpKSB7XG4gICAgICByZXR1cm4gJ+eUt+aAp+WMlnZz5aWz5oCn5YyWJztcbiAgICB9XG4gICAgaWYgKHRleHQuaW5jbHVkZXMoJ+S4jeehruWumicpIHx8IHRleHQuaW5jbHVkZXMoJ+mjjumZqScpIHx8IHRleHQuaW5jbHVkZXMoJ+WPmOWMlicpKSB7XG4gICAgICByZXR1cm4gJ+S4jeehruWumuaAp+inhOmBvyc7XG4gICAgfVxuICAgIHJldHVybiAn57u85ZCIJztcbiAgfVxuXG4gIC8vIOeUn+aIkOm7mOiupOmAiemhuVxuICBwcml2YXRlIGdlbmVyYXRlRGVmYXVsdE9wdGlvbnModHlwZTogUXVlc3Rpb25UeXBlKTogUXVlc3Rpb25PcHRpb25bXSB7XG4gICAgaWYgKHR5cGUgPT09ICdzaW5nbGVfY2hvaWNlJyB8fCB0eXBlID09PSAnbXVsdGlwbGVfY2hvaWNlJykge1xuICAgICAgcmV0dXJuIFtcbiAgICAgICAgeyBpZDogJ29wdF8xJywgdGV4dDogJ+WujOWFqOS4jeespuWQiCcsIHNjb3JlOiAxIH0sXG4gICAgICAgIHsgaWQ6ICdvcHRfMicsIHRleHQ6ICfln7rmnKzkuI3nrKblkIgnLCBzY29yZTogMiB9LFxuICAgICAgICB7IGlkOiAnb3B0XzMnLCB0ZXh0OiAn6YOo5YiG56ym5ZCIJywgc2NvcmU6IDMgfSxcbiAgICAgICAgeyBpZDogJ29wdF80JywgdGV4dDogJ+WfuuacrOespuWQiCcsIHNjb3JlOiA0IH0sXG4gICAgICAgIHsgaWQ6ICdvcHRfNScsIHRleHQ6ICflrozlhajnrKblkIgnLCBzY29yZTogNSB9LFxuICAgICAgXTtcbiAgICB9XG4gICAgcmV0dXJuIFtdO1xuICB9XG5cbiAgLy8g6aqM6K+B6Zeu5Y23XG4gIHByaXZhdGUgYXN5bmMgdmFsaWRhdGVRdWVzdGlvbm5haXJlKHF1ZXN0aW9ubmFpcmU6IFF1ZXN0aW9ubmFpcmUpOiBQcm9taXNlPFZhbGlkYXRpb25SZXN1bHQ+IHtcbiAgICBjb25zdCBlcnJvcnM6IHN0cmluZ1tdID0gW107XG4gICAgY29uc3Qgd2FybmluZ3M6IHN0cmluZ1tdID0gW107XG4gICAgY29uc3Qgc3VnZ2VzdGlvbnM6IHN0cmluZ1tdID0gW107XG5cbiAgICAvLyDln7rmnKzpqozor4FcbiAgICBpZiAoIXF1ZXN0aW9ubmFpcmUudGl0bGUpIHtcbiAgICAgIGVycm9ycy5wdXNoKCfpl67ljbfmoIfpopjkuI3og73kuLrnqbonKTtcbiAgICB9XG4gICAgXG4gICAgaWYgKHF1ZXN0aW9ubmFpcmUucXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgZXJyb3JzLnB1c2goJ+mXruWNt+W/hemhu+WMheWQq+iHs+WwkeS4gOS4qumXrumimCcpO1xuICAgIH1cblxuICAgIC8vIOmXrumimOaVsOmHj+mqjOivgVxuICAgIGNvbnN0IGV4cGVjdGVkUXVlc3Rpb25zID0gcXVlc3Rpb25uYWlyZS52ZXJzaW9uID09PSAncHJvZmVzc2lvbmFsJyA/IDQwIDogMjA7XG4gICAgaWYgKHF1ZXN0aW9ubmFpcmUucXVlc3Rpb25zLmxlbmd0aCA8IGV4cGVjdGVkUXVlc3Rpb25zICogMC44KSB7XG4gICAgICB3YXJuaW5ncy5wdXNoKGDpl67popjmlbDph4/lgY/lsJHvvIzlu7rorq7oh7PlsJEke2V4cGVjdGVkUXVlc3Rpb25zfeS4qumXrumimGApO1xuICAgIH1cblxuICAgIC8vIOe7tOW6puimhueblumqjOivgVxuICAgIGNvbnN0IGRpbWVuc2lvbnMgPSBuZXcgU2V0KHF1ZXN0aW9ubmFpcmUucXVlc3Rpb25zLm1hcChxID0+IHEuZGltZW5zaW9uKSk7XG4gICAgaWYgKGRpbWVuc2lvbnMuc2l6ZSA8IDQpIHtcbiAgICAgIGVycm9ycy5wdXNoKCfpl67ljbflupTopobnm5ZPQ1RJ5Zub5Liq57u05bqmJyk7XG4gICAgfVxuXG4gICAgLy8g6Zeu6aKY57G75Z6L5YiG5biD6aqM6K+BXG4gICAgY29uc3QgdHlwZURpc3RyaWJ1dGlvbiA9IHRoaXMuYW5hbHl6ZVF1ZXN0aW9uVHlwZXMocXVlc3Rpb25uYWlyZS5xdWVzdGlvbnMpO1xuICAgIGlmICh0eXBlRGlzdHJpYnV0aW9uLmxpa2VydF9zY2FsZSA8IDAuNSkge1xuICAgICAgc3VnZ2VzdGlvbnMucHVzaCgn5bu66K6u5aKe5Yqg5pu05aSa6YeP6KGo6aKY5Lul5o+Q6auY5rWL6YeP57K+5bqmJyk7XG4gICAgfVxuXG4gICAgLy8g6Zeu6aKY6LSo6YeP6aqM6K+BXG4gICAgZm9yIChjb25zdCBxdWVzdGlvbiBvZiBxdWVzdGlvbm5haXJlLnF1ZXN0aW9ucykge1xuICAgICAgaWYgKCFxdWVzdGlvbi50ZXh0IHx8IHF1ZXN0aW9uLnRleHQubGVuZ3RoIDwgMTApIHtcbiAgICAgICAgZXJyb3JzLnB1c2goYOmXrumimCR7cXVlc3Rpb24uaWR95YaF5a656L+H55+tYCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGlmIChxdWVzdGlvbi50eXBlID09PSAnc2luZ2xlX2Nob2ljZScgJiYgKCFxdWVzdGlvbi5vcHRpb25zIHx8IHF1ZXN0aW9uLm9wdGlvbnMubGVuZ3RoIDwgMykpIHtcbiAgICAgICAgZXJyb3JzLnB1c2goYOmXrumimCR7cXVlc3Rpb24uaWR96YCJ6aG55LiN6LazYCk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g6K6h566X6LSo6YeP5YiG5pWwXG4gICAgbGV0IHF1YWxpdHlTY29yZSA9IDEuMDtcbiAgICBxdWFsaXR5U2NvcmUgLT0gZXJyb3JzLmxlbmd0aCAqIDAuMjtcbiAgICBxdWFsaXR5U2NvcmUgLT0gd2FybmluZ3MubGVuZ3RoICogMC4xO1xuICAgIHF1YWxpdHlTY29yZSA9IE1hdGgubWF4KDAsIHF1YWxpdHlTY29yZSk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgaXNWYWxpZDogZXJyb3JzLmxlbmd0aCA9PT0gMCxcbiAgICAgIGVycm9ycyxcbiAgICAgIHdhcm5pbmdzLFxuICAgICAgc3VnZ2VzdGlvbnMsXG4gICAgICBzY29yZTogcXVhbGl0eVNjb3JlLFxuICAgICAgcXVhbGl0eVNjb3JlLFxuICAgIH07XG4gIH1cblxuICAvLyDph43mlrDnlJ/miJDpl67ljbdcbiAgcHJpdmF0ZSBhc3luYyByZWdlbmVyYXRlUXVlc3Rpb25uYWlyZShcbiAgICBvcHRpb25zOiBEZXNpZ25PcHRpb25zLFxuICAgIHN1Z2dlc3Rpb25zOiBzdHJpbmdbXVxuICApOiBQcm9taXNlPFF1ZXN0aW9ubmFpcmU+IHtcbiAgICBsb2dnZXIuaW5mbygnUmVnZW5lcmF0aW5nIHF1ZXN0aW9ubmFpcmUgd2l0aCBpbXByb3ZlbWVudHMnLCB7IHN1Z2dlc3Rpb25zIH0pO1xuICAgIFxuICAgIC8vIOa3u+WKoOaUuei/m+W7uuiuruWIsOiHquWumuS5ieimgeaxglxuICAgIGNvbnN0IGltcHJvdmVkT3B0aW9ucyA9IHtcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICBjdXN0b21SZXF1aXJlbWVudHM6IFtcbiAgICAgICAgb3B0aW9ucy5jdXN0b21SZXF1aXJlbWVudHMgfHwgJycsXG4gICAgICAgICfor7fnibnliKvms6jmhI/ku6XkuIvmlLnov5vlu7rorq7vvJonLFxuICAgICAgICAuLi5zdWdnZXN0aW9ucyxcbiAgICAgIF0uZmlsdGVyKEJvb2xlYW4pLmpvaW4oJ1xcbicpLFxuICAgIH07XG5cbiAgICAvLyDpgJLlvZLosIPnlKjvvIzkvYbpmZDliLbph43or5XmrKHmlbBcbiAgICByZXR1cm4gdGhpcy5kZXNpZ24oaW1wcm92ZWRPcHRpb25zKTtcbiAgfVxuXG4gIC8vIOWIhuaekOmXrumimOexu+Wei+WIhuW4g1xuICBwcml2YXRlIGFuYWx5emVRdWVzdGlvblR5cGVzKHF1ZXN0aW9uczogUXVlc3Rpb25bXSk6IFJlY29yZDxRdWVzdGlvblR5cGUsIG51bWJlcj4ge1xuICAgIGNvbnN0IGRpc3RyaWJ1dGlvbjogUmVjb3JkPFF1ZXN0aW9uVHlwZSwgbnVtYmVyPiA9IHtcbiAgICAgIHNpbmdsZV9jaG9pY2U6IDAsXG4gICAgICBtdWx0aXBsZV9jaG9pY2U6IDAsXG4gICAgICBzY2FsZTogMCxcbiAgICAgIG9wZW5fZW5kZWQ6IDAsXG4gICAgICByYW5raW5nOiAwLFxuICAgICAgbWF0cml4OiAwLFxuICAgICAgbGlrZXJ0X3NjYWxlOiAwLFxuICAgICAgY2hvaWNlOiAwLFxuICAgICAgc2NlbmFyaW86IDAsXG4gICAgfTtcblxuICAgIHF1ZXN0aW9ucy5mb3JFYWNoKHEgPT4ge1xuICAgICAgZGlzdHJpYnV0aW9uW3EudHlwZV0gPSAoZGlzdHJpYnV0aW9uW3EudHlwZV0gfHwgMCkgKyAxO1xuICAgIH0pO1xuXG4gICAgLy8g6L2s5o2i5Li65q+U5L6LXG4gICAgY29uc3QgdG90YWwgPSBxdWVzdGlvbnMubGVuZ3RoO1xuICAgIE9iamVjdC5rZXlzKGRpc3RyaWJ1dGlvbikuZm9yRWFjaChrZXkgPT4ge1xuICAgICAgZGlzdHJpYnV0aW9uW2tleSBhcyBRdWVzdGlvblR5cGVdID0gZGlzdHJpYnV0aW9uW2tleSBhcyBRdWVzdGlvblR5cGVdIC8gdG90YWw7XG4gICAgfSk7XG5cbiAgICByZXR1cm4gZGlzdHJpYnV0aW9uO1xuICB9XG5cbiAgLy8g6K6h566X6aKE5Lyw5pe26Ze0XG4gIHByaXZhdGUgY2FsY3VsYXRlRXN0aW1hdGVkVGltZShxdWVzdGlvbnM6IGFueVtdKTogc3RyaW5nIHtcbiAgICBjb25zdCB0b3RhbFNlY29uZHMgPSBxdWVzdGlvbnMucmVkdWNlKCh0b3RhbCwgcSkgPT4ge1xuICAgICAgY29uc3QgYmFzZVRpbWUgPSBxLnR5cGUgPT09ICdvcGVuX2VuZGVkJyA/IDYwIDogMzA7XG4gICAgICByZXR1cm4gdG90YWwgKyAocS5leHBlY3RlZFJlc3BvbnNlVGltZSB8fCBiYXNlVGltZSk7XG4gICAgfSwgMCk7XG5cbiAgICBjb25zdCBtaW51dGVzID0gTWF0aC5jZWlsKHRvdGFsU2Vjb25kcyAvIDYwKTtcbiAgICByZXR1cm4gYCR7bWludXRlc33liIbpkp9gO1xuICB9XG5cbiAgLy8g6K6h566X6Zq+5bqmXG4gIHByaXZhdGUgY2FsY3VsYXRlRGlmZmljdWx0eShxdWVzdGlvbnM6IGFueVtdKTogbnVtYmVyIHtcbiAgICBpZiAocXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkgcmV0dXJuIDAuNTtcbiAgICBcbiAgICBjb25zdCBhdmdEaWZmaWN1bHR5ID0gcXVlc3Rpb25zLnJlZHVjZSgoc3VtLCBxKSA9PiB7XG4gICAgICByZXR1cm4gc3VtICsgKHEuZGlmZmljdWx0eSB8fCAwLjUpO1xuICAgIH0sIDApIC8gcXVlc3Rpb25zLmxlbmd0aDtcbiAgICBcbiAgICByZXR1cm4gYXZnRGlmZmljdWx0eTtcbiAgfVxuXG4gIC8vIOeUn+aIkOmXruWNt0lEXG4gIHByaXZhdGUgZ2VuZXJhdGVRdWVzdGlvbm5haXJlSWQoKTogc3RyaW5nIHtcbiAgICByZXR1cm4gYHF1ZXN0aW9ubmFpcmVfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gO1xuICB9XG5cbiAgLy8g55Sf5oiQ57yT5a2Y6ZSuXG4gIHByaXZhdGUgZ2VuZXJhdGVDYWNoZUtleShvcHRpb25zOiBEZXNpZ25PcHRpb25zKTogc3RyaW5nIHtcbiAgICBjb25zdCBrZXlQYXJ0cyA9IFtcbiAgICAgIG9wdGlvbnMudmVyc2lvbixcbiAgICAgIG9wdGlvbnMub3JnYW5pemF0aW9uVHlwZSB8fCAnZGVmYXVsdCcsXG4gICAgICBvcHRpb25zLmluZHVzdHJ5Q29udGV4dCB8fCAnZGVmYXVsdCcsXG4gICAgICBvcHRpb25zLnRhcmdldEF1ZGllbmNlIHx8ICdkZWZhdWx0JyxcbiAgICAgIG9wdGlvbnMuY3VzdG9tUmVxdWlyZW1lbnRzIHx8ICdkZWZhdWx0JyxcbiAgICBdO1xuICAgIFxuICAgIHJldHVybiBrZXlQYXJ0cy5qb2luKCdfJykucmVwbGFjZSgvW15hLXpBLVowLTlfXS9nLCAnXycpO1xuICB9XG5cbiAgLy8g6I635Y+W6Zeu5Y236aKE6KeIXG4gIGFzeW5jIGdldFF1ZXN0aW9ubmFpcmVQcmV2aWV3KHF1ZXN0aW9ubmFpcmVJZDogc3RyaW5nKTogUHJvbWlzZTx7XG4gICAgdGl0bGU6IHN0cmluZztcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICAgIHF1ZXN0aW9uQ291bnQ6IG51bWJlcjtcbiAgICBlc3RpbWF0ZWRUaW1lOiBudW1iZXI7XG4gICAgZGltZW5zaW9uczogc3RyaW5nW107XG4gIH0gfCBudWxsPiB7XG4gICAgLy8g5LuO57yT5a2Y5Lit5p+l5om+XG4gICAgZm9yIChjb25zdCBxdWVzdGlvbm5haXJlIG9mIEFycmF5LmZyb20odGhpcy5xdWVzdGlvbkNhY2hlLnZhbHVlcygpKSkge1xuICAgICAgaWYgKHF1ZXN0aW9ubmFpcmUuaWQgPT09IHF1ZXN0aW9ubmFpcmVJZCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHRpdGxlOiBxdWVzdGlvbm5haXJlLnRpdGxlLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBxdWVzdGlvbm5haXJlLmRlc2NyaXB0aW9uLFxuICAgICAgICAgIHF1ZXN0aW9uQ291bnQ6IHF1ZXN0aW9ubmFpcmUucXVlc3Rpb25zLmxlbmd0aCxcbiAgICAgICAgICBlc3RpbWF0ZWRUaW1lOiBwYXJzZUludChxdWVzdGlvbm5haXJlLmVzdGltYXRlZFRpbWUpIHx8IDIwLFxuICAgICAgICAgIGRpbWVuc2lvbnM6IEFycmF5LmZyb20obmV3IFNldChxdWVzdGlvbm5haXJlLnF1ZXN0aW9ucy5tYXAocSA9PiBxLmRpbWVuc2lvbikpKSxcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICAvLyDmuIXpmaTnvJPlrZhcbiAgY2xlYXJDYWNoZSgpOiB2b2lkIHtcbiAgICB0aGlzLnF1ZXN0aW9uQ2FjaGUuY2xlYXIoKTtcbiAgICBsb2dnZXIuaW5mbygnUXVlc3Rpb25EZXNpZ25lckFnZW50IGNhY2hlIGNsZWFyZWQnKTtcbiAgfVxuXG4gIC8vIOiOt+WPlue7n+iuoeS/oeaBr1xuICBnZXRTdGF0cygpOiB7XG4gICAgY2FjaGVTaXplOiBudW1iZXI7XG4gICAgdG90YWxRdWVzdGlvbm5haXJlczogbnVtYmVyO1xuICAgIHZlcnNpb25EaXN0cmlidXRpb246IFJlY29yZDxzdHJpbmcsIG51bWJlcj47XG4gIH0ge1xuICAgIGNvbnN0IHF1ZXN0aW9ubmFpcmVzID0gQXJyYXkuZnJvbSh0aGlzLnF1ZXN0aW9uQ2FjaGUudmFsdWVzKCkpO1xuICAgIGNvbnN0IHZlcnNpb25EaXN0cmlidXRpb246IFJlY29yZDxzdHJpbmcsIG51bWJlcj4gPSB7fTtcbiAgICBcbiAgICBxdWVzdGlvbm5haXJlcy5mb3JFYWNoKChxOiBRdWVzdGlvbm5haXJlKSA9PiB7XG4gICAgICB2ZXJzaW9uRGlzdHJpYnV0aW9uW3EudmVyc2lvbl0gPSAodmVyc2lvbkRpc3RyaWJ1dGlvbltxLnZlcnNpb25dIHx8IDApICsgMTtcbiAgICB9KTtcbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgY2FjaGVTaXplOiB0aGlzLnF1ZXN0aW9uQ2FjaGUuc2l6ZSxcbiAgICAgIHRvdGFsUXVlc3Rpb25uYWlyZXM6IHF1ZXN0aW9ubmFpcmVzLmxlbmd0aCxcbiAgICAgIHZlcnNpb25EaXN0cmlidXRpb24sXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiDliIbmibnnlJ/miJDpl67ljbcgLSDmoLjlv4Plrp7njrBcbiAgICovXG4gIGFzeW5jIGdlbmVyYXRlUXVlc3Rpb25uYWlyZUJhdGNoZWQob3B0aW9uczogRGVzaWduT3B0aW9ucyk6IFByb21pc2U8UXVlc3Rpb25uYWlyZT4ge1xuICAgIGNvbnN0IGRpbWVuc2lvbnMgPSBbJ1MvRicsICdJL1QnLCAnTS9WJywgJ0EvRCddXG4gICAgY29uc3QgcXVlc3Rpb25zUGVyQmF0Y2ggPSA1IC8vIOavj+aJueeUn+aIkDXpgZPpophcbiAgICBjb25zdCBxdWVzdGlvbnNQZXJEaW1lbnNpb24gPSAxNVxuXG4gICAgLy8g5Yib5bu65Z+656GA6Zeu5Y2357uT5p6EXG4gICAgY29uc3QgcXVlc3Rpb25uYWlyZTogUXVlc3Rpb25uYWlyZSA9IHtcbiAgICAgIGlkOiBgcXVlc3Rpb25uYWlyZV8ke0RhdGUubm93KCl9YCxcbiAgICAgIHRpdGxlOiBgT0NUSeaZuuiDveivhOS8sCAtICR7b3B0aW9ucy5vcmdhbml6YXRpb25UeXBlfWAsXG4gICAgICBkZXNjcmlwdGlvbjogYOWfuuS6jk9DVEnlm5vnu7TlhavmnoHnkIborrrnmoQke29wdGlvbnMub3JnYW5pemF0aW9uVHlwZX3nu4Tnu4fog73lipvor4TkvLBgLFxuICAgICAgdmVyc2lvbjogb3B0aW9ucy52ZXJzaW9uLFxuICAgICAgb3JnYW5pemF0aW9uVHlwZTogb3B0aW9ucy5vcmdhbml6YXRpb25UeXBlLFxuICAgICAgYXNzZXNzbWVudFR5cGU6IG9wdGlvbnMudmVyc2lvbiA9PT0gJ3Byb2Zlc3Npb25hbCcgPyAncHJvZmVzc2lvbmFsJyA6ICdiYXNpYycsXG4gICAgICBkaW1lbnNpb25zOiBkaW1lbnNpb25zLFxuICAgICAgcXVlc3Rpb25zOiBbXSxcbiAgICAgIHRvdGFsUXVlc3Rpb25zOiA2MCxcbiAgICAgIGVzdGltYXRlZFRpbWU6ICcyMOWIhumSnycsXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH1cblxuICAgIC8vIOS4uuavj+S4que7tOW6puWIhuaJueeUn+aIkOmimOebrlxuICAgIGZvciAoY29uc3QgZGltZW5zaW9uIG9mIGRpbWVuc2lvbnMpIHtcbiAgICAgIGxvZ2dlci5pbmZvKGDlvIDlp4vnlJ/miJDnu7TluqYgJHtkaW1lbnNpb259IOeahOmimOebrmApXG5cbiAgICAgIGZvciAobGV0IGJhdGNoID0gMDsgYmF0Y2ggPCBNYXRoLmNlaWwocXVlc3Rpb25zUGVyRGltZW5zaW9uIC8gcXVlc3Rpb25zUGVyQmF0Y2gpOyBiYXRjaCsrKSB7XG4gICAgICAgIGNvbnN0IHN0YXJ0SW5kZXggPSBiYXRjaCAqIHF1ZXN0aW9uc1BlckJhdGNoXG4gICAgICAgIGNvbnN0IGVuZEluZGV4ID0gTWF0aC5taW4oc3RhcnRJbmRleCArIHF1ZXN0aW9uc1BlckJhdGNoLCBxdWVzdGlvbnNQZXJEaW1lbnNpb24pXG4gICAgICAgIGNvbnN0IHF1ZXN0aW9uc0luQmF0Y2ggPSBlbmRJbmRleCAtIHN0YXJ0SW5kZXhcblxuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGJhdGNoUXVlc3Rpb25zID0gYXdhaXQgdGhpcy5nZW5lcmF0ZVF1ZXN0aW9uQmF0Y2goXG4gICAgICAgICAgICBkaW1lbnNpb24sXG4gICAgICAgICAgICBxdWVzdGlvbnNJbkJhdGNoLFxuICAgICAgICAgICAgc3RhcnRJbmRleCArIDEsXG4gICAgICAgICAgICBvcHRpb25zXG4gICAgICAgICAgKVxuXG4gICAgICAgICAgcXVlc3Rpb25uYWlyZS5xdWVzdGlvbnMucHVzaCguLi5iYXRjaFF1ZXN0aW9ucylcbiAgICAgICAgICBsb2dnZXIuaW5mbyhg57u05bqmICR7ZGltZW5zaW9ufSDnrKwgJHtiYXRjaCArIDF9IOaJuemimOebrueUn+aIkOWujOaIkGAsIHtcbiAgICAgICAgICAgIGJhdGNoU2l6ZTogYmF0Y2hRdWVzdGlvbnMubGVuZ3RoLFxuICAgICAgICAgICAgdG90YWxHZW5lcmF0ZWQ6IHF1ZXN0aW9ubmFpcmUucXVlc3Rpb25zLmxlbmd0aFxuICAgICAgICAgIH0pXG5cbiAgICAgICAgICAvLyDnvJPlrZjlvZPliY3ov5vluqZcbiAgICAgICAgICB0aGlzLnF1ZXN0aW9uQ2FjaGUuc2V0KHF1ZXN0aW9ubmFpcmUuaWQsIHF1ZXN0aW9ubmFpcmUpXG5cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBsb2dnZXIud2Fybihg57u05bqmICR7ZGltZW5zaW9ufSDnrKwgJHtiYXRjaCArIDF9IOaJueeUn+aIkOWksei0pe+8jOS9v+eUqOm7mOiupOmimOebrmAsIHsgZXJyb3IgfSlcblxuICAgICAgICAgIC8vIOeUn+aIkOm7mOiupOmimOebruS9nOS4uuWQjuWkh1xuICAgICAgICAgIGNvbnN0IGRlZmF1bHRRdWVzdGlvbnMgPSB0aGlzLmdlbmVyYXRlRGVmYXVsdFF1ZXN0aW9uc0JhdGNoKFxuICAgICAgICAgICAgZGltZW5zaW9uLFxuICAgICAgICAgICAgcXVlc3Rpb25zSW5CYXRjaCxcbiAgICAgICAgICAgIHN0YXJ0SW5kZXggKyAxXG4gICAgICAgICAgKVxuICAgICAgICAgIHF1ZXN0aW9ubmFpcmUucXVlc3Rpb25zLnB1c2goLi4uZGVmYXVsdFF1ZXN0aW9ucylcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOabtOaWsOacgOe7iOe7n+iuoVxuICAgIHF1ZXN0aW9ubmFpcmUudG90YWxRdWVzdGlvbnMgPSBxdWVzdGlvbm5haXJlLnF1ZXN0aW9ucy5sZW5ndGhcbiAgICBxdWVzdGlvbm5haXJlLmVzdGltYXRlZFRpbWUgPSB0aGlzLmNhbGN1bGF0ZUVzdGltYXRlZFRpbWUocXVlc3Rpb25uYWlyZS5xdWVzdGlvbnMpXG5cbiAgICByZXR1cm4gcXVlc3Rpb25uYWlyZVxuICB9XG5cbiAgLyoqXG4gICAqIOeUn+aIkOWNleaJueasoemimOebrlxuICAgKi9cbiAgYXN5bmMgZ2VuZXJhdGVRdWVzdGlvbkJhdGNoKFxuICAgIGRpbWVuc2lvbjogc3RyaW5nLFxuICAgIHF1ZXN0aW9uQ291bnQ6IG51bWJlcixcbiAgICBzdGFydEluZGV4OiBudW1iZXIsXG4gICAgb3B0aW9uczogRGVzaWduT3B0aW9uc1xuICApOiBQcm9taXNlPFF1ZXN0aW9uW10+IHtcbiAgICBjb25zdCBwcm9tcHQgPSB0aGlzLmJ1aWxkQmF0Y2hQcm9tcHQoZGltZW5zaW9uLCBxdWVzdGlvbkNvdW50LCBzdGFydEluZGV4LCBvcHRpb25zKVxuXG4gICAgY29uc3QgbGxtUmVxdWVzdDogTExNUmVxdWVzdCA9IHtcbiAgICAgIG1vZGVsOiAnZGVlcHNlZWstY2hhdCcsXG4gICAgICBtZXNzYWdlczogW1xuICAgICAgICB7IHJvbGU6ICdzeXN0ZW0nLCBjb250ZW50OiBwcm9tcHQuc3lzdGVtUHJvbXB0IH0sXG4gICAgICAgIHsgcm9sZTogJ3VzZXInLCBjb250ZW50OiBwcm9tcHQudXNlclByb21wdCB9XG4gICAgICBdLFxuICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgIG1heFRva2VuczogMzAwMCAvLyDovoPlsI/nmoR0b2tlbumZkOWItu+8jOWboOS4uuWPqueUn+aIkOWHoOmBk+mimFxuICAgIH1cblxuICAgIGNvbnN0IHByb3ZpZGVyID0gJ2RlZXBzZWVrJ1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5sbG1DbGllbnQuY2hhdChwcm92aWRlciwgbGxtUmVxdWVzdClcblxuICAgIHJldHVybiB0aGlzLnBhcnNlQmF0Y2hSZXNwb25zZShyZXNwb25zZS5jb250ZW50IHx8ICcnLCBkaW1lbnNpb24sIHN0YXJ0SW5kZXgpXG4gIH1cblxuICAvKipcbiAgICog5p6E5bu65om55qyh5o+Q56S66K+NXG4gICAqL1xuICBwcml2YXRlIGJ1aWxkQmF0Y2hQcm9tcHQoZGltZW5zaW9uOiBzdHJpbmcsIHF1ZXN0aW9uQ291bnQ6IG51bWJlciwgc3RhcnRJbmRleDogbnVtYmVyLCBvcHRpb25zOiBEZXNpZ25PcHRpb25zKSB7XG4gICAgY29uc3QgZGltZW5zaW9uRGVzY3JpcHRpb25zID0ge1xuICAgICAgJ1MvRic6ICfnu5PmnoTljJbkuI7ngbXmtLvmgKcgLSDor4TkvLDnu4Tnu4flnKjop4TojIPljJbnrqHnkIbkuI7ngbXmtLvlupTlj5jkuYvpl7TnmoTlubPooaEnLFxuICAgICAgJ0kvVCc6ICfliJvmlrDmgKfkuI7kvKDnu5/mgKcgLSDor4TkvLDnu4Tnu4flnKjliJvmlrDnqoHnoLTkuI7kvKDnu5/nqLPlrprkuYvpl7TnmoTlj5blkJEnLFxuICAgICAgJ00vVic6ICfnrqHnkIblr7zlkJHkuI7mhL/mma/lr7zlkJEgLSDor4TkvLDnu4Tnu4flnKjml6XluLjnrqHnkIbkuI7plb/ov5zmhL/mma/kuYvpl7TnmoTph43ngrknLFxuICAgICAgJ0EvRCc6ICfooYzliqjlr7zlkJHkuI7lhrPnrZblr7zlkJEgLSDor4TkvLDnu4Tnu4flnKjlv6vpgJ/ooYzliqjkuI7mt7HluqblhrPnrZbkuYvpl7TnmoTlgY/lpb0nXG4gICAgfVxuXG4gICAgY29uc3Qgc3lzdGVtUHJvbXB0ID0gYOS9oOaYr09DVEnpl67ljbforr7orqHkuJPlrrbvvIzkuJPpl6jkuLoke2RpbWVuc2lvbn3nu7TluqbnlJ/miJDpq5jotKjph4/nmoTor4TkvLDpopjnm67jgIJcblxuJHtkaW1lbnNpb25957u05bqm6K+05piO77yaJHtkaW1lbnNpb25EZXNjcmlwdGlvbnNbZGltZW5zaW9uIGFzIGtleW9mIHR5cGVvZiBkaW1lbnNpb25EZXNjcmlwdGlvbnNdfVxuXG7or7fnlJ/miJAke3F1ZXN0aW9uQ291bnR96YGT6ZKI5a+5JHtvcHRpb25zLm9yZ2FuaXphdGlvblR5cGV957G75Z6L57uE57uH55qE5LiT5Lia6aKY55uu44CCYFxuXG4gICAgY29uc3QgdXNlclByb21wdCA9IGDor7fkuLoke2RpbWVuc2lvbn3nu7TluqbnlJ/miJAke3F1ZXN0aW9uQ291bnR96YGT6aKY55uu77yI57yW5Y+35LuOJHtzdGFydEluZGV4feW8gOWni++8ieOAglxuXG7opoHmsYLvvJpcbi0g5q+P6YGT6aKYNeS4qumAiemhue+8jOWIhuWAvDEtNeWIhlxuLSDpopjnm67opoHlhbfkvZPjgIHlrp7nlKjjgIHpkojlr7kke29wdGlvbnMub3JnYW5pemF0aW9uVHlwZX1cbi0gJHtvcHRpb25zLnZlcnNpb24gPT09ICdwcm9mZXNzaW9uYWwnID8gJ+S9v+eUqOa3seW6puaDheWig+WMlumXrumimCcgOiAn5L2/55So6YCa55So5oCn6Zeu6aKYJ31cblxu6K+35Lil5qC85oyJ54Wn5Lul5LiLSlNPTuagvOW8j+i/lOWbnu+8mlxuXG5cXGBcXGBcXGBqc29uXG57XG4gIFwicXVlc3Rpb25zXCI6IFtcbiAgICB7XG4gICAgICBcImlkXCI6IFwicV8ke3N0YXJ0SW5kZXh9XCIsXG4gICAgICBcImRpbWVuc2lvblwiOiBcIiR7ZGltZW5zaW9ufVwiLFxuICAgICAgXCJ0eXBlXCI6IFwic2luZ2xlX2Nob2ljZVwiLFxuICAgICAgXCJ0ZXh0XCI6IFwi5YW35L2T55qE6Zeu6aKY5paH5pysXCIsXG4gICAgICBcIm9wdGlvbnNcIjogW1xuICAgICAgICB7XCJpZFwiOiBcIm9fJHtzdGFydEluZGV4fV8xXCIsIFwidGV4dFwiOiBcIuWujOWFqOS4jeespuWQiFwiLCBcInNjb3JlXCI6IDF9LFxuICAgICAgICB7XCJpZFwiOiBcIm9fJHtzdGFydEluZGV4fV8yXCIsIFwidGV4dFwiOiBcIuWfuuacrOS4jeespuWQiFwiLCBcInNjb3JlXCI6IDJ9LFxuICAgICAgICB7XCJpZFwiOiBcIm9fJHtzdGFydEluZGV4fV8zXCIsIFwidGV4dFwiOiBcIumDqOWIhuespuWQiFwiLCBcInNjb3JlXCI6IDN9LFxuICAgICAgICB7XCJpZFwiOiBcIm9fJHtzdGFydEluZGV4fV80XCIsIFwidGV4dFwiOiBcIuWfuuacrOespuWQiFwiLCBcInNjb3JlXCI6IDR9LFxuICAgICAgICB7XCJpZFwiOiBcIm9fJHtzdGFydEluZGV4fV81XCIsIFwidGV4dFwiOiBcIuWujOWFqOespuWQiFwiLCBcInNjb3JlXCI6IDV9XG4gICAgICBdLFxuICAgICAgXCJyZXF1aXJlZFwiOiB0cnVlLFxuICAgICAgXCJvcmRlclwiOiAke3N0YXJ0SW5kZXh9XG4gICAgfVxuICBdXG59XG5cXGBcXGBcXGBcblxu56Gu5L+d6L+U5Zue5a6M5pW055qESlNPTuagvOW8j+OAgmBcblxuICAgIHJldHVybiB7IHN5c3RlbVByb21wdCwgdXNlclByb21wdCB9XG4gIH1cblxuICAvKipcbiAgICog6Kej5p6Q5om55qyh5ZON5bqUXG4gICAqL1xuICBwcml2YXRlIHBhcnNlQmF0Y2hSZXNwb25zZShjb250ZW50OiBzdHJpbmcsIGRpbWVuc2lvbjogc3RyaW5nLCBzdGFydEluZGV4OiBudW1iZXIpOiBRdWVzdGlvbltdIHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ+aJueasoeWTjeW6lOWGheWuuemVv+W6pjonLCBjb250ZW50Lmxlbmd0aClcbiAgICAgIGNvbnNvbGUubG9nKCfmibnmrKHlk43lupTliY01MDDlrZfnrKY6JywgY29udGVudC5zdWJzdHJpbmcoMCwgNTAwKSlcblxuICAgICAgLy8g5o+Q5Y+WSlNPTlxuICAgICAgbGV0IGpzb25TdHIgPSAnJ1xuICAgICAgY29uc3QganNvbk1hdGNoID0gY29udGVudC5tYXRjaCgvYGBganNvblxccyooW1xcc1xcU10qPylcXHMqYGBgLylcbiAgICAgIGlmIChqc29uTWF0Y2gpIHtcbiAgICAgICAganNvblN0ciA9IGpzb25NYXRjaFsxXS50cmltKClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IG9iamVjdE1hdGNoID0gY29udGVudC5tYXRjaCgvXFx7W1xcc1xcU10qLylcbiAgICAgICAgaWYgKG9iamVjdE1hdGNoKSB7XG4gICAgICAgICAganNvblN0ciA9IG9iamVjdE1hdGNoWzBdLnRyaW0oKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmICghanNvblN0cikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+acquaJvuWIsEpTT07moLzlvI8nKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn5o+Q5Y+W55qESlNPTuWtl+espuS4sumVv+W6pjonLCBqc29uU3RyLmxlbmd0aClcblxuICAgICAgLy8g5bCd6K+V5L+u5aSN5LiN5a6M5pW055qESlNPTlxuICAgICAgbGV0IGZpeGVkSnNvbiA9IHRoaXMuZml4QmF0Y2hKc29uKGpzb25TdHIpXG4gICAgICBjb25zb2xlLmxvZygn5L+u5aSN5ZCO55qESlNPTjonLCBmaXhlZEpzb24uc3Vic3RyaW5nKDAsIDIwMCkgKyAnLi4uJylcblxuICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShmaXhlZEpzb24pXG5cbiAgICAgIGlmIChwYXJzZWQucXVlc3Rpb25zICYmIEFycmF5LmlzQXJyYXkocGFyc2VkLnF1ZXN0aW9ucykpIHtcbiAgICAgICAgY29uc3QgcXVlc3Rpb25zID0gcGFyc2VkLnF1ZXN0aW9ucy5tYXAoKHE6IGFueSwgaW5kZXg6IG51bWJlcikgPT4gKHtcbiAgICAgICAgICBpZDogcS5pZCB8fCBgcV8ke3N0YXJ0SW5kZXggKyBpbmRleH1gLFxuICAgICAgICAgIGRpbWVuc2lvbjogcS5kaW1lbnNpb24gfHwgZGltZW5zaW9uLFxuICAgICAgICAgIHR5cGU6IHEudHlwZSB8fCAnc2luZ2xlX2Nob2ljZScsXG4gICAgICAgICAgdGV4dDogcS50ZXh0IHx8IHEudGl0bGUgfHwgYCR7ZGltZW5zaW9ufee7tOW6pumXrumimCAke3N0YXJ0SW5kZXggKyBpbmRleH1gLFxuICAgICAgICAgIG9wdGlvbnM6IHEub3B0aW9ucyB8fCB0aGlzLmdlbmVyYXRlRGVmYXVsdE9wdGlvbnMoJ3NpbmdsZV9jaG9pY2UnKSxcbiAgICAgICAgICByZXF1aXJlZDogcS5yZXF1aXJlZCAhPT0gZmFsc2UsXG4gICAgICAgICAgb3JkZXI6IHEub3JkZXIgfHwgc3RhcnRJbmRleCArIGluZGV4XG4gICAgICAgIH0pKVxuXG4gICAgICAgIGNvbnNvbGUubG9nKGDmiJDlip/op6PmnpAgJHtxdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm65gKVxuICAgICAgICByZXR1cm4gcXVlc3Rpb25zXG4gICAgICB9XG5cbiAgICAgIHRocm93IG5ldyBFcnJvcign6Kej5p6Q55qESlNPTuS4reayoeaciXF1ZXN0aW9uc+aVsOe7hCcpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJueasoeino+aekOWksei0pTonLCBlcnJvcilcbiAgICAgIGxvZ2dlci53YXJuKGDmibnmrKHop6PmnpDlpLHotKXvvIzkvb/nlKjpu5jorqTpopjnm65gLCB7IGVycm9yLCBkaW1lbnNpb24sIHN0YXJ0SW5kZXggfSlcbiAgICAgIHJldHVybiB0aGlzLmdlbmVyYXRlRGVmYXVsdFF1ZXN0aW9uc0JhdGNoKGRpbWVuc2lvbiwgMSwgc3RhcnRJbmRleClcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5L+u5aSN5LiN5a6M5pW055qE5om55qyhSlNPTlxuICAgKi9cbiAgcHJpdmF0ZSBmaXhCYXRjaEpzb24oanNvblN0cjogc3RyaW5nKTogc3RyaW5nIHtcbiAgICB0cnkge1xuICAgICAgLy8g5aaC5p6cSlNPTuW3sue7j+WujOaVtO+8jOebtOaOpei/lOWbnlxuICAgICAgSlNPTi5wYXJzZShqc29uU3RyKVxuICAgICAgcmV0dXJuIGpzb25TdHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coJ0pTT07kuI3lrozmlbTvvIzlsJ3or5Xkv67lpI0uLi4nKVxuXG4gICAgICBsZXQgZml4ZWQgPSBqc29uU3RyXG5cbiAgICAgIC8vIOehruS/neaciXF1ZXN0aW9uc+aVsOe7hOW8gOWni1xuICAgICAgaWYgKCFmaXhlZC5pbmNsdWRlcygnXCJxdWVzdGlvbnNcIicpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSlNPTuS4reayoeaciXF1ZXN0aW9uc+Wtl+autScpXG4gICAgICB9XG5cbiAgICAgIC8vIOS/ruWkjeWPr+iDvee8uuWwkeeahOe7k+WwvlxuICAgICAgaWYgKCFmaXhlZC5lbmRzV2l0aCgnfScpKSB7XG4gICAgICAgIC8vIOaJvuWIsOacgOWQjuS4gOS4quWujOaVtOeahOmXrumimOWvueixoVxuICAgICAgICBjb25zdCBsYXN0Q29tcGxldGVRdWVzdGlvbiA9IGZpeGVkLmxhc3RJbmRleE9mKCdcIm9yZGVyXCInKVxuICAgICAgICBpZiAobGFzdENvbXBsZXRlUXVlc3Rpb24gIT09IC0xKSB7XG4gICAgICAgICAgLy8g5om+5Yiw6L+Z5Liqb3JkZXLlrZfmrrXnmoTlgLznu5PlsL5cbiAgICAgICAgICBjb25zdCBhZnRlck9yZGVyID0gZml4ZWQuc3Vic3RyaW5nKGxhc3RDb21wbGV0ZVF1ZXN0aW9uKVxuICAgICAgICAgIGNvbnN0IG51bWJlck1hdGNoID0gYWZ0ZXJPcmRlci5tYXRjaCgvXCJvcmRlclwiOlxccyooXFxkKykvKVxuICAgICAgICAgIGlmIChudW1iZXJNYXRjaCkge1xuICAgICAgICAgICAgY29uc3QgZW5kUG9zID0gbGFzdENvbXBsZXRlUXVlc3Rpb24gKyBhZnRlck9yZGVyLmluZGV4T2YobnVtYmVyTWF0Y2hbMV0pICsgbnVtYmVyTWF0Y2hbMV0ubGVuZ3RoXG4gICAgICAgICAgICBmaXhlZCA9IGZpeGVkLnN1YnN0cmluZygwLCBlbmRQb3MpICsgJ1xcbiAgICAgfVxcbiAgIF1cXG59J1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDpqozor4Hkv67lpI3nu5PmnpxcbiAgICAgIEpTT04ucGFyc2UoZml4ZWQpXG4gICAgICBjb25zb2xlLmxvZygnSlNPTuS/ruWkjeaIkOWKnycpXG4gICAgICByZXR1cm4gZml4ZWRcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog55Sf5oiQ6buY6K6k6aKY55uu5om55qyhXG4gICAqL1xuICBwcml2YXRlIGdlbmVyYXRlRGVmYXVsdFF1ZXN0aW9uc0JhdGNoKGRpbWVuc2lvbjogc3RyaW5nLCBxdWVzdGlvbkNvdW50OiBudW1iZXIsIHN0YXJ0SW5kZXg6IG51bWJlcik6IFF1ZXN0aW9uW10ge1xuICAgIGNvbnN0IHF1ZXN0aW9uczogUXVlc3Rpb25bXSA9IFtdXG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHF1ZXN0aW9uQ291bnQ7IGkrKykge1xuICAgICAgY29uc3QgcXVlc3Rpb25JbmRleCA9IHN0YXJ0SW5kZXggKyBpXG4gICAgICBxdWVzdGlvbnMucHVzaCh7XG4gICAgICAgIGlkOiBgcV8ke3F1ZXN0aW9uSW5kZXh9YCxcbiAgICAgICAgZGltZW5zaW9uLFxuICAgICAgICB0eXBlOiAnc2luZ2xlX2Nob2ljZScsXG4gICAgICAgIHRleHQ6IGDlhbPkuo4ke2RpbWVuc2lvbn3nu7TluqbnmoTor4TkvLDpl67popggJHtxdWVzdGlvbkluZGV4fWAsXG4gICAgICAgIG9wdGlvbnM6IHRoaXMuZ2VuZXJhdGVEZWZhdWx0T3B0aW9ucygnc2luZ2xlX2Nob2ljZScpLFxuICAgICAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICAgICAgb3JkZXI6IHF1ZXN0aW9uSW5kZXhcbiAgICAgIH0pXG4gICAgfVxuXG4gICAgcmV0dXJuIHF1ZXN0aW9uc1xuICB9XG5cbiAgLyoqXG4gICAqIOeUn+aIkOm7mOiupOmXruWNt++8iOW9k0xMTeino+aekOWksei0peaXtueahOWkh+eUqOaWueahiO+8iVxuICAgKi9cbiAgcHJpdmF0ZSBnZW5lcmF0ZURlZmF1bHRRdWVzdGlvbnMob3B0aW9uczogRGVzaWduT3B0aW9ucyk6IFF1ZXN0aW9uW10ge1xuICAgIGNvbnN0IHF1ZXN0aW9uczogUXVlc3Rpb25bXSA9IFtdXG4gICAgY29uc3QgZGltZW5zaW9uUXVlc3Rpb25zID0ge1xuICAgICAgJ1MvRic6IFtcbiAgICAgICAgJ+aCqOeahOe7hOe7h+abtOWAvuWQkeS6jumBteW+quaXouWumueahOa1geeoi+WSjOinhOiMgycsXG4gICAgICAgICflnKjpnaLlr7nmlrDmg4XlhrXml7bvvIzmgqjnmoTnu4Tnu4fog73lpJ/lv6vpgJ/osIPmlbTnrZbnlaUnLFxuICAgICAgICAn5oKo55qE57uE57uH5pyJ5piO56Gu55qE5bGC57qn57uT5p6E5ZKM6IGM6LSj5YiG5belJyxcbiAgICAgICAgJ+aCqOeahOe7hOe7h+m8k+WKseWRmOW3peWcqOW3peS9nOS4reWPkeaMpeWIm+mAoOaApycsXG4gICAgICAgICfmgqjnmoTnu4Tnu4fph43op4bmoIflh4bljJbnmoTlt6XkvZzmtYHnqIsnXG4gICAgICBdLFxuICAgICAgJ0kvVCc6IFtcbiAgICAgICAgJ+aCqOeahOe7hOe7h+enr+aegemHh+eUqOaWsOaKgOacr+WSjOaWsOaWueazlScsXG4gICAgICAgICfmgqjnmoTnu4Tnu4fph43op4bkvKDnu5/nu4/pqozlkozmiJDnhp/lgZrms5UnLFxuICAgICAgICAn5oKo55qE57uE57uH6byT5Yqx5ZGY5bel5o+Q5Ye65Yib5paw5oOz5rOVJyxcbiAgICAgICAgJ+aCqOeahOe7hOe7h+WcqOWGs+etluaXtuS8muWFheWIhuiAg+iZkeWOhuWPsue7j+mqjCcsXG4gICAgICAgICfmgqjnmoTnu4Tnu4fmhL/mhI/mib/mi4XliJvmlrDluKbmnaXnmoTpo47pmaknXG4gICAgICBdLFxuICAgICAgJ00vVic6IFtcbiAgICAgICAgJ+aCqOeahOe7hOe7h+azqOmHjeaXpeW4uOi/kOiQpeeuoeeQhueahOaViOeOhycsXG4gICAgICAgICfmgqjnmoTnu4Tnu4fmnInmuIXmmbDnmoTplb/ov5zlj5HlsZXmhL/mma8nLFxuICAgICAgICAn5oKo55qE57uE57uH6YeN6KeG57up5pWI5oyH5qCH55qE6L6+5oiQJyxcbiAgICAgICAgJ+aCqOeahOe7hOe7h+e7j+W4uOiuqOiuuuacquadpeWPkeWxleaWueWQkScsXG4gICAgICAgICfmgqjnmoTnu4Tnu4flloTkuo7liLblrpror6bnu4bnmoTmiafooYzorqHliJInXG4gICAgICBdLFxuICAgICAgJ0EvRCc6IFtcbiAgICAgICAgJ+aCqOeahOe7hOe7h+WcqOWGs+etluaXtuS8mui/m+ihjOWFheWIhueahOWIhuaekOWSjOiuqOiuuicsXG4gICAgICAgICfmgqjnmoTnu4Tnu4fog73lpJ/lv6vpgJ/lk43lupTluILlnLrlj5jljJYnLFxuICAgICAgICAn5oKo55qE57uE57uH6YeN6KeG5pWw5o2u5YiG5p6Q5Zyo5Yaz562W5Lit55qE5L2c55SoJyxcbiAgICAgICAgJ+aCqOeahOe7hOe7h+m8k+WKseWRmOW3peS4u+WKqOmHh+WPluihjOWKqCcsXG4gICAgICAgICfmgqjnmoTnu4Tnu4flnKjph43opoHlhrPnrZbliY3kvJrlvoHmsYLlpJrmlrnmhI/op4EnXG4gICAgICBdXG4gICAgfVxuXG4gICAgbGV0IHF1ZXN0aW9uSWQgPSAxXG4gICAgb3B0aW9ucy5kaW1lbnNpb25zLmZvckVhY2goZGltZW5zaW9uID0+IHtcbiAgICAgIGNvbnN0IGRpbWVuc2lvblRleHRzID0gZGltZW5zaW9uUXVlc3Rpb25zW2RpbWVuc2lvbiBhcyBrZXlvZiB0eXBlb2YgZGltZW5zaW9uUXVlc3Rpb25zXSB8fCBbXVxuICAgICAgZGltZW5zaW9uVGV4dHMuZm9yRWFjaCh0ZXh0ID0+IHtcbiAgICAgICAgcXVlc3Rpb25zLnB1c2goe1xuICAgICAgICAgIGlkOiBgcV8ke3F1ZXN0aW9uSWR9YCxcbiAgICAgICAgICBkaW1lbnNpb24sXG4gICAgICAgICAgdHlwZTogJ3NpbmdsZV9jaG9pY2UnLFxuICAgICAgICAgIHRleHQ6IHRleHQsXG4gICAgICAgICAgb3B0aW9uczogW1xuICAgICAgICAgICAgeyBpZDogYG9fJHtxdWVzdGlvbklkfV8xYCwgdGV4dDogJ+WujOWFqOS4jeWQjOaEjycsIHNjb3JlOiAxIH0sXG4gICAgICAgICAgICB7IGlkOiBgb18ke3F1ZXN0aW9uSWR9XzJgLCB0ZXh0OiAn5LiN5ZCM5oSPJywgc2NvcmU6IDIgfSxcbiAgICAgICAgICAgIHsgaWQ6IGBvXyR7cXVlc3Rpb25JZH1fM2AsIHRleHQ6ICfkuK3nq4snLCBzY29yZTogMyB9LFxuICAgICAgICAgICAgeyBpZDogYG9fJHtxdWVzdGlvbklkfV80YCwgdGV4dDogJ+WQjOaEjycsIHNjb3JlOiA0IH0sXG4gICAgICAgICAgICB7IGlkOiBgb18ke3F1ZXN0aW9uSWR9XzVgLCB0ZXh0OiAn5a6M5YWo5ZCM5oSPJywgc2NvcmU6IDUgfVxuICAgICAgICAgIF0sXG4gICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgICAgICAgb3JkZXI6IHF1ZXN0aW9uSWRcbiAgICAgICAgfSlcbiAgICAgICAgcXVlc3Rpb25JZCsrXG4gICAgICB9KVxuICAgIH0pXG5cbiAgICAvLyDooaXlhYXliLA2MOmimFxuICAgIHdoaWxlIChxdWVzdGlvbnMubGVuZ3RoIDwgNjApIHtcbiAgICAgIGNvbnN0IGRpbWVuc2lvbiA9IG9wdGlvbnMuZGltZW5zaW9uc1txdWVzdGlvbnMubGVuZ3RoICUgNF1cbiAgICAgIHF1ZXN0aW9ucy5wdXNoKHtcbiAgICAgICAgaWQ6IGBxXyR7cXVlc3Rpb25zLmxlbmd0aCArIDF9YCxcbiAgICAgICAgZGltZW5zaW9uLFxuICAgICAgICB0eXBlOiAnc2luZ2xlX2Nob2ljZScsXG4gICAgICAgIHRleHQ6IGDlhbPkuo4ke2RpbWVuc2lvbn3nu7TluqbnmoTor4TkvLDpl67popggJHtxdWVzdGlvbnMubGVuZ3RoICsgMX1gLFxuICAgICAgICBvcHRpb25zOiBbXG4gICAgICAgICAgeyBpZDogYG9fJHtxdWVzdGlvbnMubGVuZ3RoICsgMX1fMWAsIHRleHQ6ICflrozlhajkuI3lkIzmhI8nLCBzY29yZTogMSB9LFxuICAgICAgICAgIHsgaWQ6IGBvXyR7cXVlc3Rpb25zLmxlbmd0aCArIDF9XzJgLCB0ZXh0OiAn5LiN5ZCM5oSPJywgc2NvcmU6IDIgfSxcbiAgICAgICAgICB7IGlkOiBgb18ke3F1ZXN0aW9ucy5sZW5ndGggKyAxfV8zYCwgdGV4dDogJ+S4reeriycsIHNjb3JlOiAzIH0sXG4gICAgICAgICAgeyBpZDogYG9fJHtxdWVzdGlvbnMubGVuZ3RoICsgMX1fNGAsIHRleHQ6ICflkIzmhI8nLCBzY29yZTogNCB9LFxuICAgICAgICAgIHsgaWQ6IGBvXyR7cXVlc3Rpb25zLmxlbmd0aCArIDF9XzVgLCB0ZXh0OiAn5a6M5YWo5ZCM5oSPJywgc2NvcmU6IDUgfVxuICAgICAgICBdLFxuICAgICAgICByZXF1aXJlZDogdHJ1ZSxcbiAgICAgICAgb3JkZXI6IHF1ZXN0aW9ucy5sZW5ndGggKyAxXG4gICAgICB9KVxuICAgIH1cblxuICAgIHJldHVybiBxdWVzdGlvbnNcbiAgfVxufVxuIl0sIm5hbWVzIjpbIkxvZ2dlciIsImxvZ2dlciIsIlF1ZXN0aW9uRGVzaWduZXJBZ2VudCIsImNvbnN0cnVjdG9yIiwibGxtQ2xpZW50IiwicHJvbXB0QnVpbGRlciIsImRhdGFGdXNpb25FbmdpbmUiLCJuYW1lIiwiaW5pdGlhbGl6ZWQiLCJsYXN0QWN0aXZpdHkiLCJEYXRlIiwicXVlc3Rpb25DYWNoZSIsIk1hcCIsImluaXRpYWxpemUiLCJpbmZvIiwiZ2V0U3RhdHVzIiwiY29uZmlnIiwibGxtUHJvdmlkZXIiLCJ2ZXJzaW9uIiwiZXhlY3V0ZSIsImlucHV0IiwiZGVzaWduT3B0aW9ucyIsIm9yZ2FuaXphdGlvblR5cGUiLCJ0YXJnZXRBdWRpZW5jZSIsImN1c3RvbVJlcXVpcmVtZW50cyIsInF1ZXN0aW9uQ291bnQiLCJkaW1lbnNpb25zIiwicXVlc3Rpb25uYWlyZSIsImRlc2lnbiIsInN1Y2Nlc3MiLCJkYXRhIiwiZXJyb3IiLCJFcnJvciIsIm1lc3NhZ2UiLCJvcHRpb25zIiwic3RhcnRUaW1lIiwibm93IiwiZ2VuZXJhdGVRdWVzdGlvbm5haXJlQmF0Y2hlZCIsImdlbmVyYXRpb25UaW1lIiwidG9GaXhlZCIsIm1ldGFkYXRhIiwibW9kZWwiLCJwcm92aWRlciIsInF1ZXN0aW9ucyIsImxlbmd0aCIsImJ1aWxkUHJvbXB0Iiwic3lzdGVtUHJvbXB0IiwidXNlclByb21wdCIsInBhcnNlUmVzcG9uc2UiLCJjb250ZW50IiwiY29uc29sZSIsImxvZyIsInN1YnN0cmluZyIsIndhcm4iLCJqc29uU3RyIiwianNvbkJsb2NrTWF0Y2giLCJtYXRjaCIsInRyaW0iLCJqc29uT2JqZWN0TWF0Y2giLCJzdGFydEluZGV4IiwiaW5kZXhPZiIsImJyYWNlQ291bnQiLCJlbmRJbmRleCIsImkiLCJwYXJzZWRDb250ZW50IiwiSlNPTiIsInBhcnNlIiwidmFsaWRhdGVBbmRGb3JtYXRRdWVzdGlvbm5haXJlIiwicGFyc2VFcnJvciIsImZpeGVkSnNvbiIsImZpeEpzb25TdHJpbmciLCJmaXhFcnJvciIsInBhcnNlU3RydWN0dXJlZFJlc3BvbnNlIiwiU3RyaW5nIiwiY29udGVudFByZXZpZXciLCJmaXhlZCIsInJlcGxhY2UiLCJlbmRzV2l0aCIsImxhc3RDb21wbGV0ZU9iamVjdCIsImxhc3RJbmRleE9mIiwiZXJyb3JzIiwiaWQiLCJwdXNoIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImFzc2Vzc21lbnRUeXBlIiwiQXJyYXkiLCJpc0FycmF5Iiwiam9pbiIsIm1hcCIsInEiLCJpbmRleCIsImRpbWVuc2lvbiIsInR5cGUiLCJvIiwib3B0aW9uSW5kZXgiLCJ0ZXh0Iiwic2NvcmUiLCJyZXF1aXJlZCIsIm9yZGVyIiwidG90YWxRdWVzdGlvbnMiLCJlc3RpbWF0ZWRUaW1lIiwiY2FsY3VsYXRlRXN0aW1hdGVkVGltZSIsImNyZWF0ZWRBdCIsInRvSVNPU3RyaW5nIiwicmVzdWx0IiwiZ2VuZXJhdGVEZWZhdWx0UXVlc3Rpb25zIiwiZXh0cmFjdFZhbHVlIiwibGluZSIsImNvbG9uSW5kZXgiLCJwYXJzZVF1ZXN0aW9ucyIsInJhd1F1ZXN0aW9ucyIsInJhd1EiLCJxdWVzdGlvbiIsImluZmVyRGltZW5zaW9uIiwic3ViZGltZW5zaW9uIiwiaW5mZXJRdWVzdGlvblR5cGUiLCJkZXB0aCIsInNjYWxlIiwicmV2ZXJzZWQiLCJ3ZWlnaHQiLCJkaWZmaWN1bHR5IiwiZGlzY3JpbWluYXRpb25JbmRleCIsImV4cGVjdGVkUmVzcG9uc2VUaW1lIiwiZ2VuZXJhdGVEZWZhdWx0T3B0aW9ucyIsIm1pbiIsIm1heCIsImxhYmVscyIsImluY2x1ZGVzIiwidmFsaWRhdGVRdWVzdGlvbm5haXJlIiwid2FybmluZ3MiLCJzdWdnZXN0aW9ucyIsImV4cGVjdGVkUXVlc3Rpb25zIiwiU2V0Iiwic2l6ZSIsInR5cGVEaXN0cmlidXRpb24iLCJhbmFseXplUXVlc3Rpb25UeXBlcyIsImxpa2VydF9zY2FsZSIsInF1YWxpdHlTY29yZSIsIk1hdGgiLCJpc1ZhbGlkIiwicmVnZW5lcmF0ZVF1ZXN0aW9ubmFpcmUiLCJpbXByb3ZlZE9wdGlvbnMiLCJmaWx0ZXIiLCJCb29sZWFuIiwiZGlzdHJpYnV0aW9uIiwic2luZ2xlX2Nob2ljZSIsIm11bHRpcGxlX2Nob2ljZSIsIm9wZW5fZW5kZWQiLCJyYW5raW5nIiwibWF0cml4IiwiY2hvaWNlIiwic2NlbmFyaW8iLCJmb3JFYWNoIiwidG90YWwiLCJPYmplY3QiLCJrZXlzIiwia2V5IiwidG90YWxTZWNvbmRzIiwicmVkdWNlIiwiYmFzZVRpbWUiLCJtaW51dGVzIiwiY2VpbCIsImNhbGN1bGF0ZURpZmZpY3VsdHkiLCJhdmdEaWZmaWN1bHR5Iiwic3VtIiwiZ2VuZXJhdGVRdWVzdGlvbm5haXJlSWQiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsImdlbmVyYXRlQ2FjaGVLZXkiLCJrZXlQYXJ0cyIsImluZHVzdHJ5Q29udGV4dCIsImdldFF1ZXN0aW9ubmFpcmVQcmV2aWV3IiwicXVlc3Rpb25uYWlyZUlkIiwiZnJvbSIsInZhbHVlcyIsInBhcnNlSW50IiwiY2xlYXJDYWNoZSIsImNsZWFyIiwiZ2V0U3RhdHMiLCJxdWVzdGlvbm5haXJlcyIsInZlcnNpb25EaXN0cmlidXRpb24iLCJjYWNoZVNpemUiLCJ0b3RhbFF1ZXN0aW9ubmFpcmVzIiwicXVlc3Rpb25zUGVyQmF0Y2giLCJxdWVzdGlvbnNQZXJEaW1lbnNpb24iLCJiYXRjaCIsInF1ZXN0aW9uc0luQmF0Y2giLCJiYXRjaFF1ZXN0aW9ucyIsImdlbmVyYXRlUXVlc3Rpb25CYXRjaCIsImJhdGNoU2l6ZSIsInRvdGFsR2VuZXJhdGVkIiwic2V0IiwiZGVmYXVsdFF1ZXN0aW9ucyIsImdlbmVyYXRlRGVmYXVsdFF1ZXN0aW9uc0JhdGNoIiwicHJvbXB0IiwiYnVpbGRCYXRjaFByb21wdCIsImxsbVJlcXVlc3QiLCJtZXNzYWdlcyIsInJvbGUiLCJ0ZW1wZXJhdHVyZSIsIm1heFRva2VucyIsInJlc3BvbnNlIiwiY2hhdCIsInBhcnNlQmF0Y2hSZXNwb25zZSIsImRpbWVuc2lvbkRlc2NyaXB0aW9ucyIsImpzb25NYXRjaCIsIm9iamVjdE1hdGNoIiwiZml4QmF0Y2hKc29uIiwicGFyc2VkIiwibGFzdENvbXBsZXRlUXVlc3Rpb24iLCJhZnRlck9yZGVyIiwibnVtYmVyTWF0Y2giLCJlbmRQb3MiLCJxdWVzdGlvbkluZGV4IiwiZGltZW5zaW9uUXVlc3Rpb25zIiwicXVlc3Rpb25JZCIsImRpbWVuc2lvblRleHRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/services/agents/QuestionDesignerAgent.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/data/data-fusion-engine.ts":
/*!*************************************************!*\
  !*** ./src/services/data/data-fusion-engine.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataFusionEngine: () => (/* binding */ DataFusionEngine)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n// 数据源Schema - 扩展支持更多内容类型\nconst DataSourceSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    sourceId: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    content: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    contentType: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n        \"text\",\n        \"json\",\n        \"pdf\",\n        \"docx\",\n        \"url\",\n        \"custom\",\n        \"hr\",\n        \"financial\",\n        \"operational\",\n        \"market\"\n    ]),\n    metadata: zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        timestamp: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        reliability: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).max(1).optional(),\n        source: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional()\n    }).optional()\n});\n// 融合配置Schema\nconst FusionConfigSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    strategy: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n        \"weighted\",\n        \"priority\",\n        \"consensus\"\n    ]).default(\"weighted\"),\n    weights: zod__WEBPACK_IMPORTED_MODULE_1__.record(zod__WEBPACK_IMPORTED_MODULE_1__.number()).default({}),\n    threshold: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).max(1).default(0.7),\n    maxSources: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().default(5),\n    enableCache: zod__WEBPACK_IMPORTED_MODULE_1__.boolean().optional(),\n    cacheTimeout: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional()\n});\n/**\n * 数据融合引擎类\n */ class DataFusionEngine {\n    constructor(config = {}){\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"DataFusionEngine\");\n        const defaultConfig = {\n            strategy: \"weighted\",\n            threshold: 0.7,\n            maxSources: 5,\n            weights: {},\n            enableCache: true,\n            cacheTimeout: 3600\n        };\n        this.config = {\n            ...defaultConfig,\n            ...config\n        };\n    }\n    /**\n   * 获取默认配置\n   */ getDefaultConfig() {\n        return {\n            strategy: \"weighted\",\n            threshold: 0.7,\n            maxSources: 5,\n            weights: {},\n            enableCache: true,\n            cacheTimeout: 3600\n        };\n    }\n    /**\n   * 融合多个数据源\n   */ async fuseData(sources, config = {}) {\n        try {\n            // 验证输入\n            const validatedSources = sources.map((source)=>DataSourceSchema.parse(source));\n            const validatedConfig = FusionConfigSchema.parse(config);\n            this.logger.info(`开始数据融合，共 ${validatedSources.length} 个数据源`);\n            // 预处理数据源\n            const processedSources = await this.preprocessSources(validatedSources);\n            // 根据策略融合数据\n            const fusionResult = await this.applyFusionStrategy(processedSources, validatedConfig);\n            // 计算置信度\n            const confidence = this.calculateConfidence(processedSources, fusionResult);\n            this.logger.info(\"数据融合完成\", {\n                sourcesCount: validatedSources.length,\n                confidence,\n                strategy: validatedConfig.strategy\n            });\n            return {\n                fusedContent: fusionResult.content,\n                confidence,\n                sourcesUsed: fusionResult.sourcesUsed,\n                metadata: {\n                    strategy: validatedConfig.strategy,\n                    processedAt: new Date().toISOString(),\n                    originalSourcesCount: validatedSources.length,\n                    qualityScore: this.assessDataQuality(processedSources)\n                }\n            };\n        } catch (error) {\n            this.logger.error(\"数据融合失败\", {\n                error\n            });\n            throw new Error(`数据融合失败: ${error}`);\n        }\n    }\n    /**\n   * 预处理数据源\n   */ async preprocessSources(sources) {\n        const processed = [];\n        for (const source of sources){\n            try {\n                let processedContent = source.content;\n                // 根据内容类型进行预处理\n                switch(source.contentType){\n                    case \"json\":\n                        processedContent = this.processJsonContent(source.content);\n                        break;\n                    case \"pdf\":\n                    case \"docx\":\n                        processedContent = await this.processDocumentContent(source.content);\n                        break;\n                    case \"url\":\n                        processedContent = await this.processUrlContent(source.content);\n                        break;\n                    case \"text\":\n                    default:\n                        processedContent = this.processTextContent(source.content);\n                        break;\n                }\n                // 评估数据质量\n                const qualityScore = this.assessContentQuality(processedContent);\n                const relevanceScore = this.assessContentRelevance(processedContent);\n                processed.push({\n                    ...source,\n                    processedContent,\n                    qualityScore,\n                    relevanceScore\n                });\n            } catch (error) {\n                this.logger.warn(`数据源预处理失败: ${source.sourceId}`, {\n                    error\n                });\n            // 继续处理其他数据源\n            }\n        }\n        return processed;\n    }\n    /**\n   * 应用融合策略\n   */ async applyFusionStrategy(sources, config) {\n        // 过滤低质量数据源\n        const filteredSources = sources.filter((source)=>source.qualityScore >= config.threshold).slice(0, config.maxSources);\n        if (filteredSources.length === 0) {\n            throw new Error(\"没有符合质量要求的数据源\");\n        }\n        switch(config.strategy){\n            case \"weighted\":\n                return this.weightedFusion(filteredSources, config.weights || {});\n            case \"priority\":\n                return this.priorityFusion(filteredSources);\n            case \"consensus\":\n                return this.consensusFusion(filteredSources);\n            default:\n                throw new Error(`不支持的融合策略: ${config.strategy}`);\n        }\n    }\n    /**\n   * 加权融合策略\n   */ weightedFusion(sources, weights) {\n        let fusedContent = \"# 数据融合报告\\n\\n\";\n        const sourcesUsed = [];\n        // 按权重排序\n        const sortedSources = sources.sort((a, b)=>{\n            const weightA = weights[a.sourceId] || a.qualityScore;\n            const weightB = weights[b.sourceId] || b.qualityScore;\n            return weightB - weightA;\n        });\n        for (const source of sortedSources){\n            const weight = weights[source.sourceId] || source.qualityScore;\n            fusedContent += `## 数据源: ${source.sourceId} (权重: ${weight.toFixed(2)})\\n\\n`;\n            fusedContent += `${source.processedContent}\\n\\n`;\n            sourcesUsed.push(source.sourceId);\n        }\n        return {\n            content: fusedContent,\n            sourcesUsed\n        };\n    }\n    /**\n   * 优先级融合策略\n   */ priorityFusion(sources) {\n        // 按综合评分排序\n        const sortedSources = sources.sort((a, b)=>{\n            const scoreA = (a.qualityScore + a.relevanceScore) / 2;\n            const scoreB = (b.qualityScore + b.relevanceScore) / 2;\n            return scoreB - scoreA;\n        });\n        let fusedContent = \"# 优先级数据融合报告\\n\\n\";\n        const sourcesUsed = [];\n        for (const source of sortedSources){\n            const score = (source.qualityScore + source.relevanceScore) / 2;\n            fusedContent += `## 数据源: ${source.sourceId} (评分: ${score.toFixed(2)})\\n\\n`;\n            fusedContent += `${source.processedContent}\\n\\n`;\n            sourcesUsed.push(source.sourceId);\n        }\n        return {\n            content: fusedContent,\n            sourcesUsed\n        };\n    }\n    /**\n   * 共识融合策略\n   */ consensusFusion(sources) {\n        // 简化实现：提取共同关键词和主题\n        const allContent = sources.map((s)=>s.processedContent).join(\" \");\n        const keywords = this.extractKeywords(allContent);\n        const commonThemes = this.identifyCommonThemes(sources.map((s)=>s.processedContent));\n        let fusedContent = \"# 共识数据融合报告\\n\\n\";\n        fusedContent += `## 关键词汇\\n${keywords.join(\", \")}\\n\\n`;\n        fusedContent += `## 共同主题\\n${commonThemes.join(\"\\n\")}\\n\\n`;\n        fusedContent += `## 详细内容\\n\\n`;\n        const sourcesUsed = [];\n        for (const source of sources){\n            fusedContent += `### ${source.sourceId}\\n${source.processedContent}\\n\\n`;\n            sourcesUsed.push(source.sourceId);\n        }\n        return {\n            content: fusedContent,\n            sourcesUsed\n        };\n    }\n    /**\n   * 处理不同类型的内容\n   */ processJsonContent(content) {\n        try {\n            const parsed = JSON.parse(content);\n            return JSON.stringify(parsed, null, 2);\n        } catch  {\n            return content;\n        }\n    }\n    processTextContent(content) {\n        // 清理和格式化文本\n        return content.replace(/\\s+/g, \" \").replace(/\\n\\s*\\n/g, \"\\n\\n\").trim();\n    }\n    async processDocumentContent(content) {\n        // 这里应该实现文档解析逻辑\n        // 暂时返回原内容\n        return content;\n    }\n    async processUrlContent(content) {\n        // 这里应该实现URL内容抓取逻辑\n        // 暂时返回原内容\n        return content;\n    }\n    /**\n   * 评估内容质量\n   */ assessContentQuality(content) {\n        let score = 0.5 // 基础分\n        ;\n        // 长度评分\n        if (content.length > 100) score += 0.1;\n        if (content.length > 500) score += 0.1;\n        if (content.length > 1000) score += 0.1;\n        // 结构评分\n        if (content.includes(\"\\n\")) score += 0.05;\n        if (/[.!?]/.test(content)) score += 0.05;\n        // 内容丰富度评分\n        const uniqueWords = new Set(content.toLowerCase().split(/\\s+/)).size;\n        if (uniqueWords > 50) score += 0.1;\n        if (uniqueWords > 100) score += 0.1;\n        return Math.min(score, 1.0);\n    }\n    /**\n   * 评估内容相关性\n   */ assessContentRelevance(content) {\n        // OCTI相关关键词\n        const octiKeywords = [\n            \"组织\",\n            \"团队\",\n            \"领导\",\n            \"管理\",\n            \"文化\",\n            \"能力\",\n            \"评估\",\n            \"结构化\",\n            \"灵活\",\n            \"直觉\",\n            \"思考\",\n            \"愿景\",\n            \"行动\",\n            \"深思\"\n        ];\n        const contentLower = content.toLowerCase();\n        const matchedKeywords = octiKeywords.filter((keyword)=>contentLower.includes(keyword));\n        return Math.min(matchedKeywords.length / octiKeywords.length * 2, 1.0);\n    }\n    /**\n   * 计算融合置信度\n   */ calculateConfidence(sources, result) {\n        if (sources.length === 0) return 0;\n        const usedSources = sources.slice(0, result.sourcesUsed.length);\n        const avgQuality = usedSources.reduce((sum, s)=>sum + s.qualityScore, 0) / usedSources.length;\n        const avgRelevance = usedSources.reduce((sum, s)=>sum + s.relevanceScore, 0) / usedSources.length;\n        // 综合质量、相关性和数据源数量\n        const sourceCountFactor = Math.min(usedSources.length / 3, 1.0);\n        return avgQuality * 0.4 + avgRelevance * 0.4 + sourceCountFactor * 0.2;\n    }\n    /**\n   * 评估整体数据质量\n   */ assessDataQuality(sources) {\n        if (sources.length === 0) return 0;\n        return sources.reduce((sum, s)=>sum + s.qualityScore, 0) / sources.length;\n    }\n    /**\n   * 提取关键词\n   */ extractKeywords(content) {\n        const words = content.toLowerCase().replace(/[^\\w\\s\\u4e00-\\u9fff]/g, \" \").split(/\\s+/).filter((word)=>word.length > 2);\n        const wordCount = new Map();\n        for (const word of words){\n            wordCount.set(word, (wordCount.get(word) || 0) + 1);\n        }\n        return Array.from(wordCount.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 10).map(([word])=>word);\n    }\n    /**\n   * 识别共同主题\n   */ identifyCommonThemes(contents) {\n        // 简化实现：查找在多个内容中都出现的句子模式\n        const themes = [];\n        // 这里应该实现更复杂的主题识别算法\n        // 暂时返回基础主题\n        themes.push(\"组织能力评估相关内容\");\n        return themes;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/data/data-fusion-engine.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llm/llm-api-client.ts":
/*!********************************************!*\
  !*** ./src/services/llm/llm-api-client.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLMApiClient: () => (/* binding */ LLMApiClient)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n// LLM请求配置Schema\nconst LLMRequestSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    model: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    messages: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        role: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n            \"system\",\n            \"user\",\n            \"assistant\"\n        ]),\n        content: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })),\n    temperature: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).max(2).optional(),\n    maxTokens: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    stream: zod__WEBPACK_IMPORTED_MODULE_1__.boolean().optional()\n});\n// LLM响应Schema\nconst LLMResponseSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        message: zod__WEBPACK_IMPORTED_MODULE_1__.object({\n            role: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n            content: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n        }),\n        finishReason: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional()\n    })),\n    usage: zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        promptTokens: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n        completionTokens: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n        totalTokens: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional()\n    }).optional()\n});\n/**\n * LLM API客户端类\n */ class LLMApiClient {\n    constructor(config = {}){\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"LLMApiClient\");\n        this.config = {\n            defaultTimeout: 30000,\n            maxRetries: 3,\n            minimax: {\n                apiKey: process.env.MINIMAX_API_KEY,\n                baseUrl: process.env.MINIMAX_BASE_URL || \"https://api.minimax.chat/v1\",\n                timeout: 30000,\n                ...config.minimax\n            },\n            deepseek: {\n                apiKey: process.env.DEEPSEEK_API_KEY,\n                baseUrl: process.env.DEEPSEEK_BASE_URL || \"https://api.deepseek.com\",\n                timeout: 30000,\n                ...config.deepseek\n            },\n            ...config\n        };\n        // 初始化常量属性\n        this.MINIMAX_BASE_URL = this.config.minimax?.baseUrl || \"https://api.minimax.chat/v1\";\n        this.DEEPSEEK_BASE_URL = this.config.deepseek?.baseUrl || \"https://api.deepseek.com\";\n        this.MAX_RETRIES = this.config.maxRetries || 3;\n        this.DEFAULT_TIMEOUT = this.config.defaultTimeout || 30000;\n    }\n    /**\n   * 获取默认配置\n   */ getDefaultConfig() {\n        return {\n            defaultTimeout: 30000,\n            maxRetries: 3,\n            minimax: {\n                apiKey: process.env.MINIMAX_API_KEY,\n                baseUrl: process.env.MINIMAX_BASE_URL || \"https://api.minimax.chat/v1\",\n                timeout: 30000\n            },\n            deepseek: {\n                apiKey: process.env.DEEPSEEK_API_KEY,\n                baseUrl: process.env.DEEPSEEK_BASE_URL || \"https://api.deepseek.com\",\n                timeout: 30000\n            }\n        };\n    }\n    /**\n   * 调用MiniMax API\n   */ async callMiniMax(request) {\n        const apiKey = process.env.MINIMAX_API_KEY;\n        if (!apiKey) {\n            throw new Error(\"MiniMax API密钥未配置\");\n        }\n        return this.makeRequest(`${this.MINIMAX_BASE_URL}/chat/completions`, {\n            ...request,\n            model: request.model || \"abab6.5-chat\"\n        }, {\n            \"Authorization\": `Bearer ${apiKey}`,\n            \"Content-Type\": \"application/json\"\n        });\n    }\n    /**\n   * 调用DeepSeek API\n   */ async callDeepSeek(request) {\n        const apiKey = process.env.DEEPSEEK_API_KEY;\n        if (!apiKey) {\n            throw new Error(\"DeepSeek API密钥未配置\");\n        }\n        return this.makeRequest(`${this.DEEPSEEK_BASE_URL}/chat/completions`, {\n            ...request,\n            model: request.model || \"deepseek-chat\"\n        }, {\n            \"Authorization\": `Bearer ${apiKey}`,\n            \"Content-Type\": \"application/json\"\n        });\n    }\n    /**\n   * 统一的HTTP请求方法\n   */ async makeRequest(url, data, headers) {\n        let lastError = null;\n        for(let attempt = 1; attempt <= this.MAX_RETRIES; attempt++){\n            try {\n                this.logger.debug(`LLM API请求 (尝试 ${attempt}/${this.MAX_RETRIES})`, {\n                    url: url.replace(/\\/v1.*/, \"/v1/***\"),\n                    model: data.model\n                });\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), this.DEFAULT_TIMEOUT);\n                const response = await fetch(url, {\n                    method: \"POST\",\n                    headers,\n                    body: JSON.stringify(data),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`HTTP ${response.status}: ${errorText}`);\n                }\n                const responseData = await response.json();\n                const validatedResponse = LLMResponseSchema.parse(responseData);\n                // 添加便捷的content属性\n                const responseWithContent = {\n                    ...validatedResponse,\n                    content: validatedResponse.choices[0]?.message?.content || \"\"\n                };\n                this.logger.info(\"LLM API调用成功\", {\n                    model: data.model,\n                    tokensUsed: validatedResponse.usage?.totalTokens || 0,\n                    attempt\n                });\n                return responseWithContent;\n            } catch (error) {\n                lastError = error;\n                this.logger.warn(`LLM API调用失败 (尝试 ${attempt}/${this.MAX_RETRIES})`, {\n                    error: lastError.message,\n                    model: data.model\n                });\n                // 如果不是最后一次尝试，等待后重试\n                if (attempt < this.MAX_RETRIES) {\n                    const delay = Math.pow(2, attempt) * 1000 // 指数退避\n                    ;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                }\n            }\n        }\n        this.logger.error(\"LLM API调用最终失败\", {\n            error: lastError?.message,\n            attempts: this.MAX_RETRIES\n        });\n        throw lastError || new Error(\"LLM API调用失败\");\n    }\n    /**\n   * 统一的聊天接口\n   */ async chat(provider, request) {\n        switch(provider){\n            case \"minimax\":\n                return this.callMiniMax(request);\n            case \"deepseek\":\n                return this.callDeepSeek(request);\n            default:\n                throw new Error(`不支持的LLM提供商: ${provider}`);\n        }\n    }\n    /**\n   * 双模型聊天接口\n   */ async dualModelChat(request, strategy = \"sequential\") {\n        if (strategy === \"parallel\") {\n            // 并行调用两个模型\n            const [minimaxResult, deepseekResult] = await Promise.allSettled([\n                this.callMiniMax(request),\n                this.callDeepSeek(request)\n            ]);\n            // 选择成功的结果，优先选择MiniMax\n            if (minimaxResult.status === \"fulfilled\") {\n                return minimaxResult.value;\n            } else if (deepseekResult.status === \"fulfilled\") {\n                return deepseekResult.value;\n            } else {\n                throw new Error(\"双模型调用均失败\");\n            }\n        } else {\n            // 顺序调用，失败时切换到另一个模型\n            try {\n                return await this.callMiniMax(request);\n            } catch (error) {\n                this.logger.warn(\"MiniMax调用失败，切换到DeepSeek\", {\n                    error\n                });\n                return await this.callDeepSeek(request);\n            }\n        }\n    }\n    /**\n   * 健康检查\n   */ async healthCheck() {\n        const results = {\n            minimax: false,\n            deepseek: false\n        };\n        // 检查MiniMax\n        try {\n            await this.callMiniMax({\n                model: \"abab6.5-chat\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: \"ping\"\n                    }\n                ],\n                maxTokens: 10\n            });\n            results.minimax = true;\n        } catch (error) {\n            this.logger.warn(\"MiniMax健康检查失败\", {\n                error\n            });\n        }\n        // 检查DeepSeek\n        try {\n            await this.callDeepSeek({\n                model: \"deepseek-chat\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: \"ping\"\n                    }\n                ],\n                maxTokens: 10\n            });\n            results.deepseek = true;\n        } catch (error) {\n            this.logger.warn(\"DeepSeek健康检查失败\", {\n                error\n            });\n        }\n        return results;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llm/llm-api-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llm/prompt-builder.ts":
/*!********************************************!*\
  !*** ./src/services/llm/prompt-builder.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PromptBuilder: () => (/* binding */ PromptBuilder)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n// 提示词模板Schema\nconst PromptTemplateSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    systemPrompt: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    userPromptTemplate: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    variables: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.string()),\n    version: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    metadata: zod__WEBPACK_IMPORTED_MODULE_1__.record(zod__WEBPACK_IMPORTED_MODULE_1__.any()).optional()\n});\n/**\n * 提示词构建器类\n */ class PromptBuilder {\n    constructor(config = {}){\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"PromptBuilder\");\n        this.templates = new Map();\n        this.config = {\n            templatePath: \"./config/prompts\",\n            cacheEnabled: true,\n            maxTokens: 4000,\n            temperature: 0.7,\n            ...config\n        };\n        // 初始化默认模板\n        this.initializeDefaultTemplates();\n    }\n    /**\n   * 获取默认配置\n   */ getDefaultConfig() {\n        return {\n            templatePath: \"./config/prompts\",\n            cacheEnabled: true,\n            maxTokens: 4000,\n            temperature: 0.7\n        };\n    }\n    /**\n   * 初始化默认提示词模板\n   */ initializeDefaultTemplates() {\n        // 问卷设计师提示词模板\n        this.addTemplate({\n            id: \"questionnaire_designer\",\n            name: \"问卷设计师\",\n            systemPrompt: `你是OCTI组织能力评估系统的专业问卷设计师。你的任务是根据用户需求设计高质量的评估问卷。\n\n核心要求：\n1. 严格遵循OCTI四维八极理论框架\n2. 问题设计要具有科学性和专业性\n3. 确保问题的区分度和信效度\n4. 输出标准JSON格式的问卷结构\n\nOCTI四维八极框架：\n- S/F维度：结构化 ↔ 灵活化\n- I/T维度：直觉 ↔ 思考  \n- M/V维度：管理 ↔ 愿景\n- A/D维度：行动 ↔ 深思\n\n请确保每个维度的问题数量均衡，问题表述清晰准确。`,\n            userPromptTemplate: `请为以下评估需求设计问卷：\n\n评估类型：{{assessmentType}}\n目标维度：{{dimensions}}\n问卷版本：{{version}}\n特殊要求：{{requirements}}\n\n请生成包含以下结构的JSON格式问卷：\n{\n  \"id\": \"问卷唯一标识\",\n  \"title\": \"问卷标题\",\n  \"description\": \"问卷描述\",\n  \"version\": \"版本信息\",\n  \"questions\": [\n    {\n      \"id\": \"问题ID\",\n      \"text\": \"问题内容\",\n      \"type\": \"问题类型\",\n      \"dimension\": \"所属维度\",\n      \"subdimension\": \"子维度\",\n      \"options\": [选项数组],\n      \"weight\": 权重值\n    }\n  ],\n  \"metadata\": {\n    \"estimatedTime\": 预估完成时间,\n    \"totalQuestions\": 问题总数,\n    \"dimensionDistribution\": 维度分布统计\n  }\n}`,\n            variables: [\n                \"assessmentType\",\n                \"dimensions\",\n                \"version\",\n                \"requirements\"\n            ]\n        });\n        // 组织评估导师提示词模板\n        this.addTemplate({\n            id: \"organization_tutor_standard\",\n            name: \"组织评估导师-标准版\",\n            systemPrompt: `你是OCTI组织能力评估系统的专业分析师。你的任务是基于问卷回答结果，提供深入的组织能力分析和改进建议。\n\n分析框架：\n1. OCTI四维八极得分计算\n2. 组织能力优势识别\n3. 改进机会分析\n4. 具体行动建议\n\n输出要求：\n- 客观准确的数据分析\n- 实用可行的改进建议\n- 结构化的报告格式\n- 专业的语言表达`,\n            userPromptTemplate: `请分析以下组织评估数据：\n\n组织信息：{{organizationInfo}}\n问卷回答：{{responses}}\n评估版本：{{version}}\n\n请提供包含以下内容的分析报告：\n1. 总体评估得分\n2. 四维八极详细分析\n3. 组织优势总结\n4. 改进建议\n5. 下一步行动计划`,\n            variables: [\n                \"organizationInfo\",\n                \"responses\",\n                \"version\"\n            ]\n        });\n        this.logger.info(`已加载 ${this.templates.size} 个默认提示词模板`);\n    }\n    /**\n   * 添加提示词模板\n   */ addTemplate(template) {\n        const validated = PromptTemplateSchema.parse(template);\n        this.templates.set(validated.id, validated);\n        this.logger.debug(`已添加提示词模板: ${validated.id}`);\n    }\n    /**\n   * 获取提示词模板\n   */ getTemplate(id) {\n        return this.templates.get(id) || null;\n    }\n    /**\n   * 构建提示词\n   */ buildPrompt(templateId, variables) {\n        const template = this.getTemplate(templateId);\n        if (!template) {\n            throw new Error(`提示词模板不存在: ${templateId}`);\n        }\n        // 检查必需变量\n        const missingVars = template.variables.filter((varName)=>!(varName in variables) || variables[varName] === undefined);\n        if (missingVars.length > 0) {\n            throw new Error(`缺少必需变量: ${missingVars.join(\", \")}`);\n        }\n        // 替换变量\n        let userPrompt = template.userPromptTemplate;\n        for (const [key, value] of Object.entries(variables)){\n            const placeholder = `{{${key}}}`;\n            const replacement = typeof value === \"string\" ? value : JSON.stringify(value);\n            userPrompt = userPrompt.replace(new RegExp(placeholder, \"g\"), replacement);\n        }\n        this.logger.debug(`已构建提示词: ${templateId}`, {\n            variablesUsed: Object.keys(variables)\n        });\n        return {\n            systemPrompt: template.systemPrompt,\n            userPrompt\n        };\n    }\n    /**\n   * 列出所有模板\n   */ listTemplates() {\n        return Array.from(this.templates.values());\n    }\n    /**\n   * 验证提示词安全性\n   */ validatePromptSafety(prompt) {\n        const issues = [];\n        // 检查潜在的注入攻击\n        const dangerousPatterns = [\n            /ignore\\s+previous\\s+instructions/i,\n            /system\\s*:\\s*you\\s+are\\s+now/i,\n            /forget\\s+everything/i,\n            /new\\s+instructions/i,\n            /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n            /javascript:/i,\n            /on\\w+\\s*=/i\n        ];\n        for (const pattern of dangerousPatterns){\n            if (pattern.test(prompt)) {\n                issues.push(`检测到潜在的注入攻击模式: ${pattern.source}`);\n            }\n        }\n        // 检查敏感信息泄露\n        const sensitivePatterns = [\n            /api[_-]?key/i,\n            /password/i,\n            /secret/i,\n            /token/i,\n            /credential/i\n        ];\n        for (const pattern of sensitivePatterns){\n            if (pattern.test(prompt)) {\n                issues.push(`检测到可能的敏感信息: ${pattern.source}`);\n            }\n        }\n        return {\n            safe: issues.length === 0,\n            issues\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llm/prompt-builder.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fquestionnaire%2Fbatch%2Froute&page=%2Fapi%2Fquestionnaire%2Fbatch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fquestionnaire%2Fbatch%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();