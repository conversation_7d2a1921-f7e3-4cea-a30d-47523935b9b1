"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/agents/route";
exports.ids = ["app/api/agents/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_agents_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/agents/route.ts */ \"(rsc)/./src/app/api/agents/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/agents/route\",\n        pathname: \"/api/agents\",\n        filename: \"route\",\n        bundlePath: \"app/api/agents/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/agents/route.ts\",\n    nextConfigOutput,\n    userland: _Users_apple_Documents_2_1_AI_Journey_Cursor_projects_octi_test_src_app_api_agents_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/agents/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/agents/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/agents/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _services_api_ApiService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/api/ApiService */ \"(rsc)/./src/services/api/ApiService.ts\");\n\n\nlet apiService = null;\n/**\n * 获取API服务实例\n */ async function getApiService() {\n    if (!apiService) {\n        apiService = _services_api_ApiService__WEBPACK_IMPORTED_MODULE_1__.ApiService.getInstance();\n        await apiService.initialize();\n    }\n    return apiService;\n}\n/**\n * GET /api/agents - 获取智能体状态或列表\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const agentName = searchParams.get(\"agent\");\n        const action = searchParams.get(\"action\") || \"list\";\n        const service = await getApiService();\n        const apiRequest = {\n            action: action,\n            agentName: agentName || undefined\n        };\n        const result = await service.handleAgentRequest(apiRequest);\n        if (result.success) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(result, {\n                status: 200\n            });\n        } else {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(result, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        console.error(\"智能体API GET请求失败:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"服务器内部错误\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * POST /api/agents/execute - 执行智能体任务\n */ async function POST(request) {\n    try {\n        const body = await request.json();\n        const { agentName, input } = body;\n        if (!agentName || !input) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"缺少必要参数: agentName 或 input\"\n            }, {\n                status: 400\n            });\n        }\n        const service = await getApiService();\n        const apiRequest = {\n            action: \"execute\",\n            agentName,\n            input\n        };\n        const result = await service.handleAgentRequest(apiRequest);\n        if (result.success) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(result, {\n                status: 200\n            });\n        } else {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(result, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        console.error(\"智能体执行失败:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"服务器内部错误\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/agents/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cache.ts":
/*!**************************!*\
  !*** ./src/lib/cache.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MemoryCache: () => (/* binding */ MemoryCache),\n/* harmony export */   RedisCache: () => (/* binding */ RedisCache)\n/* harmony export */ });\n/**\n * Redis缓存服务\n * 在开发阶段使用内存缓存模拟Redis功能\n */ class RedisCache {\n    constructor(config){\n        this.cache = new Map();\n        this.cleanupInterval = null;\n        const defaultConfig = {\n            ttl: 3600,\n            strategy: \"ttl\"\n        };\n        this.config = {\n            ...defaultConfig,\n            ...config\n        };\n        // 启动清理定时器\n        this.startCleanup();\n    }\n    /**\n   * 获取缓存值\n   */ async get(key) {\n        const fullKey = this.getFullKey(key);\n        const entry = this.cache.get(fullKey);\n        if (!entry) {\n            return null;\n        }\n        // 检查是否过期\n        if (this.isExpired(entry)) {\n            this.cache.delete(fullKey);\n            return null;\n        }\n        // 更新访问时间\n        entry.accessedAt = new Date();\n        return entry.value;\n    }\n    /**\n   * 设置缓存值\n   */ async set(key, value, ttl) {\n        const fullKey = this.getFullKey(key);\n        const now = new Date();\n        const entry = {\n            key: fullKey,\n            value,\n            ttl: ttl || this.config.ttl,\n            createdAt: now,\n            accessedAt: now\n        };\n        this.cache.set(fullKey, entry);\n        // 如果超过最大大小，清理旧条目\n        if (this.config.maxSize && this.cache.size > this.config.maxSize) {\n            this.evictEntries();\n        }\n    }\n    /**\n   * 删除缓存\n   */ async delete(key) {\n        const fullKey = this.getFullKey(key);\n        return this.cache.delete(fullKey);\n    }\n    /**\n   * 检查键是否存在\n   */ async has(key) {\n        const fullKey = this.getFullKey(key);\n        const entry = this.cache.get(fullKey);\n        if (!entry) {\n            return false;\n        }\n        if (this.isExpired(entry)) {\n            this.cache.delete(fullKey);\n            return false;\n        }\n        return true;\n    }\n    /**\n   * 清空所有缓存\n   */ async clear() {\n        this.cache.clear();\n    }\n    /**\n   * 获取缓存大小\n   */ async size() {\n        return this.cache.size;\n    }\n    /**\n   * 获取所有键\n   */ async keys() {\n        const keys = [];\n        const entries = Array.from(this.cache.entries());\n        for (const [key, entry] of entries){\n            if (!this.isExpired(entry)) {\n                keys.push(key.replace(this.config.keyPrefix || \"\", \"\"));\n            }\n        }\n        return keys;\n    }\n    /**\n   * 批量获取\n   */ async mget(keys) {\n        const results = [];\n        for (const key of keys){\n            results.push(await this.get(key));\n        }\n        return results;\n    }\n    /**\n   * 批量设置\n   */ async mset(entries) {\n        for (const entry of entries){\n            await this.set(entry.key, entry.value, entry.ttl);\n        }\n    }\n    /**\n   * 设置过期时间\n   */ async expire(key, ttl) {\n        const fullKey = this.getFullKey(key);\n        const entry = this.cache.get(fullKey);\n        if (!entry) {\n            return false;\n        }\n        entry.ttl = ttl;\n        entry.createdAt = new Date();\n        return true;\n    }\n    /**\n   * 获取剩余过期时间\n   */ async ttl(key) {\n        const fullKey = this.getFullKey(key);\n        const entry = this.cache.get(fullKey);\n        if (!entry) {\n            return -2 // 键不存在\n            ;\n        }\n        const elapsed = Date.now() - entry.createdAt.getTime();\n        const remaining = entry.ttl * 1000 - elapsed;\n        if (remaining <= 0) {\n            return -1 // 已过期\n            ;\n        }\n        return Math.ceil(remaining / 1000);\n    }\n    /**\n   * 销毁缓存服务\n   */ destroy() {\n        if (this.cleanupInterval) {\n            clearInterval(this.cleanupInterval);\n            this.cleanupInterval = null;\n        }\n        this.cache.clear();\n    }\n    /**\n   * 获取完整键名\n   */ getFullKey(key) {\n        return this.config.keyPrefix ? `${this.config.keyPrefix}${key}` : key;\n    }\n    /**\n   * 检查条目是否过期\n   */ isExpired(entry) {\n        const elapsed = Date.now() - entry.createdAt.getTime();\n        return elapsed > entry.ttl * 1000;\n    }\n    /**\n   * 清理过期条目\n   */ cleanup() {\n        const now = Date.now();\n        const keysToDelete = [];\n        const entries = Array.from(this.cache.entries());\n        for (const [key, entry] of entries){\n            if (this.isExpired(entry)) {\n                keysToDelete.push(key);\n            }\n        }\n        for (const key of keysToDelete){\n            this.cache.delete(key);\n        }\n    }\n    /**\n   * 启动清理定时器\n   */ startCleanup() {\n        // 每分钟清理一次过期条目\n        this.cleanupInterval = setInterval(()=>{\n            this.cleanup();\n        }, 60000);\n    }\n    /**\n   * 根据策略清理条目\n   */ evictEntries() {\n        if (!this.config.maxSize) {\n            return;\n        }\n        const entriesToRemove = this.cache.size - this.config.maxSize;\n        if (entriesToRemove <= 0) {\n            return;\n        }\n        const entries = Array.from(this.cache.entries());\n        switch(this.config.strategy){\n            case \"lru\":\n                // 按访问时间排序，删除最久未访问的\n                entries.sort((a, b)=>a[1].accessedAt.getTime() - b[1].accessedAt.getTime());\n                break;\n            case \"fifo\":\n                // 按创建时间排序，删除最早创建的\n                entries.sort((a, b)=>a[1].createdAt.getTime() - b[1].createdAt.getTime());\n                break;\n            case \"ttl\":\n            default:\n                // 按剩余TTL排序，删除最快过期的\n                entries.sort((a, b)=>{\n                    const aTtl = a[1].ttl * 1000 - (Date.now() - a[1].createdAt.getTime());\n                    const bTtl = b[1].ttl * 1000 - (Date.now() - b[1].createdAt.getTime());\n                    return aTtl - bTtl;\n                });\n                break;\n        }\n        for(let i = 0; i < entriesToRemove; i++){\n            this.cache.delete(entries[i][0]);\n        }\n    }\n}\n/**\n * 内存缓存服务（用于开发和测试）\n */ class MemoryCache extends RedisCache {\n    constructor(config){\n        super({\n            ttl: 3600,\n            strategy: \"lru\",\n            maxSize: 1000,\n            ...config\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cache.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Logger: () => (/* binding */ Logger),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/**\n * 日志服务\n */ class Logger {\n    constructor(context = \"App\"){\n        this.context = context;\n    }\n    formatMessage(level, message, meta) {\n        const timestamp = new Date().toISOString();\n        const metaStr = meta ? ` ${JSON.stringify(meta)}` : \"\";\n        return `[${timestamp}] [${level.toUpperCase()}] [${this.context}] ${message}${metaStr}`;\n    }\n    debug(message, meta) {\n        if (process.env.LOG_LEVEL === \"debug\") {\n            console.debug(this.formatMessage(\"debug\", message, meta));\n        }\n    }\n    info(message, meta) {\n        console.info(this.formatMessage(\"info\", message, meta));\n    }\n    warn(message, meta) {\n        console.warn(this.formatMessage(\"warn\", message, meta));\n    }\n    error(message, meta) {\n        console.error(this.formatMessage(\"error\", message, meta));\n    }\n    setContext(context) {\n        this.context = context;\n    }\n}\n// 创建默认日志器实例\nconst logger = new Logger(\"OCTI\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/agents/AgentService.ts":
/*!*********************************************!*\
  !*** ./src/services/agents/AgentService.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgentManager: () => (/* binding */ AgentManager),\n/* harmony export */   AgentService: () => (/* binding */ AgentService)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var _QuestionDesignerAgent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QuestionDesignerAgent */ \"(rsc)/./src/services/agents/QuestionDesignerAgent.ts\");\n/* harmony import */ var _OrganizationTutorAgent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OrganizationTutorAgent */ \"(rsc)/./src/services/agents/OrganizationTutorAgent.ts\");\n/* harmony import */ var _llm_llm_api_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../llm/llm-api-client */ \"(rsc)/./src/services/llm/llm-api-client.ts\");\n/* harmony import */ var _llm_prompt_builder__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../llm/prompt-builder */ \"(rsc)/./src/services/llm/prompt-builder.ts\");\n/* harmony import */ var _data_data_fusion_engine__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../data/data-fusion-engine */ \"(rsc)/./src/services/data/data-fusion-engine.ts\");\n\n\n\n// 添加缺少的导入\n\n\n\n/**\n * 智能体服务基类\n * 提供智能体的基础功能和管理\n */ class AgentService {\n    constructor(agentName){\n        this.isInitialized = false;\n        this.agentName = agentName;\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(`Agent:${agentName}`);\n        // ConfigService.getInstance()返回Promise，需要在initialize中处理\n        this.configService = null; // 临时设置，在initialize中正确初始化\n        // ✅ 使用默认配置初始化\n        this.llmClient = new _llm_llm_api_client__WEBPACK_IMPORTED_MODULE_3__.LLMApiClient();\n        this.promptBuilder = new _llm_prompt_builder__WEBPACK_IMPORTED_MODULE_4__.PromptBuilder();\n        this.dataFusionEngine = new _data_data_fusion_engine__WEBPACK_IMPORTED_MODULE_5__.DataFusionEngine();\n    }\n    /**\n   * 使用自定义配置初始化服务\n   */ initializeWithConfig(llmConfig, promptConfig, fusionConfig) {\n        if (llmConfig) {\n            this.llmClient = new _llm_llm_api_client__WEBPACK_IMPORTED_MODULE_3__.LLMApiClient(llmConfig);\n        }\n        if (promptConfig) {\n            this.promptBuilder = new _llm_prompt_builder__WEBPACK_IMPORTED_MODULE_4__.PromptBuilder(promptConfig);\n        }\n        if (fusionConfig) {\n            this.dataFusionEngine = new _data_data_fusion_engine__WEBPACK_IMPORTED_MODULE_5__.DataFusionEngine(fusionConfig);\n        }\n    }\n    /**\n   * 初始化智能体\n   */ async initialize() {\n        if (this.isInitialized) {\n            this.logger.warn(\"智能体已经初始化\");\n            return;\n        }\n        try {\n            this.logger.info(\"开始初始化智能体\");\n            await this.onInitialize();\n            this.isInitialized = true;\n            this.logger.info(\"智能体初始化完成\");\n        } catch (error) {\n            this.logger.error(\"智能体初始化失败\", {\n                error\n            });\n            throw error;\n        }\n    }\n    /**\n   * 执行智能体任务\n   */ async execute(input) {\n        if (!this.isInitialized) {\n            throw new Error(\"智能体未初始化\");\n        }\n        try {\n            this.logger.info(\"开始执行智能体任务\", {\n                input\n            });\n            const result = await this.onExecute(input);\n            this.logger.info(\"智能体任务执行完成\", {\n                result\n            });\n            return result;\n        } catch (error) {\n            this.logger.error(\"智能体任务执行失败\", {\n                error,\n                input\n            });\n            throw error;\n        }\n    }\n    /**\n   * 获取智能体状态\n   */ getStatus() {\n        return {\n            name: this.agentName,\n            initialized: this.isInitialized,\n            lastActivity: new Date(),\n            config: this.getAgentConfig()\n        };\n    }\n    /**\n   * 获取智能体配置\n   */ getAgentConfig() {\n        const configKey = `octi.agents.${this.agentName.toLowerCase().replace(/\\s+/g, \"_\")}`;\n        return this.configService.get(configKey, {});\n    }\n}\n/**\n * 智能体管理器\n * 负责管理所有智能体的生命周期\n */ class AgentManager {\n    constructor(){\n        this.agents = new Map();\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"AgentManager\");\n    }\n    async initialize() {\n        try {\n            this.logger.info(\"开始初始化智能体管理器\");\n            // 创建依赖项实例\n            const llmClient = new _llm_llm_api_client__WEBPACK_IMPORTED_MODULE_3__.LLMApiClient();\n            const promptBuilder = new _llm_prompt_builder__WEBPACK_IMPORTED_MODULE_4__.PromptBuilder();\n            const dataFusionEngine = new _data_data_fusion_engine__WEBPACK_IMPORTED_MODULE_5__.DataFusionEngine();\n            // 注册智能体\n            this.registerAgent(new _QuestionDesignerAgent__WEBPACK_IMPORTED_MODULE_1__.QuestionDesignerAgent(llmClient, promptBuilder, dataFusionEngine));\n            this.registerAgent(new _OrganizationTutorAgent__WEBPACK_IMPORTED_MODULE_2__.OrganizationTutorAgent(llmClient, promptBuilder, dataFusionEngine));\n            // 初始化所有智能体\n            for (const [name, agent] of Array.from(this.agents.entries())){\n                await agent.initialize();\n                this.logger.info(`智能体 ${name} 初始化完成`);\n            }\n            this.logger.info(\"智能体管理器初始化完成\");\n        } catch (error) {\n            this.logger.error(\"智能体管理器初始化失败\", {\n                error\n            });\n            throw error;\n        }\n    }\n    /**\n   * 注册智能体\n   */ registerAgent(agent) {\n        this.agents.set(agent.name, agent);\n        this.logger.info(`注册智能体: ${agent.name}`);\n    }\n    /**\n   * 获取智能体 - 增强类型安全\n   */ getAgent(name) {\n        return this.agents.get(name);\n    }\n    /**\n   * 执行智能体任务 - 保持向后兼容\n   */ async executeAgent(name, input) {\n        const agent = this.getAgent(name);\n        if (!agent) {\n            throw new Error(`智能体不存在: ${name}`);\n        }\n        // 根据智能体类型调用相应方法\n        if (name === \"question_designer\") {\n            const questionAgent = agent;\n            return await questionAgent.designQuestionnaire(input);\n        } else if (name === \"organization_tutor\") {\n            const tutorAgent = agent;\n            return await tutorAgent.generateAssessment(input.assessmentData, input.options);\n        }\n        throw new Error(`不支持的智能体操作: ${name}`);\n    }\n    /**\n   * 获取所有智能体状态\n   */ getAllStatus() {\n        const status = {};\n        for (const [name, agent] of Array.from(this.agents.entries())){\n            status[name] = agent.getStatus();\n        }\n        return status;\n    }\n    /**\n   * 获取可用的智能体列表\n   */ getAvailableAgents() {\n        return Array.from(this.agents.keys());\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/agents/AgentService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/agents/OrganizationTutorAgent.ts":
/*!*******************************************************!*\
  !*** ./src/services/agents/OrganizationTutorAgent.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrganizationTutorAgent: () => (/* binding */ OrganizationTutorAgent),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n\nconst logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"OrganizationTutorAgent\");\nclass OrganizationTutorAgent {\n    constructor(llmClient, promptBuilder, dataFusionEngine){\n        this.name = \"organization_tutor\";\n        this.resultCache = new Map();\n        this.cacheExpiry = 24 * 60 * 60 * 1000 // 24小时\n        ;\n        this.isInitialized = false;\n        this.llmClient = llmClient;\n        this.promptBuilder = promptBuilder;\n        this.dataFusionEngine = dataFusionEngine;\n    }\n    // 添加缺失的初始化方法\n    async initialize() {\n        if (this.isInitialized) {\n            logger.warn(\"OrganizationTutorAgent 已经初始化\");\n            return;\n        }\n        try {\n            // 验证必需的依赖项\n            if (!this.llmClient) {\n                throw new Error(\"LLMApiClient 是必需的依赖项\");\n            }\n            if (!this.promptBuilder) {\n                throw new Error(\"PromptBuilder 是必需的依赖项\");\n            }\n            if (!this.dataFusionEngine) {\n                throw new Error(\"DataFusionEngine 是必需的依赖项\");\n            }\n            // 清理过期缓存\n            this.cleanExpiredCache();\n            this.isInitialized = true;\n            logger.info(\"OrganizationTutorAgent 初始化完成\");\n        } catch (error) {\n            logger.error(\"OrganizationTutorAgent 初始化失败\", {\n                error\n            });\n            throw error;\n        }\n    }\n    /**\n   * 实现BaseAgent接口的execute方法\n   */ async execute(input) {\n        return await this.generateAssessment(input.assessmentData, input.options);\n    }\n    /**\n   * 实现BaseAgent接口的getStatus方法\n   */ getStatus() {\n        return {\n            name: this.name,\n            initialized: this.isInitialized,\n            lastActivity: new Date(),\n            config: {\n                cacheExpiry: this.cacheExpiry,\n                cacheSize: this.resultCache.size\n            }\n        };\n    }\n    // 生成评估报告\n    async generateAssessment(assessmentData, options = {\n        version: \"standard\",\n        analysisMode: \"basic\",\n        includeRecommendations: true,\n        outputLanguage: \"zh\"\n    }) {\n        if (!this.isInitialized) {\n            throw new Error(\"OrganizationTutorAgent 未初始化\");\n        }\n        // 输入验证\n        if (!assessmentData || !assessmentData.organizationId) {\n            throw new Error(\"评估数据无效：缺少组织ID\");\n        }\n        if (!assessmentData.responses || assessmentData.responses.length === 0) {\n            throw new Error(\"评估数据无效：缺少响应数据\");\n        }\n        const startTime = Date.now();\n        try {\n            // 检查缓存\n            const cacheKey = this.generateCacheKey(assessmentData, options);\n            const cached = this.resultCache.get(cacheKey);\n            if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {\n                logger.info(\"使用缓存的评估结果\", {\n                    cacheKey\n                });\n                return cached.data;\n            }\n            // 数据预处理\n            const processedData = this.preprocessAssessmentData(assessmentData);\n            // 数据融合（如果有外部数据）\n            let fusedData = null;\n            if (options.externalData && options.externalData.length > 0) {\n                fusedData = await this.dataFusionEngine.fuseData(options.externalData.map((source)=>({\n                        sourceId: source.source,\n                        content: JSON.stringify(source.data),\n                        contentType: source.type,\n                        metadata: {\n                            timestamp: source.timestamp,\n                            reliability: source.reliability\n                        }\n                    })), options.dataFusion);\n            }\n            // 构建提示词变量\n            const promptVariables = {\n                version: options.version,\n                assessmentData: JSON.stringify(processedData),\n                analysisMode: options.analysisMode || \"basic\",\n                includeRecommendations: options.includeRecommendations ? \"是\" : \"否\",\n                customFocus: options.customFocus || \"无\",\n                outputLanguage: options.outputLanguage || \"zh\",\n                organizationType: assessmentData.metadata.organizationType || \"未指定\",\n                industryContext: assessmentData.metadata.industryContext || \"通用行业\"\n            };\n            const prompt = this.promptBuilder.buildPrompt(\"organization_tutor\", promptVariables);\n            // 调用LLM生成评估\n            let llmResponse;\n            if (options.version === \"professional\") {\n                // 专业版：MiniMax 先评估 → DeepSeek 再总结\n                llmResponse = await this.runProfessionalDualModelAnalysis(prompt, assessmentData);\n            } else {\n                // 标准版：仅使用 MiniMax M1\n                const standardRequest = {\n                    model: \"MiniMax-M1\",\n                    messages: [\n                        {\n                            role: \"system\",\n                            content: prompt.systemPrompt\n                        },\n                        {\n                            role: \"user\",\n                            content: prompt.userPrompt\n                        }\n                    ],\n                    temperature: 0.3,\n                    maxTokens: 4000\n                };\n                llmResponse = await this.llmClient.chat(\"minimax\", standardRequest);\n            }\n            // 解析响应\n            const report = await this.parseAssessmentResponse(llmResponse.content || \"\", assessmentData, options);\n            // 缓存结果\n            this.resultCache.set(cacheKey, {\n                data: report,\n                timestamp: Date.now()\n            });\n            const processingTime = Date.now() - startTime;\n            logger.info(\"评估报告生成完成\", {\n                organizationId: assessmentData.organizationId,\n                version: options.version,\n                processingTime\n            });\n            return report;\n        } catch (error) {\n            logger.error(\"评估报告生成失败\", {\n                error: error instanceof Error ? error.message : String(error),\n                organizationId: assessmentData.organizationId,\n                options\n            });\n            throw new Error(`评估报告生成失败: ${error instanceof Error ? error.message : \"未知错误\"}`);\n        }\n    }\n    /**\n   * 专业版双模型分析：MiniMax 先评估 → DeepSeek 再总结\n   */ async runProfessionalDualModelAnalysis(prompt, assessmentData) {\n        logger.info(\"开始专业版双模型分析\");\n        // 第一步：使用 MiniMax M1 进行初步评估\n        const minimaxRequest = {\n            model: \"MiniMax-M1\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: prompt.systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: prompt.userPrompt\n                }\n            ],\n            temperature: 0.3,\n            maxTokens: 6000\n        };\n        logger.info(\"第一步：MiniMax M1 初步评估\");\n        const minimaxResponse = await this.llmClient.chat(\"minimax\", minimaxRequest);\n        // 第二步：使用 DeepSeek 对 MiniMax 的结果进行深度总结和优化\n        const deepseekPrompt = this.buildDeepSeekSummaryPrompt(minimaxResponse.content || \"\", assessmentData);\n        const deepseekRequest = {\n            model: \"deepseek-reasoner\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: deepseekPrompt.systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: deepseekPrompt.userPrompt\n                }\n            ],\n            temperature: 0.2,\n            maxTokens: 8000\n        };\n        logger.info(\"第二步：DeepSeek 深度总结\");\n        const deepseekResponse = await this.llmClient.chat(\"deepseek\", deepseekRequest);\n        // 返回 DeepSeek 的最终结果\n        return deepseekResponse;\n    }\n    /**\n   * 构建 DeepSeek 总结提示词\n   */ buildDeepSeekSummaryPrompt(minimaxResult, assessmentData) {\n        const systemPrompt = `你是一位资深的组织发展专家和数据分析师，专门负责对OCTI评估报告进行深度总结和优化。\n\n你的任务是：\n1. 分析MiniMax模型生成的初步评估报告\n2. 提取关键洞察和发现\n3. 优化报告结构和表达\n4. 增加深层次的分析和建议\n5. 确保报告的专业性和实用性\n\n请保持客观、专业的分析态度，提供具有实际指导价值的建议。`;\n        const userPrompt = `请对以下MiniMax生成的OCTI评估报告进行深度总结和优化：\n\n## MiniMax初步评估报告：\n${minimaxResult}\n\n## 评估数据概览：\n- 组织类型：${assessmentData.metadata?.organizationType || \"未指定\"}\n- 评估时间：${assessmentData.metadata?.createdAt || \"未指定\"}\n- 题目数量：${assessmentData.questions?.length || 0}\n\n## 请按以下要求优化报告：\n\n1. **深度分析**：对四个维度的表现进行更深入的解读\n2. **关联分析**：分析维度间的相互影响和潜在关联\n3. **风险识别**：识别组织可能面临的潜在风险和挑战\n4. **机遇发现**：基于优势分析，识别发展机遇\n5. **行动建议**：提供具体、可操作的改进建议\n6. **资源推荐**：推荐相关的学习资源和工具\n\n请确保最终报告：\n- 结构清晰，逻辑严密\n- 语言专业但易懂\n- 建议具体可行\n- 符合专业版报告的高标准\n\n请以JSON格式返回优化后的完整报告。`;\n        return {\n            systemPrompt,\n            userPrompt\n        };\n    }\n    // 添加缺失的辅助方法\n    generateCacheKey(data, options) {\n        const keyParts = [\n            data.organizationId,\n            options.version,\n            options.analysisMode,\n            JSON.stringify(options.customFocus || []),\n            data.metadata.completedAt\n        ];\n        return keyParts.join(\"_\").replace(/[^a-zA-Z0-9_]/g, \"_\");\n    }\n    preprocessAssessmentData(data) {\n        // 实现数据预处理逻辑\n        return {\n            organizationId: data.organizationId,\n            responses: data.responses.map((r)=>({\n                    questionId: r.questionId,\n                    answer: r.answer,\n                    dimension: r.dimension,\n                    subdimension: r.subdimension\n                })),\n            metadata: data.metadata\n        };\n    }\n    async parseAssessmentResponse(content, assessmentData, options) {\n        // 实现响应解析逻辑\n        try {\n            const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/) || content.match(/\\{[\\s\\S]*\\}/);\n            let parsedContent;\n            if (jsonMatch) {\n                parsedContent = JSON.parse(jsonMatch[1] || jsonMatch[0]);\n            } else {\n                // 结构化解析\n                parsedContent = this.parseStructuredReport(content);\n            }\n            return {\n                id: `report_${Date.now()}`,\n                organizationId: assessmentData.organizationId,\n                version: options.version,\n                overallScore: parsedContent.overallScore || 0,\n                dimensionScores: parsedContent.dimensionScores || {},\n                strengths: parsedContent.strengths || [],\n                improvements: parsedContent.improvements || [],\n                recommendations: parsedContent.recommendations || [],\n                nextSteps: parsedContent.nextSteps || [],\n                metadata: {\n                    generatedAt: new Date(),\n                    analysisMode: options.analysisMode,\n                    dataSourcesUsed: options.externalData?.map((d)=>d.source) || []\n                }\n            };\n        } catch (error) {\n            logger.error(\"评估响应解析失败\", {\n                error,\n                content: content.substring(0, 500)\n            });\n            throw new Error(\"评估响应解析失败\");\n        }\n    }\n    parseStructuredReport(content) {\n        // 实现结构化报告解析\n        return {\n            overallScore: 75,\n            dimensionScores: {},\n            strengths: [],\n            improvements: [],\n            recommendations: [],\n            nextSteps: []\n        };\n    }\n    // 添加缓存清理方法\n    cleanExpiredCache() {\n        const now = Date.now();\n        Array.from(this.resultCache.entries()).forEach(([key, entry])=>{\n            if (now - entry.timestamp > this.cacheExpiry) {\n                this.resultCache.delete(key);\n            }\n        });\n    }\n    // 获取缓存统计\n    getCacheStats() {\n        const entries = Array.from(this.resultCache.values());\n        const oldestEntry = entries.length > 0 ? entries.reduce((oldest, entry)=>new Date(entry.timestamp) < new Date(oldest.timestamp) ? entry : oldest).timestamp.toString() : null;\n        return {\n            size: this.resultCache.size,\n            hitRate: 0,\n            oldestEntry\n        };\n    }\n    // 清空缓存\n    clearCache() {\n        this.resultCache.clear();\n        logger.info(\"Assessment cache cleared\");\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrganizationTutorAgent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/agents/OrganizationTutorAgent.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/agents/QuestionDesignerAgent.ts":
/*!******************************************************!*\
  !*** ./src/services/agents/QuestionDesignerAgent.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionDesignerAgent: () => (/* binding */ QuestionDesignerAgent)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n\nconst logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"QuestionDesignerAgent\");\n/**\n * 问卷设计师智能体\n */ class QuestionDesignerAgent {\n    constructor(llmClient, promptBuilder, dataFusionEngine){\n        this.llmClient = llmClient;\n        this.promptBuilder = promptBuilder;\n        this.dataFusionEngine = dataFusionEngine;\n        this.name = \"question_designer\";\n        this.initialized = false;\n        this.lastActivity = new Date();\n        this.questionCache = new Map();\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"QuestionDesignerAgent\");\n    }\n    /**\n   * 初始化智能体\n   */ async initialize() {\n        this.logger.info(\"问卷设计师智能体初始化完成\");\n        this.initialized = true;\n        this.lastActivity = new Date();\n    }\n    /**\n   * 获取智能体状态\n   */ getStatus() {\n        return {\n            name: this.name,\n            initialized: this.initialized,\n            lastActivity: this.lastActivity,\n            config: {\n                llmProvider: \"minimax\",\n                version: \"1.0.0\"\n            }\n        };\n    }\n    /**\n   * 执行智能体任务\n   */ async execute(input) {\n        this.logger.info(\"执行问卷设计任务\", {\n            input\n        });\n        this.lastActivity = new Date();\n        try {\n            // 这里应该调用现有的 designQuestionnaire 方法\n            // 为了保持兼容性，我们将输入转换为 DesignOptions 格式\n            const designOptions = {\n                version: input.version || \"standard\",\n                organizationType: input.organizationType || \"公益组织\",\n                targetAudience: input.targetAudience || \"组织成员\",\n                customRequirements: input.customRequirements || \"\",\n                questionCount: input.questionCount || 20,\n                dimensions: input.dimensions || [\n                    \"战略规划\",\n                    \"团队协作\",\n                    \"资源管理\",\n                    \"影响力\"\n                ]\n            };\n            const questionnaire = await this.design(designOptions);\n            return {\n                success: true,\n                data: questionnaire\n            };\n        } catch (error) {\n            this.logger.error(\"问卷设计失败\", {\n                error\n            });\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"未知错误\"\n            };\n        }\n    }\n    /**\n   * 设计问卷\n   */ async design(options) {\n        const startTime = Date.now();\n        try {\n            logger.info(\"开始分批设计问卷\", options);\n            // 使用分批生成策略\n            const questionnaire = await this.generateQuestionnaireBatched(options);\n            const generationTime = ((Date.now() - startTime) / 1000).toFixed(1) + \"s\";\n            // 添加元数据\n            questionnaire.metadata = {\n                generationTime,\n                model: \"deepseek-chat\",\n                provider: \"deepseek\"\n            };\n            logger.info(\"问卷设计完成\", {\n                questionCount: questionnaire.questions.length,\n                generationTime\n            });\n            return questionnaire;\n        } catch (error) {\n            logger.error(\"问卷设计失败\", {\n                error,\n                options\n            });\n            throw error;\n        }\n    }\n    /**\n   * 构建LLM提示词\n   */ buildPrompt(options) {\n        const systemPrompt = `你是OCTI（Organization Capability Type Indicator）智能评估系统的问卷设计专家。\n\nOCTI评估框架包含四个核心维度：\n1. S/F (Structure/Flexibility) - 结构化与灵活性\n2. I/T (Innovation/Tradition) - 创新性与传统性  \n3. M/V (Management/Vision) - 管理导向与愿景导向\n4. A/D (Action/Decision) - 行动导向与决策导向\n\n请严格按照以下要求设计问卷：\n- 每个维度15道题，总共60道题\n- 每道题5个选项，分值1-5分\n- 题目要针对${options.organizationType}类型组织\n- ${options.version === \"professional\" ? \"专业版需要更深入的情境化问题\" : \"标准版使用通用性问题\"}\n- 确保题目的专业性和实用性\n\n重要：\n1. 必须返回完整的JSON格式，不要截断\n2. 使用紧凑的JSON格式，减少不必要的空格\n3. 确保所有60道题目都包含在响应中`;\n        const userPrompt = `请为${options.organizationType}类型组织设计一套${options.version}版OCTI评估问卷。\n\n具体要求：\n- 组织类型：${options.organizationType}\n- 目标受众：${options.targetAudience}\n- 版本：${options.version}\n${options.customRequirements ? `- 特殊要求：${options.customRequirements}` : \"\"}\n\n请严格按照以下JSON格式返回完整的60道题目：\n\n\\`\\`\\`json\n{\n  \"id\": \"questionnaire_${Date.now()}\",\n  \"title\": \"问卷标题\",\n  \"description\": \"问卷描述\",\n  \"version\": \"${options.version}\",\n  \"organizationType\": \"${options.organizationType}\",\n  \"assessmentType\": \"${options.version === \"professional\" ? \"professional\" : \"basic\"}\",\n  \"dimensions\": [\"S/F\", \"I/T\", \"M/V\", \"A/D\"],\n  \"questions\": [\n    {\n      \"id\": \"q_1\",\n      \"dimension\": \"S/F\",\n      \"type\": \"single_choice\",\n      \"title\": \"问题标题\",\n      \"description\": \"问题描述\",\n      \"options\": [\n        {\"id\": \"o_1\", \"text\": \"完全不同意\", \"score\": 1},\n        {\"id\": \"o_2\", \"text\": \"不同意\", \"score\": 2},\n        {\"id\": \"o_3\", \"text\": \"中立\", \"score\": 3},\n        {\"id\": \"o_4\", \"text\": \"同意\", \"score\": 4},\n        {\"id\": \"o_5\", \"text\": \"完全同意\", \"score\": 5}\n      ],\n      \"required\": true,\n      \"order\": 1\n    }\n  ]\n}\n\\`\\`\\`\n\n注意：\n1. 必须包含完整的60道题目\n2. 每个维度15道题\n3. 确保JSON格式完整，不要截断\n4. 所有字段都必须填写完整`;\n        return {\n            systemPrompt,\n            userPrompt\n        };\n    }\n    /**\n   * 解析LLM响应\n   */ parseResponse(content, options) {\n        try {\n            console.log(\"原始LLM响应长度:\", content.length);\n            console.log(\"原始LLM响应前500字符:\", content.substring(0, 500));\n            // 检查响应是否被截断\n            if (content.length < 1000) {\n                console.warn(\"LLM响应长度过短，可能被截断:\", content.length);\n            }\n            // 尝试多种JSON提取方式\n            let jsonStr = \"\";\n            // 方式1: 提取```json代码块\n            const jsonBlockMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonBlockMatch) {\n                jsonStr = jsonBlockMatch[1].trim();\n                console.log(\"提取到JSON代码块，长度:\", jsonStr.length);\n            }\n            // 方式2: 提取完整的JSON对象\n            if (!jsonStr) {\n                const jsonObjectMatch = content.match(/\\{[\\s\\S]*\\}/);\n                if (jsonObjectMatch) {\n                    jsonStr = jsonObjectMatch[0].trim();\n                    console.log(\"提取到JSON对象，长度:\", jsonStr.length);\n                }\n            }\n            // 方式3: 查找最后一个完整的}\n            if (!jsonStr) {\n                const startIndex = content.indexOf(\"{\");\n                if (startIndex !== -1) {\n                    let braceCount = 0;\n                    let endIndex = -1;\n                    for(let i = startIndex; i < content.length; i++){\n                        if (content[i] === \"{\") braceCount++;\n                        if (content[i] === \"}\") {\n                            braceCount--;\n                            if (braceCount === 0) {\n                                endIndex = i;\n                                break;\n                            }\n                        }\n                    }\n                    if (endIndex !== -1) {\n                        jsonStr = content.substring(startIndex, endIndex + 1);\n                        console.log(\"通过括号匹配提取JSON，长度:\", jsonStr.length);\n                    }\n                }\n            }\n            if (jsonStr) {\n                try {\n                    const parsedContent = JSON.parse(jsonStr);\n                    console.log(\"JSON解析成功，问题数量:\", parsedContent.questions?.length || 0);\n                    return this.validateAndFormatQuestionnaire(parsedContent, options);\n                } catch (parseError) {\n                    console.error(\"JSON解析失败:\", parseError);\n                    console.log(\"尝试修复JSON...\");\n                    // 尝试修复常见的JSON问题\n                    const fixedJson = this.fixJsonString(jsonStr);\n                    if (fixedJson) {\n                        try {\n                            const parsedContent = JSON.parse(fixedJson);\n                            console.log(\"修复后JSON解析成功，问题数量:\", parsedContent.questions?.length || 0);\n                            return this.validateAndFormatQuestionnaire(parsedContent, options);\n                        } catch (fixError) {\n                            console.error(\"修复后仍然解析失败:\", fixError);\n                        }\n                    }\n                }\n            }\n            // 如果JSON解析都失败，使用结构化解析\n            console.log(\"JSON解析失败，使用结构化解析\");\n            return this.parseStructuredResponse(content, options);\n        } catch (error) {\n            console.error(\"响应解析完全失败:\", error);\n            logger.error(\"LLM响应解析失败\", {\n                error: error instanceof Error ? error.message : String(error),\n                contentPreview: content.substring(0, 500)\n            });\n            throw new Error(`问卷解析失败: ${error instanceof Error ? error.message : \"未知错误\"}`);\n        }\n    }\n    /**\n   * 尝试修复JSON字符串\n   */ fixJsonString(jsonStr) {\n        try {\n            // 移除可能的控制字符\n            let fixed = jsonStr.replace(/[\\x00-\\x1F\\x7F]/g, \"\");\n            // 修复常见的引号问题\n            fixed = fixed.replace(/'/g, '\"');\n            // 修复末尾缺少的括号或引号\n            if (!fixed.endsWith(\"}\")) {\n                // 尝试找到最后一个完整的对象\n                const lastCompleteObject = fixed.lastIndexOf('\"}');\n                if (lastCompleteObject !== -1) {\n                    fixed = fixed.substring(0, lastCompleteObject + 2) + \"}\";\n                }\n            }\n            // 验证修复后的JSON\n            JSON.parse(fixed);\n            return fixed;\n        } catch (error) {\n            console.error(\"JSON修复失败:\", error);\n            return null;\n        }\n    }\n    /**\n   * 验证和格式化解析后的问卷\n   */ validateAndFormatQuestionnaire(parsedContent, options) {\n        const errors = [];\n        if (!parsedContent.id) {\n            errors.push(\"问卷ID不能为空\");\n        }\n        if (!parsedContent.title) {\n            errors.push(\"问卷标题不能为空\");\n        }\n        if (!parsedContent.description) {\n            errors.push(\"问卷描述不能为空\");\n        }\n        if (!parsedContent.version) {\n            errors.push(\"问卷版本不能为空\");\n        }\n        if (!parsedContent.organizationType) {\n            errors.push(\"组织类型不能为空\");\n        }\n        if (!parsedContent.assessmentType) {\n            errors.push(\"评估类型不能为空\");\n        }\n        if (!parsedContent.dimensions || !Array.isArray(parsedContent.dimensions) || parsedContent.dimensions.length !== 4) {\n            errors.push(\"问卷必须包含四个维度\");\n        }\n        if (!parsedContent.questions || !Array.isArray(parsedContent.questions) || parsedContent.questions.length !== 60) {\n            errors.push(\"问卷必须包含60道题\");\n        }\n        if (errors.length > 0) {\n            throw new Error(`问卷解析失败：${errors.join(\", \")}`);\n        }\n        return {\n            id: parsedContent.id,\n            title: parsedContent.title,\n            description: parsedContent.description,\n            version: parsedContent.version,\n            organizationType: parsedContent.organizationType,\n            assessmentType: parsedContent.assessmentType,\n            dimensions: parsedContent.dimensions,\n            questions: parsedContent.questions.map((q, index)=>({\n                    id: q.id || `q_${index + 1}`,\n                    dimension: q.dimension,\n                    type: q.type,\n                    title: q.title,\n                    description: q.description || \"\",\n                    options: q.options.map((o, optionIndex)=>({\n                            id: o.id || `o_${index + 1}_${optionIndex + 1}`,\n                            text: o.text,\n                            score: o.score\n                        })),\n                    required: q.required || true,\n                    order: q.order || index + 1\n                })),\n            totalQuestions: parsedContent.questions.length,\n            estimatedTime: this.calculateEstimatedTime(parsedContent.questions),\n            createdAt: new Date().toISOString(),\n            metadata: {\n                generationTime: \"\",\n                model: \"\",\n                provider: \"\"\n            }\n        };\n    }\n    /**\n   * 改进的结构化解析\n   */ parseStructuredResponse(content, options) {\n        console.log(\"开始结构化解析，内容长度:\", content.length);\n        const result = {\n            id: `questionnaire_${Date.now()}`,\n            title: `${options.organizationType}组织OCTI评估问卷`,\n            description: `针对${options.organizationType}类型组织的OCTI能力评估问卷`,\n            version: options.version,\n            organizationType: options.organizationType,\n            assessmentType: options.version === \"professional\" ? \"professional\" : \"basic\",\n            dimensions: options.dimensions,\n            questions: [],\n            totalQuestions: 0,\n            estimatedTime: \"\",\n            createdAt: new Date().toISOString(),\n            metadata: {\n                generationTime: \"\",\n                model: \"\",\n                provider: \"\"\n            }\n        };\n        // 如果结构化解析也失败，生成默认问卷\n        if (result.questions.length === 0) {\n            console.log(\"结构化解析失败，生成默认问卷\");\n            result.questions = this.generateDefaultQuestions(options);\n        }\n        result.totalQuestions = result.questions.length;\n        result.estimatedTime = this.calculateEstimatedTime(result.questions);\n        console.log(\"结构化解析完成，问题数量:\", result.questions.length);\n        return result;\n    }\n    extractValue(line) {\n        const colonIndex = line.indexOf(\":\");\n        return colonIndex > -1 ? line.substring(colonIndex + 1).trim() : line;\n    }\n    // 解析问题列表\n    parseQuestions(rawQuestions) {\n        return rawQuestions.map((rawQ, index)=>{\n            const question = {\n                id: rawQ.id || `q_${index + 1}`,\n                dimension: rawQ.dimension || this.inferDimension(rawQ.text),\n                subdimension: rawQ.subdimension || \"\",\n                type: rawQ.type || this.inferQuestionType(rawQ.text),\n                depth: rawQ.depth || \"intermediate\",\n                text: rawQ.text || rawQ.question || \"\",\n                options: rawQ.options,\n                scale: rawQ.scale,\n                reversed: rawQ.reversed || false,\n                weight: rawQ.weight || 1,\n                required: rawQ.required !== false,\n                order: rawQ.order || index + 1,\n                metadata: {\n                    difficulty: rawQ.difficulty || 0.5,\n                    discriminationIndex: rawQ.discriminationIndex || 0.5,\n                    expectedResponseTime: rawQ.expectedResponseTime || 30\n                }\n            };\n            // 为选择题生成默认选项\n            if ((question.type === \"single_choice\" || question.type === \"multiple_choice\") && !question.options) {\n                question.options = this.generateDefaultOptions(question.type);\n            }\n            // 为量表题生成默认量表\n            if (question.type === \"likert_scale\" && !question.scale) {\n                question.scale = {\n                    min: 1,\n                    max: 5,\n                    labels: [\n                        \"完全不同意\",\n                        \"不同意\",\n                        \"中立\",\n                        \"同意\",\n                        \"完全同意\"\n                    ]\n                };\n            }\n            return question;\n        });\n    }\n    // 推断问题类型\n    inferQuestionType(text) {\n        if (text.includes(\"排序\") || text.includes(\"排列\")) {\n            return \"ranking\";\n        }\n        if (text.includes(\"多选\") || text.includes(\"可以选择多个\")) {\n            return \"multiple_choice\";\n        }\n        if (text.includes(\"同意\") || text.includes(\"程度\")) {\n            return \"likert_scale\";\n        }\n        if (text.includes(\"描述\") || text.includes(\"说明\") || text.includes(\"举例\")) {\n            return \"open_ended\";\n        }\n        return \"single_choice\";\n    }\n    // 推断维度\n    inferDimension(text) {\n        if (text.includes(\"权力\") || text.includes(\"等级\") || text.includes(\"层级\")) {\n            return \"权力距离\";\n        }\n        if (text.includes(\"个人\") || text.includes(\"集体\") || text.includes(\"团队\")) {\n            return \"个人主义vs集体主义\";\n        }\n        if (text.includes(\"竞争\") || text.includes(\"合作\") || text.includes(\"成就\")) {\n            return \"男性化vs女性化\";\n        }\n        if (text.includes(\"不确定\") || text.includes(\"风险\") || text.includes(\"变化\")) {\n            return \"不确定性规避\";\n        }\n        return \"综合\";\n    }\n    // 生成默认选项\n    generateDefaultOptions(type) {\n        if (type === \"single_choice\" || type === \"multiple_choice\") {\n            return [\n                {\n                    id: \"opt_1\",\n                    text: \"完全不符合\",\n                    score: 1\n                },\n                {\n                    id: \"opt_2\",\n                    text: \"基本不符合\",\n                    score: 2\n                },\n                {\n                    id: \"opt_3\",\n                    text: \"部分符合\",\n                    score: 3\n                },\n                {\n                    id: \"opt_4\",\n                    text: \"基本符合\",\n                    score: 4\n                },\n                {\n                    id: \"opt_5\",\n                    text: \"完全符合\",\n                    score: 5\n                }\n            ];\n        }\n        return [];\n    }\n    // 验证问卷\n    async validateQuestionnaire(questionnaire) {\n        const errors = [];\n        const warnings = [];\n        const suggestions = [];\n        // 基本验证\n        if (!questionnaire.title) {\n            errors.push(\"问卷标题不能为空\");\n        }\n        if (questionnaire.questions.length === 0) {\n            errors.push(\"问卷必须包含至少一个问题\");\n        }\n        // 问题数量验证\n        const expectedQuestions = questionnaire.version === \"professional\" ? 40 : 20;\n        if (questionnaire.questions.length < expectedQuestions * 0.8) {\n            warnings.push(`问题数量偏少，建议至少${expectedQuestions}个问题`);\n        }\n        // 维度覆盖验证\n        const dimensions = new Set(questionnaire.questions.map((q)=>q.dimension));\n        if (dimensions.size < 4) {\n            errors.push(\"问卷应覆盖OCTI四个维度\");\n        }\n        // 问题类型分布验证\n        const typeDistribution = this.analyzeQuestionTypes(questionnaire.questions);\n        if (typeDistribution.likert_scale < 0.5) {\n            suggestions.push(\"建议增加更多量表题以提高测量精度\");\n        }\n        // 问题质量验证\n        for (const question of questionnaire.questions){\n            if (!question.text || question.text.length < 10) {\n                errors.push(`问题${question.id}内容过短`);\n            }\n            if (question.type === \"single_choice\" && (!question.options || question.options.length < 3)) {\n                errors.push(`问题${question.id}选项不足`);\n            }\n        }\n        // 计算质量分数\n        let qualityScore = 1.0;\n        qualityScore -= errors.length * 0.2;\n        qualityScore -= warnings.length * 0.1;\n        qualityScore = Math.max(0, qualityScore);\n        return {\n            isValid: errors.length === 0,\n            errors,\n            warnings,\n            suggestions,\n            score: qualityScore,\n            qualityScore\n        };\n    }\n    // 重新生成问卷\n    async regenerateQuestionnaire(options, suggestions) {\n        logger.info(\"Regenerating questionnaire with improvements\", {\n            suggestions\n        });\n        // 添加改进建议到自定义要求\n        const improvedOptions = {\n            ...options,\n            customRequirements: [\n                options.customRequirements || \"\",\n                \"请特别注意以下改进建议：\",\n                ...suggestions\n            ].filter(Boolean).join(\"\\n\")\n        };\n        // 递归调用，但限制重试次数\n        return this.design(improvedOptions);\n    }\n    // 分析问题类型分布\n    analyzeQuestionTypes(questions) {\n        const distribution = {\n            single_choice: 0,\n            multiple_choice: 0,\n            scale: 0,\n            open_ended: 0,\n            ranking: 0,\n            matrix: 0,\n            likert_scale: 0,\n            choice: 0,\n            scenario: 0\n        };\n        questions.forEach((q)=>{\n            distribution[q.type] = (distribution[q.type] || 0) + 1;\n        });\n        // 转换为比例\n        const total = questions.length;\n        Object.keys(distribution).forEach((key)=>{\n            distribution[key] = distribution[key] / total;\n        });\n        return distribution;\n    }\n    // 计算预估时间\n    calculateEstimatedTime(questions) {\n        const totalSeconds = questions.reduce((total, q)=>{\n            const baseTime = q.type === \"open_ended\" ? 60 : 30;\n            return total + (q.expectedResponseTime || baseTime);\n        }, 0);\n        const minutes = Math.ceil(totalSeconds / 60);\n        return `${minutes}分钟`;\n    }\n    // 计算难度\n    calculateDifficulty(questions) {\n        if (questions.length === 0) return 0.5;\n        const avgDifficulty = questions.reduce((sum, q)=>{\n            return sum + (q.difficulty || 0.5);\n        }, 0) / questions.length;\n        return avgDifficulty;\n    }\n    // 生成问卷ID\n    generateQuestionnaireId() {\n        return `questionnaire_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    // 生成缓存键\n    generateCacheKey(options) {\n        const keyParts = [\n            options.version,\n            options.organizationType || \"default\",\n            options.industryContext || \"default\",\n            options.targetAudience || \"default\",\n            options.customRequirements || \"default\"\n        ];\n        return keyParts.join(\"_\").replace(/[^a-zA-Z0-9_]/g, \"_\");\n    }\n    // 获取问卷预览\n    async getQuestionnairePreview(questionnaireId) {\n        // 从缓存中查找\n        for (const questionnaire of Array.from(this.questionCache.values())){\n            if (questionnaire.id === questionnaireId) {\n                return {\n                    title: questionnaire.title,\n                    description: questionnaire.description,\n                    questionCount: questionnaire.questions.length,\n                    estimatedTime: parseInt(questionnaire.estimatedTime) || 20,\n                    dimensions: Array.from(new Set(questionnaire.questions.map((q)=>q.dimension)))\n                };\n            }\n        }\n        return null;\n    }\n    // 清除缓存\n    clearCache() {\n        this.questionCache.clear();\n        logger.info(\"QuestionDesignerAgent cache cleared\");\n    }\n    // 获取统计信息\n    getStats() {\n        const questionnaires = Array.from(this.questionCache.values());\n        const versionDistribution = {};\n        questionnaires.forEach((q)=>{\n            versionDistribution[q.version] = (versionDistribution[q.version] || 0) + 1;\n        });\n        return {\n            cacheSize: this.questionCache.size,\n            totalQuestionnaires: questionnaires.length,\n            versionDistribution\n        };\n    }\n    /**\n   * 分批生成问卷 - 核心实现\n   */ async generateQuestionnaireBatched(options) {\n        const dimensions = [\n            \"S/F\",\n            \"I/T\",\n            \"M/V\",\n            \"A/D\"\n        ];\n        const questionsPerBatch = 5 // 每批生成5道题\n        ;\n        const questionsPerDimension = 15;\n        // 创建基础问卷结构\n        const questionnaire = {\n            id: `questionnaire_${Date.now()}`,\n            title: `OCTI智能评估 - ${options.organizationType}`,\n            description: `基于OCTI四维八极理论的${options.organizationType}组织能力评估`,\n            version: options.version,\n            organizationType: options.organizationType,\n            assessmentType: options.version === \"professional\" ? \"professional\" : \"basic\",\n            dimensions: dimensions,\n            questions: [],\n            totalQuestions: 60,\n            estimatedTime: \"20分钟\",\n            createdAt: new Date().toISOString()\n        };\n        // 为每个维度分批生成题目\n        for (const dimension of dimensions){\n            logger.info(`开始生成维度 ${dimension} 的题目`);\n            for(let batch = 0; batch < Math.ceil(questionsPerDimension / questionsPerBatch); batch++){\n                const startIndex = batch * questionsPerBatch;\n                const endIndex = Math.min(startIndex + questionsPerBatch, questionsPerDimension);\n                const questionsInBatch = endIndex - startIndex;\n                try {\n                    const batchQuestions = await this.generateQuestionBatch(dimension, questionsInBatch, startIndex + 1, options);\n                    questionnaire.questions.push(...batchQuestions);\n                    logger.info(`维度 ${dimension} 第 ${batch + 1} 批题目生成完成`, {\n                        batchSize: batchQuestions.length,\n                        totalGenerated: questionnaire.questions.length\n                    });\n                    // 缓存当前进度\n                    this.questionCache.set(questionnaire.id, questionnaire);\n                } catch (error) {\n                    logger.warn(`维度 ${dimension} 第 ${batch + 1} 批生成失败，使用默认题目`, {\n                        error\n                    });\n                    // 生成默认题目作为后备\n                    const defaultQuestions = this.generateDefaultQuestionsBatch(dimension, questionsInBatch, startIndex + 1);\n                    questionnaire.questions.push(...defaultQuestions);\n                }\n            }\n        }\n        // 更新最终统计\n        questionnaire.totalQuestions = questionnaire.questions.length;\n        questionnaire.estimatedTime = this.calculateEstimatedTime(questionnaire.questions);\n        return questionnaire;\n    }\n    /**\n   * 生成单批次题目\n   */ async generateQuestionBatch(dimension, questionCount, startIndex, options) {\n        const prompt = this.buildBatchPrompt(dimension, questionCount, startIndex, options);\n        const llmRequest = {\n            model: \"deepseek-chat\",\n            messages: [\n                {\n                    role: \"system\",\n                    content: prompt.systemPrompt\n                },\n                {\n                    role: \"user\",\n                    content: prompt.userPrompt\n                }\n            ],\n            temperature: 0.7,\n            maxTokens: 3000 // 较小的token限制，因为只生成几道题\n        };\n        const provider = \"deepseek\";\n        const response = await this.llmClient.chat(provider, llmRequest);\n        return this.parseBatchResponse(response.content || \"\", dimension, startIndex);\n    }\n    /**\n   * 构建批次提示词\n   */ buildBatchPrompt(dimension, questionCount, startIndex, options) {\n        const dimensionDescriptions = {\n            \"S/F\": \"结构化与灵活性 - 评估组织在规范化管理与灵活应变之间的平衡\",\n            \"I/T\": \"创新性与传统性 - 评估组织在创新突破与传统稳定之间的取向\",\n            \"M/V\": \"管理导向与愿景导向 - 评估组织在日常管理与长远愿景之间的重点\",\n            \"A/D\": \"行动导向与决策导向 - 评估组织在快速行动与深度决策之间的偏好\"\n        };\n        const systemPrompt = `你是OCTI问卷设计专家，专门为${dimension}维度生成高质量的评估题目。\n\n${dimension}维度说明：${dimensionDescriptions[dimension]}\n\n请生成${questionCount}道针对${options.organizationType}类型组织的专业题目。`;\n        const userPrompt = `请为${dimension}维度生成${questionCount}道题目（编号从${startIndex}开始）。\n\n要求：\n- 每道题5个选项，分值1-5分\n- 题目要具体、实用、针对${options.organizationType}\n- ${options.version === \"professional\" ? \"使用深度情境化问题\" : \"使用通用性问题\"}\n\n请严格按照以下JSON格式返回：\n\n\\`\\`\\`json\n{\n  \"questions\": [\n    {\n      \"id\": \"q_${startIndex}\",\n      \"dimension\": \"${dimension}\",\n      \"type\": \"single_choice\",\n      \"text\": \"具体的问题文本\",\n      \"options\": [\n        {\"id\": \"o_${startIndex}_1\", \"text\": \"完全不符合\", \"score\": 1},\n        {\"id\": \"o_${startIndex}_2\", \"text\": \"基本不符合\", \"score\": 2},\n        {\"id\": \"o_${startIndex}_3\", \"text\": \"部分符合\", \"score\": 3},\n        {\"id\": \"o_${startIndex}_4\", \"text\": \"基本符合\", \"score\": 4},\n        {\"id\": \"o_${startIndex}_5\", \"text\": \"完全符合\", \"score\": 5}\n      ],\n      \"required\": true,\n      \"order\": ${startIndex}\n    }\n  ]\n}\n\\`\\`\\`\n\n确保返回完整的JSON格式。`;\n        return {\n            systemPrompt,\n            userPrompt\n        };\n    }\n    /**\n   * 解析批次响应\n   */ parseBatchResponse(content, dimension, startIndex) {\n        try {\n            console.log(\"批次响应内容长度:\", content.length);\n            console.log(\"批次响应前500字符:\", content.substring(0, 500));\n            // 提取JSON\n            let jsonStr = \"\";\n            const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonStr = jsonMatch[1].trim();\n            } else {\n                const objectMatch = content.match(/\\{[\\s\\S]*/);\n                if (objectMatch) {\n                    jsonStr = objectMatch[0].trim();\n                }\n            }\n            if (!jsonStr) {\n                throw new Error(\"未找到JSON格式\");\n            }\n            console.log(\"提取的JSON字符串长度:\", jsonStr.length);\n            // 尝试修复不完整的JSON\n            let fixedJson = this.fixBatchJson(jsonStr);\n            console.log(\"修复后的JSON:\", fixedJson.substring(0, 200) + \"...\");\n            const parsed = JSON.parse(fixedJson);\n            if (parsed.questions && Array.isArray(parsed.questions)) {\n                const questions = parsed.questions.map((q, index)=>({\n                        id: q.id || `q_${startIndex + index}`,\n                        dimension: q.dimension || dimension,\n                        type: q.type || \"single_choice\",\n                        text: q.text || q.title || `${dimension}维度问题 ${startIndex + index}`,\n                        options: q.options || this.generateDefaultOptions(\"single_choice\"),\n                        required: q.required !== false,\n                        order: q.order || startIndex + index\n                    }));\n                console.log(`成功解析 ${questions.length} 道题目`);\n                return questions;\n            }\n            throw new Error(\"解析的JSON中没有questions数组\");\n        } catch (error) {\n            console.error(\"批次解析失败:\", error);\n            logger.warn(`批次解析失败，使用默认题目`, {\n                error,\n                dimension,\n                startIndex\n            });\n            return this.generateDefaultQuestionsBatch(dimension, 1, startIndex);\n        }\n    }\n    /**\n   * 修复不完整的批次JSON\n   */ fixBatchJson(jsonStr) {\n        try {\n            // 如果JSON已经完整，直接返回\n            JSON.parse(jsonStr);\n            return jsonStr;\n        } catch (error) {\n            console.log(\"JSON不完整，尝试修复...\");\n            let fixed = jsonStr;\n            // 确保有questions数组开始\n            if (!fixed.includes('\"questions\"')) {\n                throw new Error(\"JSON中没有questions字段\");\n            }\n            // 修复可能缺少的结尾\n            if (!fixed.endsWith(\"}\")) {\n                // 找到最后一个完整的问题对象\n                const lastCompleteQuestion = fixed.lastIndexOf('\"order\"');\n                if (lastCompleteQuestion !== -1) {\n                    // 找到这个order字段的值结尾\n                    const afterOrder = fixed.substring(lastCompleteQuestion);\n                    const numberMatch = afterOrder.match(/\"order\":\\s*(\\d+)/);\n                    if (numberMatch) {\n                        const endPos = lastCompleteQuestion + afterOrder.indexOf(numberMatch[1]) + numberMatch[1].length;\n                        fixed = fixed.substring(0, endPos) + \"\\n     }\\n   ]\\n}\";\n                    }\n                }\n            }\n            // 验证修复结果\n            JSON.parse(fixed);\n            console.log(\"JSON修复成功\");\n            return fixed;\n        }\n    }\n    /**\n   * 生成默认题目批次\n   */ generateDefaultQuestionsBatch(dimension, questionCount, startIndex) {\n        const questions = [];\n        for(let i = 0; i < questionCount; i++){\n            const questionIndex = startIndex + i;\n            questions.push({\n                id: `q_${questionIndex}`,\n                dimension,\n                type: \"single_choice\",\n                text: `关于${dimension}维度的评估问题 ${questionIndex}`,\n                options: this.generateDefaultOptions(\"single_choice\"),\n                required: true,\n                order: questionIndex\n            });\n        }\n        return questions;\n    }\n    /**\n   * 生成默认问卷（当LLM解析失败时的备用方案）\n   */ generateDefaultQuestions(options) {\n        const questions = [];\n        const dimensionQuestions = {\n            \"S/F\": [\n                \"您的组织更倾向于遵循既定的流程和规范\",\n                \"在面对新情况时，您的组织能够快速调整策略\",\n                \"您的组织有明确的层级结构和职责分工\",\n                \"您的组织鼓励员工在工作中发挥创造性\",\n                \"您的组织重视标准化的工作流程\"\n            ],\n            \"I/T\": [\n                \"您的组织积极采用新技术和新方法\",\n                \"您的组织重视传统经验和成熟做法\",\n                \"您的组织鼓励员工提出创新想法\",\n                \"您的组织在决策时会充分考虑历史经验\",\n                \"您的组织愿意承担创新带来的风险\"\n            ],\n            \"M/V\": [\n                \"您的组织注重日常运营管理的效率\",\n                \"您的组织有清晰的长远发展愿景\",\n                \"您的组织重视绩效指标的达成\",\n                \"您的组织经常讨论未来发展方向\",\n                \"您的组织善于制定详细的执行计划\"\n            ],\n            \"A/D\": [\n                \"您的组织在决策时会进行充分的分析和讨论\",\n                \"您的组织能够快速响应市场变化\",\n                \"您的组织重视数据分析在决策中的作用\",\n                \"您的组织鼓励员工主动采取行动\",\n                \"您的组织在重要决策前会征求多方意见\"\n            ]\n        };\n        let questionId = 1;\n        options.dimensions.forEach((dimension)=>{\n            const dimensionTexts = dimensionQuestions[dimension] || [];\n            dimensionTexts.forEach((text)=>{\n                questions.push({\n                    id: `q_${questionId}`,\n                    dimension,\n                    type: \"single_choice\",\n                    text: text,\n                    options: [\n                        {\n                            id: `o_${questionId}_1`,\n                            text: \"完全不同意\",\n                            score: 1\n                        },\n                        {\n                            id: `o_${questionId}_2`,\n                            text: \"不同意\",\n                            score: 2\n                        },\n                        {\n                            id: `o_${questionId}_3`,\n                            text: \"中立\",\n                            score: 3\n                        },\n                        {\n                            id: `o_${questionId}_4`,\n                            text: \"同意\",\n                            score: 4\n                        },\n                        {\n                            id: `o_${questionId}_5`,\n                            text: \"完全同意\",\n                            score: 5\n                        }\n                    ],\n                    required: true,\n                    order: questionId\n                });\n                questionId++;\n            });\n        });\n        // 补充到60题\n        while(questions.length < 60){\n            const dimension = options.dimensions[questions.length % 4];\n            questions.push({\n                id: `q_${questions.length + 1}`,\n                dimension,\n                type: \"single_choice\",\n                text: `关于${dimension}维度的评估问题 ${questions.length + 1}`,\n                options: [\n                    {\n                        id: `o_${questions.length + 1}_1`,\n                        text: \"完全不同意\",\n                        score: 1\n                    },\n                    {\n                        id: `o_${questions.length + 1}_2`,\n                        text: \"不同意\",\n                        score: 2\n                    },\n                    {\n                        id: `o_${questions.length + 1}_3`,\n                        text: \"中立\",\n                        score: 3\n                    },\n                    {\n                        id: `o_${questions.length + 1}_4`,\n                        text: \"同意\",\n                        score: 4\n                    },\n                    {\n                        id: `o_${questions.length + 1}_5`,\n                        text: \"完全同意\",\n                        score: 5\n                    }\n                ],\n                required: true,\n                order: questions.length + 1\n            });\n        }\n        return questions;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/agents/QuestionDesignerAgent.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/api/ApiService.ts":
/*!****************************************!*\
  !*** ./src/services/api/ApiService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiService: () => (/* binding */ ApiService)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var _config_ConfigService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../config/ConfigService */ \"(rsc)/./src/services/config/ConfigService.ts\");\n/* harmony import */ var _agents_AgentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../agents/AgentService */ \"(rsc)/./src/services/agents/AgentService.ts\");\n\n\n\n/**\n * API服务\n * 提供统一的API接口管理\n */ class ApiService {\n    constructor(){\n        this.isInitialized = false;\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"ApiService\");\n        this.configService = null // 在initialize中异步初始化\n        ;\n        this.agentManager = new _agents_AgentService__WEBPACK_IMPORTED_MODULE_2__.AgentManager() // AgentManager没有getInstance方法，直接创建实例\n        ;\n    }\n    static getInstance() {\n        if (!ApiService.instance) {\n            ApiService.instance = new ApiService();\n        }\n        return ApiService.instance;\n    }\n    /**\n   * 初始化API服务\n   */ async initialize() {\n        if (this.isInitialized) {\n            this.logger.warn(\"API服务已经初始化\");\n            return;\n        }\n        try {\n            this.logger.info(\"开始初始化API服务\");\n            // 初始化配置服务\n            this.configService = await _config_ConfigService__WEBPACK_IMPORTED_MODULE_1__.ConfigService.getInstance();\n            // 确保智能体管理器已初始化\n            await this.agentManager.initialize();\n            this.isInitialized = true;\n            this.logger.info(\"API服务初始化完成\");\n        } catch (error) {\n            this.logger.error(\"API服务初始化失败\", {\n                error\n            });\n            throw error;\n        }\n    }\n    /**\n   * 处理配置相关API请求\n   */ async handleConfigRequest(request) {\n        try {\n            const { action, key, value, batch } = request;\n            switch(action){\n                case \"get\":\n                    if (key) {\n                        const result = await this.configService.get(key);\n                        return {\n                            success: true,\n                            data: result\n                        };\n                    } else {\n                        // 获取所有配置（模拟实现）\n                        const allConfigs = {};\n                        return {\n                            success: true,\n                            data: allConfigs\n                        };\n                    }\n                case \"set\":\n                    if (!key || value === undefined) {\n                        return {\n                            success: false,\n                            error: \"缺少必要参数: key 或 value\"\n                        };\n                    }\n                    await this.configService.set(key, value);\n                    return {\n                        success: true,\n                        message: \"配置设置成功\"\n                    };\n                case \"batch_get\":\n                    if (!batch || !Array.isArray(batch)) {\n                        return {\n                            success: false,\n                            error: \"缺少必要参数: batch\"\n                        };\n                    }\n                    // 批量获取配置（模拟实现）\n                    const results = {};\n                    for (const key of batch){\n                        results[key] = await this.configService.get(key);\n                    }\n                    return {\n                        success: true,\n                        data: results\n                    };\n                case \"batch_set\":\n                    if (!batch || typeof batch !== \"object\") {\n                        return {\n                            success: false,\n                            error: \"缺少必要参数: batch\"\n                        };\n                    }\n                    // 批量设置配置（模拟实现）\n                    for (const [key, value] of Object.entries(batch)){\n                        await this.configService.set(key, value);\n                    }\n                    return {\n                        success: true,\n                        message: \"批量配置设置成功\"\n                    };\n                case \"reload\":\n                    await this.configService.reload();\n                    return {\n                        success: true,\n                        message: \"配置重新加载成功\"\n                    };\n                default:\n                    return {\n                        success: false,\n                        error: `不支持的操作: ${action}`\n                    };\n            }\n        } catch (error) {\n            this.logger.error(\"处理配置API请求失败\", {\n                error,\n                request\n            });\n            return {\n                success: false,\n                error: `配置操作失败: ${error}`\n            };\n        }\n    }\n    /**\n   * 处理智能体相关API请求\n   */ async handleAgentRequest(request) {\n        try {\n            const { action, agentName, input } = request;\n            switch(action){\n                case \"execute\":\n                    if (!agentName || !input) {\n                        return {\n                            success: false,\n                            error: \"缺少必要参数: agentName 或 input\"\n                        };\n                    }\n                    const result = await this.agentManager.executeAgent(agentName, input);\n                    return {\n                        success: true,\n                        data: result\n                    };\n                case \"status\":\n                    if (agentName) {\n                        const agent = this.agentManager.getAgent(agentName);\n                        if (!agent) {\n                            return {\n                                success: false,\n                                error: `智能体不存在: ${agentName}`\n                            };\n                        }\n                        const status = agent.getStatus();\n                        return {\n                            success: true,\n                            data: status\n                        };\n                    } else {\n                        const allStatus = this.agentManager.getAllStatus();\n                        return {\n                            success: true,\n                            data: allStatus\n                        };\n                    }\n                case \"list\":\n                    const availableAgents = this.agentManager.getAvailableAgents();\n                    return {\n                        success: true,\n                        data: availableAgents\n                    };\n                default:\n                    return {\n                        success: false,\n                        error: `不支持的操作: ${action}`\n                    };\n            }\n        } catch (error) {\n            this.logger.error(\"处理智能体API请求失败\", {\n                error,\n                request\n            });\n            return {\n                success: false,\n                error: `智能体操作失败: ${error}`\n            };\n        }\n    }\n    /**\n   * 处理评估相关API请求\n   */ async handleAssessmentRequest(request) {\n        try {\n            const { action, assessmentId, data } = request;\n            switch(action){\n                case \"create\":\n                    if (!data) {\n                        return {\n                            success: false,\n                            error: \"缺少必要参数: data\"\n                        };\n                    }\n                    const assessment = await this.createAssessment(data);\n                    return {\n                        success: true,\n                        data: assessment\n                    };\n                case \"get\":\n                    if (!assessmentId) {\n                        return {\n                            success: false,\n                            error: \"缺少必要参数: assessmentId\"\n                        };\n                    }\n                    const result = await this.getAssessment(assessmentId);\n                    return {\n                        success: true,\n                        data: result\n                    };\n                case \"submit\":\n                    if (!assessmentId || !data) {\n                        return {\n                            success: false,\n                            error: \"缺少必要参数: assessmentId 或 data\"\n                        };\n                    }\n                    const submission = await this.submitAssessment(assessmentId, data);\n                    return {\n                        success: true,\n                        data: submission\n                    };\n                case \"analyze\":\n                    if (!assessmentId) {\n                        return {\n                            success: false,\n                            error: \"缺少必要参数: assessmentId\"\n                        };\n                    }\n                    const analysis = await this.analyzeAssessment(assessmentId);\n                    return {\n                        success: true,\n                        data: analysis\n                    };\n                default:\n                    return {\n                        success: false,\n                        error: `不支持的操作: ${action}`\n                    };\n            }\n        } catch (error) {\n            this.logger.error(\"处理评估API请求失败\", {\n                error,\n                request\n            });\n            return {\n                success: false,\n                error: `评估操作失败: ${error}`\n            };\n        }\n    }\n    /**\n   * 处理系统状态API请求\n   */ async handleSystemRequest(request) {\n        try {\n            const { action } = request;\n            switch(action){\n                case \"health\":\n                    const health = await this.getSystemHealth();\n                    return {\n                        success: true,\n                        data: health\n                    };\n                case \"status\":\n                    const status = await this.getSystemStatus();\n                    return {\n                        success: true,\n                        data: status\n                    };\n                case \"metrics\":\n                    const metrics = await this.getSystemMetrics();\n                    return {\n                        success: true,\n                        data: metrics\n                    };\n                default:\n                    return {\n                        success: false,\n                        error: `不支持的操作: ${action}`\n                    };\n            }\n        } catch (error) {\n            this.logger.error(\"处理系统API请求失败\", {\n                error,\n                request\n            });\n            return {\n                success: false,\n                error: `系统操作失败: ${error}`\n            };\n        }\n    }\n    /**\n   * 创建评估（模拟实现）\n   */ async createAssessment(data) {\n        // 使用问卷设计师智能体生成问卷\n        const questionnaireResult = await this.agentManager.executeAgent(\"questionnaire_designer\", {\n            assessmentType: data.type || \"综合能力评估\",\n            dimensions: data.dimensions || [\n                \"团队协作\",\n                \"沟通能力\",\n                \"领导力\",\n                \"创新思维\"\n            ],\n            requirements: data.requirements\n        });\n        if (!questionnaireResult.success) {\n            throw new Error(\"问卷生成失败\");\n        }\n        // 模拟创建评估记录\n        const assessment = {\n            id: `assessment_${Date.now()}`,\n            title: data.title || \"智能评估\",\n            description: data.description || \"AI生成的智能评估问卷\",\n            questionnaire: questionnaireResult.data,\n            status: \"active\",\n            createdAt: new Date().toISOString(),\n            metadata: {\n                type: data.type,\n                dimensions: data.dimensions,\n                estimatedTime: questionnaireResult.data?.metadata?.estimatedTime || 15\n            }\n        };\n        this.logger.info(\"创建评估成功\", {\n            assessmentId: assessment.id\n        });\n        return assessment;\n    }\n    /**\n   * 获取评估（模拟实现）\n   */ async getAssessment(assessmentId) {\n        // 模拟从数据库获取评估\n        this.logger.info(\"获取评估\", {\n            assessmentId\n        });\n        return {\n            id: assessmentId,\n            title: \"智能评估\",\n            description: \"AI生成的智能评估问卷\",\n            status: \"active\",\n            questionnaire: {\n                title: \"OCTI智能评估问卷\",\n                sections: [\n                    {\n                        id: \"section_1\",\n                        title: \"基础信息\",\n                        questions: [\n                            {\n                                id: \"q1\",\n                                type: \"single_choice\",\n                                title: \"您的年龄段是？\",\n                                options: [\n                                    \"18-25岁\",\n                                    \"26-35岁\",\n                                    \"36-45岁\",\n                                    \"46岁以上\"\n                                ],\n                                required: true\n                            }\n                        ]\n                    }\n                ]\n            },\n            createdAt: new Date().toISOString()\n        };\n    }\n    /**\n   * 提交评估（模拟实现）\n   */ async submitAssessment(assessmentId, data) {\n        this.logger.info(\"提交评估\", {\n            assessmentId,\n            data\n        });\n        // 模拟保存评估结果\n        const submission = {\n            id: `submission_${Date.now()}`,\n            assessmentId,\n            answers: data.answers,\n            submittedAt: new Date().toISOString(),\n            status: \"completed\"\n        };\n        return submission;\n    }\n    /**\n   * 分析评估（模拟实现）\n   */ async analyzeAssessment(assessmentId) {\n        this.logger.info(\"分析评估\", {\n            assessmentId\n        });\n        // 使用评估导师智能体分析结果\n        const analysisResult = await this.agentManager.executeAgent(\"assessment_mentor\", {\n            assessmentResults: {\n                assessmentId,\n                answers: {},\n                submittedAt: new Date().toISOString()\n            },\n            userProfile: {\n                age: \"26-35岁\",\n                experience: \"3-5年\"\n            }\n        });\n        if (!analysisResult.success) {\n            throw new Error(\"评估分析失败\");\n        }\n        return {\n            assessmentId,\n            analysis: analysisResult.data,\n            analyzedAt: new Date().toISOString()\n        };\n    }\n    /**\n   * 获取系统健康状态\n   */ async getSystemHealth() {\n        return {\n            status: \"healthy\",\n            timestamp: new Date().toISOString(),\n            services: {\n                config: this.configService ? \"healthy\" : \"unhealthy\",\n                agents: this.agentManager ? \"healthy\" : \"unhealthy\",\n                api: this.isInitialized ? \"healthy\" : \"unhealthy\"\n            }\n        };\n    }\n    /**\n   * 获取系统状态\n   */ async getSystemStatus() {\n        return {\n            initialized: this.isInitialized,\n            uptime: process.uptime(),\n            memory: process.memoryUsage(),\n            version: \"1.0.0\",\n            environment: \"development\" || 0,\n            agents: this.agentManager.getAllStatus()\n        };\n    }\n    /**\n   * 获取系统指标\n   */ async getSystemMetrics() {\n        return {\n            requests: {\n                total: Math.floor(Math.random() * 1000),\n                success: Math.floor(Math.random() * 900),\n                error: Math.floor(Math.random() * 100)\n            },\n            performance: {\n                avgResponseTime: Math.floor(Math.random() * 500) + 100,\n                p95ResponseTime: Math.floor(Math.random() * 1000) + 200,\n                p99ResponseTime: Math.floor(Math.random() * 2000) + 500\n            },\n            resources: {\n                cpuUsage: Math.random() * 100,\n                memoryUsage: Math.random() * 100,\n                diskUsage: Math.random() * 100\n            }\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/api/ApiService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/config/ConfigService.ts":
/*!**********************************************!*\
  !*** ./src/services/config/ConfigService.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigService: () => (/* binding */ ConfigService)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var _lib_cache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cache */ \"(rsc)/./src/lib/cache.ts\");\n\n\n/**\n * 配置服务 - OCTI系统的核心配置管理\n * 支持配置的加载、验证、缓存和热更新\n */ class ConfigService {\n    static{\n        this.instance = null;\n    }\n    static{\n        this.isInitializing = false;\n    }\n    static async getInstance() {\n        if (ConfigService.instance) {\n            return ConfigService.instance;\n        }\n        if (ConfigService.isInitializing) {\n            // 等待初始化完成\n            while(ConfigService.isInitializing){\n                await new Promise((resolve)=>setTimeout(resolve, 10));\n            }\n            return ConfigService.instance;\n        }\n        ConfigService.isInitializing = true;\n        try {\n            ConfigService.instance = new ConfigService();\n            await ConfigService.instance.initialize();\n            return ConfigService.instance;\n        } finally{\n            ConfigService.isInitializing = false;\n        }\n    }\n    constructor(){\n        this.configSchemas = new Map();\n        this.configValues = new Map();\n        this.watchers = new Map();\n        this.cache = new _lib_cache__WEBPACK_IMPORTED_MODULE_1__.RedisCache({\n            ttl: parseInt(process.env.CONFIG_CACHE_TTL || \"3600\"),\n            keyPrefix: \"config:\",\n            strategy: \"ttl\"\n        });\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"ConfigService\");\n    }\n    /**\n   * 初始化配置服务\n   */ async initialize() {\n        try {\n            this.logger.info(\"初始化配置服务...\");\n            // 加载配置模式\n            await this.loadConfigSchemas();\n            // 加载配置值\n            await this.loadConfigValues();\n            // 验证配置\n            await this.validateConfigs();\n            this.logger.info(\"配置服务初始化完成\");\n        } catch (error) {\n            this.logger.error(\"配置服务初始化失败\", {\n                error\n            });\n            throw error;\n        }\n    }\n    /**\n   * 获取配置值\n   */ async get(key, defaultValue) {\n        try {\n            // 添加输入验证\n            if (!key || typeof key !== \"string\") {\n                throw new Error(\"配置键必须是非空字符串\");\n            }\n            const cached = await this.cache.get(key);\n            if (cached !== null) {\n                return cached;\n            }\n            const value = this.configValues.get(key);\n            if (value !== undefined) {\n                await this.cache.set(key, value);\n                return value;\n            }\n            if (defaultValue !== undefined) {\n                return defaultValue;\n            }\n            throw new Error(`配置项 ${key} 不存在且未提供默认值`);\n        } catch (error) {\n            this.logger.error(`获取配置失败: ${key}`, {\n                error: error instanceof Error ? error.message : String(error),\n                stack: error instanceof Error ? error.stack : undefined\n            });\n            throw error;\n        }\n    }\n    /**\n   * 设置配置值\n   */ async set(key, value) {\n        try {\n            // 验证配置值\n            await this.validateConfigValue(key, value);\n            // 更新内存\n            this.configValues.set(key, value);\n            // 更新缓存\n            await this.cache.set(key, value);\n            // 通知观察者\n            await this.notifyWatchers(key, value);\n            this.logger.info(`配置已更新: ${key}`, {\n                value\n            });\n        } catch (error) {\n            this.logger.error(`设置配置失败: ${key}`, {\n                error,\n                value\n            });\n            throw error;\n        }\n    }\n    /**\n   * 批量获取配置\n   */ async getMultiple(keys) {\n        const result = {};\n        const uncachedKeys = [];\n        // 批量从缓存获取\n        const cachePromises = keys.map(async (key)=>{\n            const cached = await this.cache.get(key);\n            if (cached !== null) {\n                result[key] = cached;\n            } else {\n                uncachedKeys.push(key);\n            }\n        });\n        await Promise.all(cachePromises);\n        // 批量获取未缓存的配置\n        for (const key of uncachedKeys){\n            try {\n                const value = this.configValues.get(key);\n                if (value !== undefined) {\n                    result[key] = value;\n                    // 异步缓存，不阻塞返回\n                    this.cache.set(key, value).catch((err)=>this.logger.warn(`缓存配置失败: ${key}`, {\n                            error: err\n                        }));\n                } else {\n                    result[key] = null;\n                }\n            } catch (error) {\n                this.logger.warn(`获取配置失败: ${key}`, {\n                    error\n                });\n                result[key] = null;\n            }\n        }\n        return result;\n    }\n    /**\n   * 获取配置模式\n   */ getSchema(schemaId) {\n        return this.configSchemas.get(schemaId);\n    }\n    /**\n   * 注册配置模式\n   */ async registerSchema(schema) {\n        try {\n            // 验证模式\n            this.validateSchema(schema);\n            // 存储模式\n            this.configSchemas.set(schema.id, schema);\n            this.logger.info(`配置模式已注册: ${schema.id}`, {\n                schema: schema.name\n            });\n        } catch (error) {\n            this.logger.error(`注册配置模式失败: ${schema.id}`, {\n                error\n            });\n            throw error;\n        }\n    }\n    /**\n   * 监听配置变化\n   */ watch(key, callback) {\n        if (!this.watchers.has(key)) {\n            this.watchers.set(key, []);\n        }\n        const watchers = this.watchers.get(key);\n        watchers.push(callback);\n        // 返回取消监听的函数\n        return ()=>{\n            const index = watchers.indexOf(callback);\n            if (index > -1) {\n                watchers.splice(index, 1);\n            }\n        };\n    }\n    /**\n   * 重新加载配置\n   */ async reload() {\n        try {\n            this.logger.info(\"重新加载配置...\");\n            // 清除缓存\n            await this.cache.clear();\n            // 重新加载\n            await this.loadConfigValues();\n            await this.validateConfigs();\n            this.logger.info(\"配置重新加载完成\");\n        } catch (error) {\n            this.logger.error(\"重新加载配置失败\", {\n                error\n            });\n            throw error;\n        }\n    }\n    /**\n   * 获取所有配置键\n   */ getKeys() {\n        return Array.from(this.configValues.keys());\n    }\n    /**\n   * 检查配置是否存在\n   */ has(key) {\n        return this.configValues.has(key);\n    }\n    /**\n   * 删除配置\n   */ async delete(key) {\n        try {\n            this.configValues.delete(key);\n            await this.cache.delete(key);\n            this.logger.info(`配置已删除: ${key}`);\n        } catch (error) {\n            this.logger.error(`删除配置失败: ${key}`, {\n                error\n            });\n            throw error;\n        }\n    }\n    /**\n   * 加载配置模式\n   */ async loadConfigSchemas() {\n        // 这里应该从数据库或文件系统加载配置模式\n        // 暂时使用硬编码的默认模式\n        const defaultSchemas = [\n            {\n                id: \"octi.assessment\",\n                name: \"OCTI评估配置\",\n                version: \"1.0.0\",\n                description: \"OCTI四维八极评估相关配置\",\n                schema: {\n                    type: \"object\",\n                    properties: {\n                        dimensions: {\n                            type: \"array\",\n                            items: {\n                                type: \"object\",\n                                properties: {\n                                    id: {\n                                        type: \"string\"\n                                    },\n                                    name: {\n                                        type: \"string\"\n                                    },\n                                    weight: {\n                                        type: \"number\",\n                                        minimum: 0,\n                                        maximum: 1\n                                    }\n                                }\n                            }\n                        }\n                    }\n                },\n                isActive: true,\n                createdAt: new Date(),\n                updatedAt: new Date()\n            },\n            {\n                id: \"octi.agents\",\n                name: \"AI智能体配置\",\n                version: \"1.0.0\",\n                description: \"AI智能体相关配置\",\n                schema: {\n                    type: \"object\",\n                    properties: {\n                        questionnaire_designer: {\n                            type: \"object\",\n                            properties: {\n                                model: {\n                                    type: \"string\"\n                                },\n                                temperature: {\n                                    type: \"number\",\n                                    minimum: 0,\n                                    maximum: 2\n                                },\n                                maxTokens: {\n                                    type: \"number\",\n                                    minimum: 1\n                                }\n                            }\n                        }\n                    }\n                },\n                isActive: true,\n                createdAt: new Date(),\n                updatedAt: new Date()\n            }\n        ];\n        for (const schema of defaultSchemas){\n            this.configSchemas.set(schema.id, schema);\n        }\n    }\n    /**\n   * 加载配置值\n   */ async loadConfigValues() {\n        // 这里应该从数据库加载配置值\n        // 暂时使用环境变量和默认值\n        const defaultConfigs = {\n            \"octi.assessment.dimensions\": [\n                {\n                    id: \"org\",\n                    name: \"组织维度\",\n                    weight: 0.25\n                },\n                {\n                    id: \"culture\",\n                    name: \"文化维度\",\n                    weight: 0.25\n                },\n                {\n                    id: \"talent\",\n                    name: \"人才维度\",\n                    weight: 0.25\n                },\n                {\n                    id: \"innovation\",\n                    name: \"创新维度\",\n                    weight: 0.25\n                }\n            ],\n            \"octi.agents.questionnaire_designer\": {\n                model: process.env.MINIMAX_MODEL || \"abab6.5-chat\",\n                temperature: 0.7,\n                maxTokens: 2000\n            },\n            \"octi.agents.assessment_mentor\": {\n                model: process.env.DEEPSEEK_MODEL || \"deepseek-chat\",\n                temperature: 0.5,\n                maxTokens: 3000\n            },\n            \"octi.cache.ttl\": parseInt(process.env.CONFIG_CACHE_TTL || \"3600\"),\n            \"octi.database.url\": process.env.DATABASE_URL,\n            \"octi.redis.url\": process.env.REDIS_URL\n        };\n        for (const [key, value] of Object.entries(defaultConfigs)){\n            this.configValues.set(key, value);\n        }\n    }\n    /**\n   * 验证所有配置\n   */ async validateConfigs() {\n        const entries = Array.from(this.configValues.entries());\n        for (const [key, value] of entries){\n            await this.validateConfigValue(key, value);\n        }\n    }\n    /**\n   * 验证配置值\n   */ async validateConfigValue(key, value) {\n        // 根据配置键找到对应的模式\n        const schemaId = this.getSchemaIdFromKey(key);\n        if (!schemaId) {\n            return; // 没有模式则跳过验证\n        }\n        const schema = this.configSchemas.get(schemaId);\n        if (!schema) {\n            return;\n        }\n        // 使用 Zod 进行验证\n        try {\n        // 这里应该根据 schema.schema 创建 Zod 验证器\n        // 暂时跳过详细验证\n        } catch (error) {\n            throw new Error(`配置验证失败: ${key} - ${error}`);\n        }\n    }\n    /**\n   * 验证配置模式\n   */ validateSchema(schema) {\n        if (!schema.id || !schema.name || !schema.version) {\n            throw new Error(\"配置模式缺少必要字段\");\n        }\n        if (!schema.schema || typeof schema.schema !== \"object\") {\n            throw new Error(\"配置模式格式无效\");\n        }\n    }\n    /**\n   * 从配置键获取模式ID\n   */ getSchemaIdFromKey(key) {\n        // 简单的键到模式映射\n        if (key.startsWith(\"octi.assessment\")) {\n            return \"octi.assessment\";\n        }\n        if (key.startsWith(\"octi.agents\")) {\n            return \"octi.agents\";\n        }\n        return null;\n    }\n    /**\n   * 通知配置变化观察者\n   */ async notifyWatchers(key, value) {\n        const watchers = this.watchers.get(key);\n        if (!watchers || watchers.length === 0) {\n            return;\n        }\n        for (const callback of watchers){\n            try {\n                callback(value);\n            } catch (error) {\n                this.logger.error(`配置观察者回调失败: ${key}`, {\n                    error\n                });\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/config/ConfigService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/data/data-fusion-engine.ts":
/*!*************************************************!*\
  !*** ./src/services/data/data-fusion-engine.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataFusionEngine: () => (/* binding */ DataFusionEngine)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n// 数据源Schema - 扩展支持更多内容类型\nconst DataSourceSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    sourceId: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    content: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    contentType: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n        \"text\",\n        \"json\",\n        \"pdf\",\n        \"docx\",\n        \"url\",\n        \"custom\",\n        \"hr\",\n        \"financial\",\n        \"operational\",\n        \"market\"\n    ]),\n    metadata: zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        timestamp: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n        reliability: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).max(1).optional(),\n        source: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional()\n    }).optional()\n});\n// 融合配置Schema\nconst FusionConfigSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    strategy: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n        \"weighted\",\n        \"priority\",\n        \"consensus\"\n    ]).default(\"weighted\"),\n    weights: zod__WEBPACK_IMPORTED_MODULE_1__.record(zod__WEBPACK_IMPORTED_MODULE_1__.number()).default({}),\n    threshold: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).max(1).default(0.7),\n    maxSources: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().default(5),\n    enableCache: zod__WEBPACK_IMPORTED_MODULE_1__.boolean().optional(),\n    cacheTimeout: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional()\n});\n/**\n * 数据融合引擎类\n */ class DataFusionEngine {\n    constructor(config = {}){\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"DataFusionEngine\");\n        const defaultConfig = {\n            strategy: \"weighted\",\n            threshold: 0.7,\n            maxSources: 5,\n            weights: {},\n            enableCache: true,\n            cacheTimeout: 3600\n        };\n        this.config = {\n            ...defaultConfig,\n            ...config\n        };\n    }\n    /**\n   * 获取默认配置\n   */ getDefaultConfig() {\n        return {\n            strategy: \"weighted\",\n            threshold: 0.7,\n            maxSources: 5,\n            weights: {},\n            enableCache: true,\n            cacheTimeout: 3600\n        };\n    }\n    /**\n   * 融合多个数据源\n   */ async fuseData(sources, config = {}) {\n        try {\n            // 验证输入\n            const validatedSources = sources.map((source)=>DataSourceSchema.parse(source));\n            const validatedConfig = FusionConfigSchema.parse(config);\n            this.logger.info(`开始数据融合，共 ${validatedSources.length} 个数据源`);\n            // 预处理数据源\n            const processedSources = await this.preprocessSources(validatedSources);\n            // 根据策略融合数据\n            const fusionResult = await this.applyFusionStrategy(processedSources, validatedConfig);\n            // 计算置信度\n            const confidence = this.calculateConfidence(processedSources, fusionResult);\n            this.logger.info(\"数据融合完成\", {\n                sourcesCount: validatedSources.length,\n                confidence,\n                strategy: validatedConfig.strategy\n            });\n            return {\n                fusedContent: fusionResult.content,\n                confidence,\n                sourcesUsed: fusionResult.sourcesUsed,\n                metadata: {\n                    strategy: validatedConfig.strategy,\n                    processedAt: new Date().toISOString(),\n                    originalSourcesCount: validatedSources.length,\n                    qualityScore: this.assessDataQuality(processedSources)\n                }\n            };\n        } catch (error) {\n            this.logger.error(\"数据融合失败\", {\n                error\n            });\n            throw new Error(`数据融合失败: ${error}`);\n        }\n    }\n    /**\n   * 预处理数据源\n   */ async preprocessSources(sources) {\n        const processed = [];\n        for (const source of sources){\n            try {\n                let processedContent = source.content;\n                // 根据内容类型进行预处理\n                switch(source.contentType){\n                    case \"json\":\n                        processedContent = this.processJsonContent(source.content);\n                        break;\n                    case \"pdf\":\n                    case \"docx\":\n                        processedContent = await this.processDocumentContent(source.content);\n                        break;\n                    case \"url\":\n                        processedContent = await this.processUrlContent(source.content);\n                        break;\n                    case \"text\":\n                    default:\n                        processedContent = this.processTextContent(source.content);\n                        break;\n                }\n                // 评估数据质量\n                const qualityScore = this.assessContentQuality(processedContent);\n                const relevanceScore = this.assessContentRelevance(processedContent);\n                processed.push({\n                    ...source,\n                    processedContent,\n                    qualityScore,\n                    relevanceScore\n                });\n            } catch (error) {\n                this.logger.warn(`数据源预处理失败: ${source.sourceId}`, {\n                    error\n                });\n            // 继续处理其他数据源\n            }\n        }\n        return processed;\n    }\n    /**\n   * 应用融合策略\n   */ async applyFusionStrategy(sources, config) {\n        // 过滤低质量数据源\n        const filteredSources = sources.filter((source)=>source.qualityScore >= config.threshold).slice(0, config.maxSources);\n        if (filteredSources.length === 0) {\n            throw new Error(\"没有符合质量要求的数据源\");\n        }\n        switch(config.strategy){\n            case \"weighted\":\n                return this.weightedFusion(filteredSources, config.weights || {});\n            case \"priority\":\n                return this.priorityFusion(filteredSources);\n            case \"consensus\":\n                return this.consensusFusion(filteredSources);\n            default:\n                throw new Error(`不支持的融合策略: ${config.strategy}`);\n        }\n    }\n    /**\n   * 加权融合策略\n   */ weightedFusion(sources, weights) {\n        let fusedContent = \"# 数据融合报告\\n\\n\";\n        const sourcesUsed = [];\n        // 按权重排序\n        const sortedSources = sources.sort((a, b)=>{\n            const weightA = weights[a.sourceId] || a.qualityScore;\n            const weightB = weights[b.sourceId] || b.qualityScore;\n            return weightB - weightA;\n        });\n        for (const source of sortedSources){\n            const weight = weights[source.sourceId] || source.qualityScore;\n            fusedContent += `## 数据源: ${source.sourceId} (权重: ${weight.toFixed(2)})\\n\\n`;\n            fusedContent += `${source.processedContent}\\n\\n`;\n            sourcesUsed.push(source.sourceId);\n        }\n        return {\n            content: fusedContent,\n            sourcesUsed\n        };\n    }\n    /**\n   * 优先级融合策略\n   */ priorityFusion(sources) {\n        // 按综合评分排序\n        const sortedSources = sources.sort((a, b)=>{\n            const scoreA = (a.qualityScore + a.relevanceScore) / 2;\n            const scoreB = (b.qualityScore + b.relevanceScore) / 2;\n            return scoreB - scoreA;\n        });\n        let fusedContent = \"# 优先级数据融合报告\\n\\n\";\n        const sourcesUsed = [];\n        for (const source of sortedSources){\n            const score = (source.qualityScore + source.relevanceScore) / 2;\n            fusedContent += `## 数据源: ${source.sourceId} (评分: ${score.toFixed(2)})\\n\\n`;\n            fusedContent += `${source.processedContent}\\n\\n`;\n            sourcesUsed.push(source.sourceId);\n        }\n        return {\n            content: fusedContent,\n            sourcesUsed\n        };\n    }\n    /**\n   * 共识融合策略\n   */ consensusFusion(sources) {\n        // 简化实现：提取共同关键词和主题\n        const allContent = sources.map((s)=>s.processedContent).join(\" \");\n        const keywords = this.extractKeywords(allContent);\n        const commonThemes = this.identifyCommonThemes(sources.map((s)=>s.processedContent));\n        let fusedContent = \"# 共识数据融合报告\\n\\n\";\n        fusedContent += `## 关键词汇\\n${keywords.join(\", \")}\\n\\n`;\n        fusedContent += `## 共同主题\\n${commonThemes.join(\"\\n\")}\\n\\n`;\n        fusedContent += `## 详细内容\\n\\n`;\n        const sourcesUsed = [];\n        for (const source of sources){\n            fusedContent += `### ${source.sourceId}\\n${source.processedContent}\\n\\n`;\n            sourcesUsed.push(source.sourceId);\n        }\n        return {\n            content: fusedContent,\n            sourcesUsed\n        };\n    }\n    /**\n   * 处理不同类型的内容\n   */ processJsonContent(content) {\n        try {\n            const parsed = JSON.parse(content);\n            return JSON.stringify(parsed, null, 2);\n        } catch  {\n            return content;\n        }\n    }\n    processTextContent(content) {\n        // 清理和格式化文本\n        return content.replace(/\\s+/g, \" \").replace(/\\n\\s*\\n/g, \"\\n\\n\").trim();\n    }\n    async processDocumentContent(content) {\n        // 这里应该实现文档解析逻辑\n        // 暂时返回原内容\n        return content;\n    }\n    async processUrlContent(content) {\n        // 这里应该实现URL内容抓取逻辑\n        // 暂时返回原内容\n        return content;\n    }\n    /**\n   * 评估内容质量\n   */ assessContentQuality(content) {\n        let score = 0.5 // 基础分\n        ;\n        // 长度评分\n        if (content.length > 100) score += 0.1;\n        if (content.length > 500) score += 0.1;\n        if (content.length > 1000) score += 0.1;\n        // 结构评分\n        if (content.includes(\"\\n\")) score += 0.05;\n        if (/[.!?]/.test(content)) score += 0.05;\n        // 内容丰富度评分\n        const uniqueWords = new Set(content.toLowerCase().split(/\\s+/)).size;\n        if (uniqueWords > 50) score += 0.1;\n        if (uniqueWords > 100) score += 0.1;\n        return Math.min(score, 1.0);\n    }\n    /**\n   * 评估内容相关性\n   */ assessContentRelevance(content) {\n        // OCTI相关关键词\n        const octiKeywords = [\n            \"组织\",\n            \"团队\",\n            \"领导\",\n            \"管理\",\n            \"文化\",\n            \"能力\",\n            \"评估\",\n            \"结构化\",\n            \"灵活\",\n            \"直觉\",\n            \"思考\",\n            \"愿景\",\n            \"行动\",\n            \"深思\"\n        ];\n        const contentLower = content.toLowerCase();\n        const matchedKeywords = octiKeywords.filter((keyword)=>contentLower.includes(keyword));\n        return Math.min(matchedKeywords.length / octiKeywords.length * 2, 1.0);\n    }\n    /**\n   * 计算融合置信度\n   */ calculateConfidence(sources, result) {\n        if (sources.length === 0) return 0;\n        const usedSources = sources.slice(0, result.sourcesUsed.length);\n        const avgQuality = usedSources.reduce((sum, s)=>sum + s.qualityScore, 0) / usedSources.length;\n        const avgRelevance = usedSources.reduce((sum, s)=>sum + s.relevanceScore, 0) / usedSources.length;\n        // 综合质量、相关性和数据源数量\n        const sourceCountFactor = Math.min(usedSources.length / 3, 1.0);\n        return avgQuality * 0.4 + avgRelevance * 0.4 + sourceCountFactor * 0.2;\n    }\n    /**\n   * 评估整体数据质量\n   */ assessDataQuality(sources) {\n        if (sources.length === 0) return 0;\n        return sources.reduce((sum, s)=>sum + s.qualityScore, 0) / sources.length;\n    }\n    /**\n   * 提取关键词\n   */ extractKeywords(content) {\n        const words = content.toLowerCase().replace(/[^\\w\\s\\u4e00-\\u9fff]/g, \" \").split(/\\s+/).filter((word)=>word.length > 2);\n        const wordCount = new Map();\n        for (const word of words){\n            wordCount.set(word, (wordCount.get(word) || 0) + 1);\n        }\n        return Array.from(wordCount.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 10).map(([word])=>word);\n    }\n    /**\n   * 识别共同主题\n   */ identifyCommonThemes(contents) {\n        // 简化实现：查找在多个内容中都出现的句子模式\n        const themes = [];\n        // 这里应该实现更复杂的主题识别算法\n        // 暂时返回基础主题\n        themes.push(\"组织能力评估相关内容\");\n        return themes;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/data/data-fusion-engine.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llm/llm-api-client.ts":
/*!********************************************!*\
  !*** ./src/services/llm/llm-api-client.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLMApiClient: () => (/* binding */ LLMApiClient)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n// LLM请求配置Schema\nconst LLMRequestSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    model: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    messages: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        role: zod__WEBPACK_IMPORTED_MODULE_1__[\"enum\"]([\n            \"system\",\n            \"user\",\n            \"assistant\"\n        ]),\n        content: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n    })),\n    temperature: zod__WEBPACK_IMPORTED_MODULE_1__.number().min(0).max(2).optional(),\n    maxTokens: zod__WEBPACK_IMPORTED_MODULE_1__.number().positive().optional(),\n    stream: zod__WEBPACK_IMPORTED_MODULE_1__.boolean().optional()\n});\n// LLM响应Schema\nconst LLMResponseSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        message: zod__WEBPACK_IMPORTED_MODULE_1__.object({\n            role: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n            content: zod__WEBPACK_IMPORTED_MODULE_1__.string()\n        }),\n        finishReason: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional()\n    })),\n    usage: zod__WEBPACK_IMPORTED_MODULE_1__.object({\n        promptTokens: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n        completionTokens: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional(),\n        totalTokens: zod__WEBPACK_IMPORTED_MODULE_1__.number().optional()\n    }).optional()\n});\n/**\n * LLM API客户端类\n */ class LLMApiClient {\n    constructor(config = {}){\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"LLMApiClient\");\n        this.config = {\n            defaultTimeout: 30000,\n            maxRetries: 3,\n            minimax: {\n                apiKey: process.env.MINIMAX_API_KEY,\n                baseUrl: process.env.MINIMAX_BASE_URL || \"https://api.minimax.chat/v1\",\n                timeout: 30000,\n                ...config.minimax\n            },\n            deepseek: {\n                apiKey: process.env.DEEPSEEK_API_KEY,\n                baseUrl: process.env.DEEPSEEK_BASE_URL || \"https://api.deepseek.com\",\n                timeout: 30000,\n                ...config.deepseek\n            },\n            ...config\n        };\n        // 初始化常量属性\n        this.MINIMAX_BASE_URL = this.config.minimax?.baseUrl || \"https://api.minimax.chat/v1\";\n        this.DEEPSEEK_BASE_URL = this.config.deepseek?.baseUrl || \"https://api.deepseek.com\";\n        this.MAX_RETRIES = this.config.maxRetries || 3;\n        this.DEFAULT_TIMEOUT = this.config.defaultTimeout || 30000;\n    }\n    /**\n   * 获取默认配置\n   */ getDefaultConfig() {\n        return {\n            defaultTimeout: 30000,\n            maxRetries: 3,\n            minimax: {\n                apiKey: process.env.MINIMAX_API_KEY,\n                baseUrl: process.env.MINIMAX_BASE_URL || \"https://api.minimax.chat/v1\",\n                timeout: 30000\n            },\n            deepseek: {\n                apiKey: process.env.DEEPSEEK_API_KEY,\n                baseUrl: process.env.DEEPSEEK_BASE_URL || \"https://api.deepseek.com\",\n                timeout: 30000\n            }\n        };\n    }\n    /**\n   * 调用MiniMax API\n   */ async callMiniMax(request) {\n        const apiKey = process.env.MINIMAX_API_KEY;\n        if (!apiKey) {\n            throw new Error(\"MiniMax API密钥未配置\");\n        }\n        return this.makeRequest(`${this.MINIMAX_BASE_URL}/chat/completions`, {\n            ...request,\n            model: request.model || \"abab6.5-chat\"\n        }, {\n            \"Authorization\": `Bearer ${apiKey}`,\n            \"Content-Type\": \"application/json\"\n        });\n    }\n    /**\n   * 调用DeepSeek API\n   */ async callDeepSeek(request) {\n        const apiKey = process.env.DEEPSEEK_API_KEY;\n        if (!apiKey) {\n            throw new Error(\"DeepSeek API密钥未配置\");\n        }\n        return this.makeRequest(`${this.DEEPSEEK_BASE_URL}/chat/completions`, {\n            ...request,\n            model: request.model || \"deepseek-chat\"\n        }, {\n            \"Authorization\": `Bearer ${apiKey}`,\n            \"Content-Type\": \"application/json\"\n        });\n    }\n    /**\n   * 统一的HTTP请求方法\n   */ async makeRequest(url, data, headers) {\n        let lastError = null;\n        for(let attempt = 1; attempt <= this.MAX_RETRIES; attempt++){\n            try {\n                this.logger.debug(`LLM API请求 (尝试 ${attempt}/${this.MAX_RETRIES})`, {\n                    url: url.replace(/\\/v1.*/, \"/v1/***\"),\n                    model: data.model\n                });\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), this.DEFAULT_TIMEOUT);\n                const response = await fetch(url, {\n                    method: \"POST\",\n                    headers,\n                    body: JSON.stringify(data),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`HTTP ${response.status}: ${errorText}`);\n                }\n                const responseData = await response.json();\n                const validatedResponse = LLMResponseSchema.parse(responseData);\n                // 添加便捷的content属性\n                const responseWithContent = {\n                    ...validatedResponse,\n                    content: validatedResponse.choices[0]?.message?.content || \"\"\n                };\n                this.logger.info(\"LLM API调用成功\", {\n                    model: data.model,\n                    tokensUsed: validatedResponse.usage?.totalTokens || 0,\n                    attempt\n                });\n                return responseWithContent;\n            } catch (error) {\n                lastError = error;\n                this.logger.warn(`LLM API调用失败 (尝试 ${attempt}/${this.MAX_RETRIES})`, {\n                    error: lastError.message,\n                    model: data.model\n                });\n                // 如果不是最后一次尝试，等待后重试\n                if (attempt < this.MAX_RETRIES) {\n                    const delay = Math.pow(2, attempt) * 1000 // 指数退避\n                    ;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                }\n            }\n        }\n        this.logger.error(\"LLM API调用最终失败\", {\n            error: lastError?.message,\n            attempts: this.MAX_RETRIES\n        });\n        throw lastError || new Error(\"LLM API调用失败\");\n    }\n    /**\n   * 统一的聊天接口\n   */ async chat(provider, request) {\n        switch(provider){\n            case \"minimax\":\n                return this.callMiniMax(request);\n            case \"deepseek\":\n                return this.callDeepSeek(request);\n            default:\n                throw new Error(`不支持的LLM提供商: ${provider}`);\n        }\n    }\n    /**\n   * 双模型聊天接口\n   */ async dualModelChat(request, strategy = \"sequential\") {\n        if (strategy === \"parallel\") {\n            // 并行调用两个模型\n            const [minimaxResult, deepseekResult] = await Promise.allSettled([\n                this.callMiniMax(request),\n                this.callDeepSeek(request)\n            ]);\n            // 选择成功的结果，优先选择MiniMax\n            if (minimaxResult.status === \"fulfilled\") {\n                return minimaxResult.value;\n            } else if (deepseekResult.status === \"fulfilled\") {\n                return deepseekResult.value;\n            } else {\n                throw new Error(\"双模型调用均失败\");\n            }\n        } else {\n            // 顺序调用，失败时切换到另一个模型\n            try {\n                return await this.callMiniMax(request);\n            } catch (error) {\n                this.logger.warn(\"MiniMax调用失败，切换到DeepSeek\", {\n                    error\n                });\n                return await this.callDeepSeek(request);\n            }\n        }\n    }\n    /**\n   * 健康检查\n   */ async healthCheck() {\n        const results = {\n            minimax: false,\n            deepseek: false\n        };\n        // 检查MiniMax\n        try {\n            await this.callMiniMax({\n                model: \"abab6.5-chat\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: \"ping\"\n                    }\n                ],\n                maxTokens: 10\n            });\n            results.minimax = true;\n        } catch (error) {\n            this.logger.warn(\"MiniMax健康检查失败\", {\n                error\n            });\n        }\n        // 检查DeepSeek\n        try {\n            await this.callDeepSeek({\n                model: \"deepseek-chat\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: \"ping\"\n                    }\n                ],\n                maxTokens: 10\n            });\n            results.deepseek = true;\n        } catch (error) {\n            this.logger.warn(\"DeepSeek健康检查失败\", {\n                error\n            });\n        }\n        return results;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llm/llm-api-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llm/prompt-builder.ts":
/*!********************************************!*\
  !*** ./src/services/llm/prompt-builder.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PromptBuilder: () => (/* binding */ PromptBuilder)\n/* harmony export */ });\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n// 提示词模板Schema\nconst PromptTemplateSchema = zod__WEBPACK_IMPORTED_MODULE_1__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    systemPrompt: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    userPromptTemplate: zod__WEBPACK_IMPORTED_MODULE_1__.string(),\n    variables: zod__WEBPACK_IMPORTED_MODULE_1__.array(zod__WEBPACK_IMPORTED_MODULE_1__.string()),\n    version: zod__WEBPACK_IMPORTED_MODULE_1__.string().optional(),\n    metadata: zod__WEBPACK_IMPORTED_MODULE_1__.record(zod__WEBPACK_IMPORTED_MODULE_1__.any()).optional()\n});\n/**\n * 提示词构建器类\n */ class PromptBuilder {\n    constructor(config = {}){\n        this.logger = new _lib_logger__WEBPACK_IMPORTED_MODULE_0__.Logger(\"PromptBuilder\");\n        this.templates = new Map();\n        this.config = {\n            templatePath: \"./config/prompts\",\n            cacheEnabled: true,\n            maxTokens: 4000,\n            temperature: 0.7,\n            ...config\n        };\n        // 初始化默认模板\n        this.initializeDefaultTemplates();\n    }\n    /**\n   * 获取默认配置\n   */ getDefaultConfig() {\n        return {\n            templatePath: \"./config/prompts\",\n            cacheEnabled: true,\n            maxTokens: 4000,\n            temperature: 0.7\n        };\n    }\n    /**\n   * 初始化默认提示词模板\n   */ initializeDefaultTemplates() {\n        // 问卷设计师提示词模板\n        this.addTemplate({\n            id: \"questionnaire_designer\",\n            name: \"问卷设计师\",\n            systemPrompt: `你是OCTI组织能力评估系统的专业问卷设计师。你的任务是根据用户需求设计高质量的评估问卷。\n\n核心要求：\n1. 严格遵循OCTI四维八极理论框架\n2. 问题设计要具有科学性和专业性\n3. 确保问题的区分度和信效度\n4. 输出标准JSON格式的问卷结构\n\nOCTI四维八极框架：\n- S/F维度：结构化 ↔ 灵活化\n- I/T维度：直觉 ↔ 思考  \n- M/V维度：管理 ↔ 愿景\n- A/D维度：行动 ↔ 深思\n\n请确保每个维度的问题数量均衡，问题表述清晰准确。`,\n            userPromptTemplate: `请为以下评估需求设计问卷：\n\n评估类型：{{assessmentType}}\n目标维度：{{dimensions}}\n问卷版本：{{version}}\n特殊要求：{{requirements}}\n\n请生成包含以下结构的JSON格式问卷：\n{\n  \"id\": \"问卷唯一标识\",\n  \"title\": \"问卷标题\",\n  \"description\": \"问卷描述\",\n  \"version\": \"版本信息\",\n  \"questions\": [\n    {\n      \"id\": \"问题ID\",\n      \"text\": \"问题内容\",\n      \"type\": \"问题类型\",\n      \"dimension\": \"所属维度\",\n      \"subdimension\": \"子维度\",\n      \"options\": [选项数组],\n      \"weight\": 权重值\n    }\n  ],\n  \"metadata\": {\n    \"estimatedTime\": 预估完成时间,\n    \"totalQuestions\": 问题总数,\n    \"dimensionDistribution\": 维度分布统计\n  }\n}`,\n            variables: [\n                \"assessmentType\",\n                \"dimensions\",\n                \"version\",\n                \"requirements\"\n            ]\n        });\n        // 组织评估导师提示词模板\n        this.addTemplate({\n            id: \"organization_tutor_standard\",\n            name: \"组织评估导师-标准版\",\n            systemPrompt: `你是OCTI组织能力评估系统的专业分析师。你的任务是基于问卷回答结果，提供深入的组织能力分析和改进建议。\n\n分析框架：\n1. OCTI四维八极得分计算\n2. 组织能力优势识别\n3. 改进机会分析\n4. 具体行动建议\n\n输出要求：\n- 客观准确的数据分析\n- 实用可行的改进建议\n- 结构化的报告格式\n- 专业的语言表达`,\n            userPromptTemplate: `请分析以下组织评估数据：\n\n组织信息：{{organizationInfo}}\n问卷回答：{{responses}}\n评估版本：{{version}}\n\n请提供包含以下内容的分析报告：\n1. 总体评估得分\n2. 四维八极详细分析\n3. 组织优势总结\n4. 改进建议\n5. 下一步行动计划`,\n            variables: [\n                \"organizationInfo\",\n                \"responses\",\n                \"version\"\n            ]\n        });\n        this.logger.info(`已加载 ${this.templates.size} 个默认提示词模板`);\n    }\n    /**\n   * 添加提示词模板\n   */ addTemplate(template) {\n        const validated = PromptTemplateSchema.parse(template);\n        this.templates.set(validated.id, validated);\n        this.logger.debug(`已添加提示词模板: ${validated.id}`);\n    }\n    /**\n   * 获取提示词模板\n   */ getTemplate(id) {\n        return this.templates.get(id) || null;\n    }\n    /**\n   * 构建提示词\n   */ buildPrompt(templateId, variables) {\n        const template = this.getTemplate(templateId);\n        if (!template) {\n            throw new Error(`提示词模板不存在: ${templateId}`);\n        }\n        // 检查必需变量\n        const missingVars = template.variables.filter((varName)=>!(varName in variables) || variables[varName] === undefined);\n        if (missingVars.length > 0) {\n            throw new Error(`缺少必需变量: ${missingVars.join(\", \")}`);\n        }\n        // 替换变量\n        let userPrompt = template.userPromptTemplate;\n        for (const [key, value] of Object.entries(variables)){\n            const placeholder = `{{${key}}}`;\n            const replacement = typeof value === \"string\" ? value : JSON.stringify(value);\n            userPrompt = userPrompt.replace(new RegExp(placeholder, \"g\"), replacement);\n        }\n        this.logger.debug(`已构建提示词: ${templateId}`, {\n            variablesUsed: Object.keys(variables)\n        });\n        return {\n            systemPrompt: template.systemPrompt,\n            userPrompt\n        };\n    }\n    /**\n   * 列出所有模板\n   */ listTemplates() {\n        return Array.from(this.templates.values());\n    }\n    /**\n   * 验证提示词安全性\n   */ validatePromptSafety(prompt) {\n        const issues = [];\n        // 检查潜在的注入攻击\n        const dangerousPatterns = [\n            /ignore\\s+previous\\s+instructions/i,\n            /system\\s*:\\s*you\\s+are\\s+now/i,\n            /forget\\s+everything/i,\n            /new\\s+instructions/i,\n            /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n            /javascript:/i,\n            /on\\w+\\s*=/i\n        ];\n        for (const pattern of dangerousPatterns){\n            if (pattern.test(prompt)) {\n                issues.push(`检测到潜在的注入攻击模式: ${pattern.source}`);\n            }\n        }\n        // 检查敏感信息泄露\n        const sensitivePatterns = [\n            /api[_-]?key/i,\n            /password/i,\n            /secret/i,\n            /token/i,\n            /credential/i\n        ];\n        for (const pattern of sensitivePatterns){\n            if (pattern.test(prompt)) {\n                issues.push(`检测到可能的敏感信息: ${pattern.source}`);\n            }\n        }\n        return {\n            safe: issues.length === 0,\n            issues\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llm/prompt-builder.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.ts&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();