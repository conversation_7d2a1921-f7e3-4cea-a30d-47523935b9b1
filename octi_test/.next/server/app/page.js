/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhcHBsZSUyRkRvY3VtZW50cyUyRjIuMSUyMEFJJTIwSm91cm5leSUyRkN1cnNvcl9wcm9qZWN0cyUyRm9jdGlfdGVzdCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktYXNzZXNzbWVudC1zeXN0ZW0vPzJhNmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXBwbGUvRG9jdW1lbnRzLzIuMSBBSSBKb3VybmV5L0N1cnNvcl9wcm9qZWN0cy9vY3RpX3Rlc3Qvc3JjL2FwcC9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_home_hero_section__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_assessment_cards__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_octi_introduction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_feature_comparison__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_user_guide__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n/**\n * OCTI智能评估系统 - 新版首页\n * 基于用户导向的产品展示页面设计\n */ function HomePage() {\n    // 处理开始评估\n    const handleStartAssessment = ()=>{\n        window.location.href = \"/assessment\";\n    };\n    // 处理标准版评估\n    const handleStartStandard = ()=>{\n        window.location.href = \"/assessment?version=standard\";\n    };\n    // 处理专业版评估\n    const handleStartProfessional = ()=>{\n        window.location.href = \"/assessment?version=professional\";\n    };\n    // 处理了解更多\n    const handleLearnMore = ()=>{\n        // 滚动到OCTI介绍部分\n        const introSection = document.getElementById(\"octi-introduction\");\n        if (introSection) {\n            introSection.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // 处理联系支持\n    const handleContactSupport = ()=>{\n        // 这里可以打开客服聊天或跳转到联系页面\n        window.open(\"mailto:<EMAIL>\", \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_hero_section__WEBPACK_IMPORTED_MODULE_2__.HeroSection, {\n                onStartAssessment: handleStartAssessment,\n                onLearnMore: handleLearnMore\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-6 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"选择适合您的评估版本\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"根据您的组织规模和需求，选择最适合的OCTI评估版本，获得精准的能力分析\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_assessment_cards__WEBPACK_IMPORTED_MODULE_3__.AssessmentCards, {\n                            onStartStandard: handleStartStandard,\n                            onStartProfessional: handleStartProfessional\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"octi-introduction\",\n                className: \"py-16 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_octi_introduction__WEBPACK_IMPORTED_MODULE_4__.OCTIIntroduction, {\n                    onLearnMore: handleLearnMore\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-6 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_feature_comparison__WEBPACK_IMPORTED_MODULE_5__.FeatureComparison, {\n                    onStartStandard: handleStartStandard,\n                    onStartProfessional: handleStartProfessional\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_user_guide__WEBPACK_IMPORTED_MODULE_6__.UserGuide, {\n                    onStartAssessment: handleStartAssessment,\n                    onContactSupport: handleContactSupport\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!**************************************************!*\
  !*** ./src/components/home/<USER>
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssessmentCards: () => (/* binding */ AssessmentCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Shield,Star,Target,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Shield,Star,Target,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Shield,Star,Target,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Shield,Star,Target,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Shield,Star,Target,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Shield,Star,Target,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Shield,Star,Target,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Shield,Star,Target,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ AssessmentCards auto */ \n\n\n\n\n/**\n * 评估类型选择卡片组件\n * 展示标准版和专业版的功能对比和价格\n */ const AssessmentCards = ({ onStartStandard, onStartProfessional })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"选择适合您的评估方案\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"基于OCTI四维八极理论，提供标准版和专业版两种评估方案，满足不同组织的需求\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"relative border-2 border-gray-200 hover:border-blue-300 transition-all duration-300 hover:shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"text-center pb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-8 h-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"标准版评估\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            className: \"text-lg text-gray-600\",\n                                            children: \"基础组织能力诊断\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl font-bold text-blue-600\",\n                                                    children: \"\\xa599\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 ml-2\",\n                                                    children: \"/ 次评估\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 59,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 67,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"60题标准深度评估\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 69,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"覆盖四维八极核心能力指标\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 70,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 68,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 75,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"四维能力雷达图\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 77,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"可视化展示组织能力分布\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 78,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 76,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 83,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"组织类型识别\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 85,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"16种组织类型精准匹配\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 86,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 84,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"基础发展建议\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 93,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"9个章节详细分析报告\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 94,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 92,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"PDF报告下载\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 101,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"专业格式，便于分享\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 102,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 100,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-blue-900 mb-2\",\n                                                    children: \"适用场景\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"适合初次评估、小型组织或希望快速了解组织能力现状的用户\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: onStartStandard,\n                                            className: \"w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-semibold rounded-xl shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200\",\n                                            children: [\n                                                \"开始标准版评估\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"ml-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"relative border-2 border-purple-200 hover:border-purple-300 transition-all duration-300 hover:shadow-xl bg-gradient-to-br from-white to-purple-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"推荐方案\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"text-center pb-8 pt-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-8 h-8 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"专业版评估\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            className: \"text-lg text-gray-600\",\n                                            children: \"深度组织能力诊断与分析\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-4xl font-bold text-purple-600\",\n                                                    children: \"\\xa5399\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 ml-2\",\n                                                    children: \"/ 次评估\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 font-medium\",\n                                                children: \"包含标准版所有功能，另外增加：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 159,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"多源数据融合分析\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"支持文档上传和网络数据采集\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 160,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"双模型协作分析\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"MiniMax + DeepSeek深度分析\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 168,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"详细专业报告\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"14个章节深度分析报告\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 176,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"个性化发展路径\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"定制化改进建议和行动计划\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 191,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: \"6个月免费复测\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"跟踪改进效果，持续优化\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 192,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-50 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-purple-900 mb-2\",\n                                                    children: \"适用场景\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-purple-700\",\n                                                    children: \"适合中大型组织、需要深度分析或制定详细改进计划的用户\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: onStartProfessional,\n                                            className: \"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 text-lg font-semibold rounded-xl shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200\",\n                                            children: [\n                                                \"开始专业版评估\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"ml-2 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-2 text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"数据安全保护\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-2 text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"15-30分钟完成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-2 text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Shield_Star_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"专业团队支持\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!****************************************************!*\
  !*** ./src/components/home/<USER>
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeatureComparison: () => (/* binding */ FeatureComparison)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Star,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Star,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Star,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Star,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ FeatureComparison auto */ \n\n\n\n\n/**\n * 功能对比表组件\n * 详细展示标准版和专业版的功能差异\n */ const FeatureComparison = ({ onStartStandard, onStartProfessional })=>{\n    // 功能对比数据\n    const features = [\n        {\n            category: \"评估内容\",\n            items: [\n                {\n                    name: \"评估题目数量\",\n                    standard: \"60题\",\n                    professional: \"60题+定制题目\"\n                },\n                {\n                    name: \"四维能力分析\",\n                    standard: true,\n                    professional: true\n                },\n                {\n                    name: \"组织类型识别\",\n                    standard: \"16种基础类型\",\n                    professional: \"16种+细分子类型\"\n                },\n                {\n                    name: \"能力雷达图\",\n                    standard: true,\n                    professional: true\n                },\n                {\n                    name: \"多源数据融合\",\n                    standard: false,\n                    professional: true\n                },\n                {\n                    name: \"文档上传分析\",\n                    standard: false,\n                    professional: true\n                }\n            ]\n        },\n        {\n            category: \"分析深度\",\n            items: [\n                {\n                    name: \"AI分析模型\",\n                    standard: \"单模型分析\",\n                    professional: \"双模型协作\"\n                },\n                {\n                    name: \"分析报告章节\",\n                    standard: \"9个章节\",\n                    professional: \"14个章节\"\n                },\n                {\n                    name: \"发展建议详细度\",\n                    standard: \"基础建议\",\n                    professional: \"详细行动计划\"\n                },\n                {\n                    name: \"个性化程度\",\n                    standard: \"标准化分析\",\n                    professional: \"高度个性化\"\n                },\n                {\n                    name: \"行业对标分析\",\n                    standard: false,\n                    professional: true\n                },\n                {\n                    name: \"竞争力评估\",\n                    standard: false,\n                    professional: true\n                }\n            ]\n        },\n        {\n            category: \"报告功能\",\n            items: [\n                {\n                    name: \"PDF报告下载\",\n                    standard: true,\n                    professional: true\n                },\n                {\n                    name: \"在线查看\",\n                    standard: true,\n                    professional: true\n                },\n                {\n                    name: \"数据导出\",\n                    standard: \"基础数据\",\n                    professional: \"完整数据+图表\"\n                },\n                {\n                    name: \"报告定制\",\n                    standard: false,\n                    professional: true\n                },\n                {\n                    name: \"多语言支持\",\n                    standard: false,\n                    professional: true\n                },\n                {\n                    name: \"品牌定制\",\n                    standard: false,\n                    professional: true\n                }\n            ]\n        },\n        {\n            category: \"服务支持\",\n            items: [\n                {\n                    name: \"评估有效期\",\n                    standard: \"6个月\",\n                    professional: \"12个月\"\n                },\n                {\n                    name: \"免费复测次数\",\n                    standard: \"0次\",\n                    professional: \"2次\"\n                },\n                {\n                    name: \"专家咨询\",\n                    standard: false,\n                    professional: \"1小时咨询\"\n                },\n                {\n                    name: \"实施指导\",\n                    standard: false,\n                    professional: true\n                },\n                {\n                    name: \"进度跟踪\",\n                    standard: false,\n                    professional: true\n                },\n                {\n                    name: \"优先支持\",\n                    standard: false,\n                    professional: true\n                }\n            ]\n        }\n    ];\n    const renderFeatureValue = (value, isProfessional = false)=>{\n        if (typeof value === \"boolean\") {\n            return value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-5 h-5 text-green-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-5 h-5 text-gray-300\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: `text-sm ${isProfessional ? \"font-semibold text-purple-700\" : \"text-gray-700\"}`,\n            children: value\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"功能详细对比\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"深入了解标准版和专业版的功能差异，选择最适合您组织需求的评估方案\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-lg overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-50 to-purple-50 px-6 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center md:text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                children: \"功能项目\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"详细功能对比\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl p-6 shadow-sm border-2 border-blue-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-blue-600 mb-2\",\n                                                    children: \"标准版\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-blue-600 mb-2\",\n                                                    children: \"\\xa599\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4\",\n                                                    children: \"基础组织能力诊断\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: onStartStandard,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white\",\n                                                    children: \"选择标准版\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl p-6 shadow-sm border-2 border-purple-200 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-xs font-semibold flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-3 h-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                lineNumber: 125,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \"推荐\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-purple-600 mb-2\",\n                                                    children: \"专业版\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-purple-600 mb-2\",\n                                                    children: \"\\xa5399\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4\",\n                                                    children: \"深度组织能力诊断\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: onStartProfessional,\n                                                    className: \"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white\",\n                                                    children: \"选择专业版\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200\",\n                            children: features.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-6 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-6 bg-blue-500 rounded-full mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                category.category\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: category.items.map((item, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 items-center py-3 hover:bg-gray-50 rounded-lg px-4 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: renderFeatureValue(item.standard)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: renderFeatureValue(item.professional, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 163,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, itemIndex, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, categoryIndex, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 grid grid-cols-1 md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"border-l-4 border-l-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-blue-600\",\n                                            children: \"标准版适合\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"以下类型的组织建议选择标准版评估\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"初次进行组织能力评估的企业\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"50人以下的小型组织\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"希望快速了解组织现状的团队\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"预算有限但需要专业评估的组织\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"border-l-4 border-l-purple-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-purple-600\",\n                                            children: \"专业版适合\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"以下类型的组织建议选择专业版评估\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"50人以上的中大型组织\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"需要制定详细改进计划的企业\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"希望获得专家指导的组织\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"需要持续跟踪改进效果的团队\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"还有疑问？\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 mb-6\",\n                                children: \"我们的专业团队随时为您提供咨询服务，帮助您选择最适合的评估方案\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        className: \"border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white\",\n                                        children: [\n                                            \"在线咨询\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"ml-2 w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        className: \"border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white\",\n                                        children: [\n                                            \"预约演示\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"ml-2 w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n            lineNumber: 86,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!**********************************************!*\
  !*** ./src/components/home/<USER>
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeroSection: () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Target,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Target,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Target,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Target,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ HeroSection auto */ \n\n\n\n/**\n * 首页品牌展示区域组件\n * 展示OCTI品牌、核心价值主张和主要CTA按钮\n */ const HeroSection = ({ onStartAssessment, onLearnMore })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative bg-gradient-to-b from-blue-50 to-white py-20 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mr-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"OCTI\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 26,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900\",\n                                        children: \"OCTI 智能评估系统\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 29,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-600 mb-4 max-w-4xl mx-auto\",\n                                children: \"基于四维八极理论的组织能力智能诊断\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-500 mb-8 max-w-3xl mx-auto\",\n                                children: \"运用先进的AI技术，为您的组织提供科学、准确、全面的能力评估， 助力组织发展和人才培养\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-8 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center p-6 bg-white rounded-xl shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"智能分析\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-center\",\n                                        children: \"基于AI算法的深度分析，提供精准的能力评估结果\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 51,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center p-6 bg-white rounded-xl shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-6 h-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"科学理论\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-center\",\n                                        children: \"基于四维八极理论，16种组织类型全面覆盖\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center p-6 bg-white rounded-xl shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"专业报告\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-center\",\n                                        children: \"详细的分析报告和改进建议，助力组织发展\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: onStartAssessment,\n                                size: \"lg\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200\",\n                                children: [\n                                    \"开始OCTI评估\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"ml-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: onLearnMore,\n                                size: \"lg\",\n                                variant: \"outline\",\n                                className: \"border-2 border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200\",\n                                children: \"了解更多\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-blue-600 mb-2\",\n                                        children: \"10,000+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"评估完成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-green-600 mb-2\",\n                                        children: \"500+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"企业客户\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-purple-600 mb-2\",\n                                        children: \"16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"组织类型\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-orange-600 mb-2\",\n                                        children: \"98%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"满意度\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-16 h-16 bg-green-200 rounded-full opacity-20 animate-pulse delay-1000\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-20 w-24 h-24 bg-purple-200 rounded-full opacity-20 animate-pulse delay-2000\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!***************************************************!*\
  !*** ./src/components/home/<USER>
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OCTIIntroduction: () => (/* binding */ OCTIIntroduction)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,Brain,Lightbulb,Network,Shield,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ OCTIIntroduction auto */ \n\n\n\n\n/**\n * OCTI四维八极理论介绍组件\n * 展示理论背景、模型结构和应用价值\n */ const OCTIIntroduction = ({ onLearnMore })=>{\n    // 四维能力数据\n    const dimensions = [\n        {\n            name: \"认知维度\",\n            icon: _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"blue\",\n            description: \"组织的学习能力、创新思维和知识管理水平\",\n            capabilities: [\n                \"学习能力\",\n                \"创新思维\",\n                \"知识管理\",\n                \"决策质量\"\n            ]\n        },\n        {\n            name: \"关系维度\",\n            icon: _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"green\",\n            description: \"组织内外部关系建立、维护和协调能力\",\n            capabilities: [\n                \"团队协作\",\n                \"沟通交流\",\n                \"合作伙伴\",\n                \"客户关系\"\n            ]\n        },\n        {\n            name: \"执行维度\",\n            icon: _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"purple\",\n            description: \"组织的目标达成、任务执行和结果交付能力\",\n            capabilities: [\n                \"目标管理\",\n                \"执行效率\",\n                \"质量控制\",\n                \"结果导向\"\n            ]\n        },\n        {\n            name: \"适应维度\",\n            icon: _barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"orange\",\n            description: \"组织面对变化的应对、调整和发展能力\",\n            capabilities: [\n                \"变化应对\",\n                \"灵活调整\",\n                \"持续改进\",\n                \"发展规划\"\n            ]\n        }\n    ];\n    // 16种组织类型示例\n    const organizationTypes = [\n        {\n            name: \"创新先锋型\",\n            code: \"CCEE\",\n            description: \"高认知高执行，引领行业创新\"\n        },\n        {\n            name: \"协作高效型\",\n            code: \"RREE\",\n            description: \"强关系强执行，团队协作卓越\"\n        },\n        {\n            name: \"学习适应型\",\n            code: \"CCAA\",\n            description: \"高认知强适应，持续学习成长\"\n        },\n        {\n            name: \"稳健发展型\",\n            code: \"RRAA\",\n            description: \"好关系强适应，稳步持续发展\"\n        },\n        {\n            name: \"执行专家型\",\n            code: \"EEEE\",\n            description: \"超强执行力，目标达成专家\"\n        },\n        {\n            name: \"变革领导型\",\n            code: \"AAAA\",\n            description: \"超强适应力，变革管理领导者\"\n        },\n        {\n            name: \"平衡发展型\",\n            code: \"CREA\",\n            description: \"四维均衡，全面协调发展\"\n        },\n        {\n            name: \"潜力成长型\",\n            code: \"CRCE\",\n            description: \"基础扎实，具备成长潜力\"\n        }\n    ];\n    const getColorClasses = (color)=>{\n        const colorMap = {\n            blue: \"bg-blue-100 text-blue-600 border-blue-200\",\n            green: \"bg-green-100 text-green-600 border-green-200\",\n            purple: \"bg-purple-100 text-purple-600 border-purple-200\",\n            orange: \"bg-orange-100 text-orange-600 border-orange-200\"\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-b from-gray-50 to-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-8 h-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"OCTI四维八极理论\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                            children: \"基于组织行为学和管理科学的前沿研究，构建科学的组织能力评估框架\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"科学权威\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"实践验证\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"持续优化\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900 text-center mb-12\",\n                            children: \"四维能力模型\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: dimensions.map((dimension, index)=>{\n                                const IconComponent = dimension.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"relative overflow-hidden hover:shadow-lg transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            className: \"text-center pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4 ${getColorClasses(dimension.color)}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 126,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: dimension.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: dimension.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: dimension.capabilities.map((capability, capIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm text-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-2 h-2 rounded-full mr-2 ${dimension.color === \"blue\" ? \"bg-blue-400\" : dimension.color === \"green\" ? \"bg-green-400\" : dimension.color === \"purple\" ? \"bg-purple-400\" : \"bg-orange-400\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                lineNumber: 139,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            capability\n                                                        ]\n                                                    }, capIndex, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 138,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 136,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 123,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"八极分析法\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"每个维度分为高低两极，形成2⁴=16种组织类型，精准识别组织特征\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl shadow-lg p-8 mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: \"OCTI\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: \"四维八极\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-8 max-w-2xl mx-auto\",\n                                        children: dimensions.map((dimension, index)=>{\n                                            const IconComponent = dimension.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-3 ${getColorClasses(dimension.color)}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"w-8 h-8\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 182,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 181,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-1\",\n                                                        children: dimension.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: \"高极 ↔ 低极\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 186,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 185,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 180,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"16种组织类型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"基于四维八极组合，识别出16种不同的组织能力类型，每种类型都有独特的特征和发展路径\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: organizationTypes.map((type, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"hover:shadow-md transition-all duration-200 border-l-4 border-l-blue-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 text-sm\",\n                                                        children: type.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-mono bg-gray-100 px-2 py-1 rounded\",\n                                                        children: type.code\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: type.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: \"* C=认知高, R=关系高, E=执行高, A=适应高\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: onLearnMore,\n                                    variant: \"outline\",\n                                    className: \"border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white\",\n                                    children: [\n                                        \"查看完整类型说明\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"ml-2 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"应用价值\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"OCTI理论在组织发展中的实际应用价值\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"精准诊断\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"科学识别组织能力现状，发现优势和短板\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"发展规划\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"基于类型特征制定个性化发展策略\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_Brain_Lightbulb_Network_Shield_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"持续改进\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"跟踪评估效果，支持组织持续优化\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n            lineNumber: 86,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!********************************************!*\
  !*** ./src/components/home/<USER>
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserGuide: () => (/* binding */ UserGuide)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,CheckCircle,Clock,Download,FileText,HelpCircle,Mail,MessageCircle,Phone,Play!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ UserGuide auto */ \n\n\n\n\n/**\n * 用户引导组件\n * 展示评估流程、常见问题和联系方式\n */ const UserGuide = ({ onStartAssessment, onContactSupport })=>{\n    // 评估流程步骤\n    const assessmentSteps = [\n        {\n            step: 1,\n            title: \"选择评估方案\",\n            description: \"根据组织规模和需求选择标准版或专业版\",\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            duration: \"1分钟\",\n            details: [\n                \"了解两种方案差异\",\n                \"选择适合的评估类型\",\n                \"确认评估范围\"\n            ]\n        },\n        {\n            step: 2,\n            title: \"完成问卷调研\",\n            description: \"回答60道专业设计的组织能力评估题目\",\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            duration: \"15-25分钟\",\n            details: [\n                \"四维能力全面覆盖\",\n                \"情景化问题设计\",\n                \"支持保存和继续\"\n            ]\n        },\n        {\n            step: 3,\n            title: \"智能分析处理\",\n            description: \"AI系统基于OCTI理论进行深度分析\",\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            duration: \"3-5分钟\",\n            details: [\n                \"多维度数据分析\",\n                \"组织类型识别\",\n                \"能力模型匹配\"\n            ]\n        },\n        {\n            step: 4,\n            title: \"获取专业报告\",\n            description: \"查看详细的分析报告和发展建议\",\n            icon: _barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            duration: \"随时查看\",\n            details: [\n                \"可视化能力雷达图\",\n                \"详细分析报告\",\n                \"PDF格式下载\"\n            ]\n        }\n    ];\n    // 常见问题\n    const faqs = [\n        {\n            question: \"评估需要多长时间？\",\n            answer: \"标准版评估大约需要15-20分钟完成问卷，专业版可能需要20-30分钟。系统支持保存进度，您可以随时暂停和继续。\"\n        },\n        {\n            question: \"评估结果的准确性如何？\",\n            answer: \"OCTI评估基于科学的组织行为学理论，经过大量实践验证。我们的AI分析系统结合了多种先进算法，确保结果的专业性和准确性。\"\n        },\n        {\n            question: \"数据安全如何保障？\",\n            answer: \"我们采用银行级别的数据加密技术，所有数据传输和存储都经过严格加密。您的评估数据仅用于生成报告，不会用于其他用途。\"\n        },\n        {\n            question: \"可以重复评估吗？\",\n            answer: \"可以。标准版用户可以重新购买评估，专业版用户享有6个月内2次免费复测机会，帮助您跟踪组织能力的改进效果。\"\n        },\n        {\n            question: \"如何理解评估报告？\",\n            answer: \"报告包含四维能力雷达图、组织类型分析、详细的能力评估和发展建议。专业版还提供专家解读和实施指导。\"\n        },\n        {\n            question: \"支持团队评估吗？\",\n            answer: \"支持。您可以邀请团队成员分别完成评估，我们提供团队版本的综合分析报告，帮助了解团队整体能力分布。\"\n        }\n    ];\n    // 客户案例\n    const testimonials = [\n        {\n            company: \"某科技创业公司\",\n            industry: \"互联网科技\",\n            size: \"50人\",\n            feedback: \"OCTI评估帮助我们清晰地认识到团队在执行力方面的短板，制定的改进计划让我们的项目交付效率提升了40%。\",\n            type: \"标准版\"\n        },\n        {\n            company: \"某制造企业\",\n            industry: \"智能制造\",\n            size: \"200人\",\n            feedback: \"专业版的深度分析让我们发现了组织结构中的关键问题，专家的指导帮助我们成功完成了组织变革。\",\n            type: \"专业版\"\n        },\n        {\n            company: \"某教育机构\",\n            industry: \"在线教育\",\n            size: \"80人\",\n            feedback: \"通过OCTI评估，我们识别出了自己的组织类型，并据此调整了管理策略，团队协作效率明显提升。\",\n            type: \"标准版\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"简单4步，完成专业评估\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"科学的评估流程，确保结果的专业性和准确性\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: assessmentSteps.map((step, index)=>{\n                                const IconComponent = step.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        index < assessmentSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-blue-300 to-transparent z-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 144,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"relative z-10 hover:shadow-lg transition-all duration-300 border-t-4 border-t-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    className: \"text-center pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"w-8 h-8 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute -top-2 -right-2 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                    children: step.step\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 151,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 149,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg font-bold text-gray-900 mb-2\",\n                                                            children: step.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                            className: \"text-sm text-gray-600 mb-3\",\n                                                            children: step.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center text-sm text-blue-600 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                step.duration\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: step.details.map((detail, detailIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    detail\n                                                                ]\n                                                            }, detailIndex, true, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                lineNumber: 169,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 167,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                    lineNumber: 166,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: onStartAssessment,\n                                size: \"lg\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n                                children: [\n                                    \"立即开始评估\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"ml-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"常见问题解答\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"解答您关于OCTI评估的疑问\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"hover:shadow-md transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-lg font-semibold text-gray-900 flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    faq.question\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: faq.answer\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl lg:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"客户成功案例\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"看看其他组织如何通过OCTI评估实现能力提升\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"hover:shadow-lg transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg font-bold text-gray-900\",\n                                                                children: testimonial.company\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                lineNumber: 239,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    testimonial.industry,\n                                                                    \" • \",\n                                                                    testimonial.size\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-xs font-semibold\",\n                                                        children: testimonial.type\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-gray-700 italic leading-relaxed\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.feedback,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"需要帮助？联系我们\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"我们的专业团队随时为您提供支持和指导\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"在线客服\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: \"工作日 9:00-18:00\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white\",\n                                            children: \"开始对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"电话咨询\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: \"400-888-0000\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-green-600 text-green-600 hover:bg-green-600 hover:text-white\",\n                                            children: \"立即拨打\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: \"邮件支持\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white\",\n                                            children: \"发送邮件\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: onContactSupport,\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl\",\n                                children: [\n                                    \"联系专业顾问\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_CheckCircle_Clock_Download_FileText_HelpCircle_Mail_MessageCircle_Phone_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"ml-2 w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/home/<USER>",\n        lineNumber: 124,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Button = ({ children, onClick, disabled = false, variant = \"default\", size = \"md\", className = \"\", type = \"button\" })=>{\n    const baseClasses = \"inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    const variantClasses = {\n        default: \"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500\",\n        outline: \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500\",\n        ghost: \"text-gray-700 hover:bg-gray-100 focus:ring-blue-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm\",\n        md: \"px-4 py-2 text-sm\",\n        lg: \"px-6 py-3 text-base\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled,\n        className: `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Button.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg border border-gray-200 shadow-sm ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardHeader = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `px-6 py-4 border-b border-gray-200 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardContent = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `px-6 py-4 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardTitle = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        className: `text-lg font-semibold text-gray-900 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardDescription = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: `text-sm text-gray-600 mt-1 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"72fd08dda53a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YzU3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcyZmQwOGRkYTUzYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"OCTI智能评估系统\",\n    description: \"基于OCTI四维八极理论的智能组织评估平台\",\n    keywords: [\n        \"OCTI\",\n        \"组织评估\",\n        \"智能评估\",\n        \"四维八极\"\n    ],\n    authors: [\n        {\n            name: \"OCTI Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"OCTI智能评估系统\",\n        description: \"基于OCTI四维八极理论的智能组织评估平台\",\n        type: \"website\",\n        locale: \"zh_CN\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} h-full antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"root\",\n                className: \"min-h-full\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();