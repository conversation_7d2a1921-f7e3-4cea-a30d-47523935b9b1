/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/assessment/page";
exports.ids = ["app/assessment/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassessment%2Fpage&page=%2Fassessment%2Fpage&appPaths=%2Fassessment%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassessment%2Fpage&page=%2Fassessment%2Fpage&appPaths=%2Fassessment%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'assessment',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/assessment/page.tsx */ \"(rsc)/./src/app/assessment/page.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/assessment/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/assessment/page\",\n        pathname: \"/assessment\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassessment%2Fpage&page=%2Fassessment%2Fpage&appPaths=%2Fassessment%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fassessment%2Fpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fassessment%2Fpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/assessment/page.tsx */ \"(ssr)/./src/app/assessment/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhcHBsZSUyRkRvY3VtZW50cyUyRjIuMSUyMEFJJTIwSm91cm5leSUyRkN1cnNvcl9wcm9qZWN0cyUyRm9jdGlfdGVzdCUyRnNyYyUyRmFwcCUyRmFzc2Vzc21lbnQlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY3RpLWFzc2Vzc21lbnQtc3lzdGVtLz9hYWQwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FwcGxlL0RvY3VtZW50cy8yLjEgQUkgSm91cm5leS9DdXJzb3JfcHJvamVjdHMvb2N0aV90ZXN0L3NyYy9hcHAvYXNzZXNzbWVudC9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fassessment%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/assessment/page.tsx":
/*!*************************************!*\
  !*** ./src/app/assessment/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AssessmentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_questionnaire_QuestionnaireLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/questionnaire/QuestionnaireLoader */ \"(ssr)/./src/components/questionnaire/QuestionnaireLoader.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AssessmentPage() {\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"selection\",\n        selectedVersion: null,\n        questionnaireConfig: null,\n        isLoading: false,\n        error: null\n    });\n    // 版本选择处理 - 简化为直接切换到问卷模式，让QuestionnaireLoader处理流式加载\n    const handleVersionSelect = async (version)=>{\n        setState((prev)=>({\n                ...prev,\n                selectedVersion: version,\n                mode: \"questionnaire\",\n                error: null,\n                isLoading: false\n            }));\n    };\n    // 问卷完成处理\n    const handleQuestionnaireComplete = async (responses)=>{\n        console.log(\"问卷完成:\", responses);\n        setState((prev)=>({\n                ...prev,\n                mode: \"completed\"\n            }));\n    };\n    // 重新开始\n    const handleRestart = ()=>{\n        setState({\n            mode: \"selection\",\n            selectedVersion: null,\n            questionnaireConfig: null,\n            isLoading: false,\n            error: null\n        });\n    };\n    // 渲染版本选择界面\n    const renderVersionSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-blue-50 to-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-12 max-w-6xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"开始OCTI评估\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8\",\n                                children: \"基于四维八极理论的组织能力智能诊断\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-500\",\n                                children: \"选择适合您组织的评估版本，开始深入了解您的组织能力特征\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"cursor-pointer hover:shadow-xl transition-all duration-300 border-2 hover:border-blue-500 hover:scale-105\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"text-center pb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                        children: \"标准版\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium\",\n                                                        children: \"推荐\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"\\xa599\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-base\",\n                                                children: \"基础组织能力诊断，适合初次评估的组织\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"60道专业评估题目\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 88,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"预计15-20分钟完成\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 95,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"四维能力分析\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 99,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"16种基础类型识别\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"能力雷达图\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 104,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-indigo-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"PDF报告下载\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-pink-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"6个月有效期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                className: \"w-full py-3 text-lg font-semibold\",\n                                                onClick: ()=>handleVersionSelect(\"standard\"),\n                                                disabled: state.isLoading,\n                                                children: state.isLoading && state.selectedVersion === \"standard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: \"生成中...\"\n                                                }, void 0, false) : \"选择标准版\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"cursor-pointer hover:shadow-xl transition-all duration-300 border-2 hover:border-purple-500 hover:scale-105 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-4 py-1 bg-purple-600 text-white text-sm rounded-full font-medium\",\n                                            children: \"推荐\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"text-center pb-4 pt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                        children: \"专业版\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 border border-purple-300 text-purple-700 text-sm rounded-full font-medium\",\n                                                        children: \"深度分析\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"\\xa5399\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                className: \"text-base\",\n                                                children: \"深度组织能力诊断，提供详细分析和专家指导\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"60题+定制题目\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"预计25-35分钟完成\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"16种+细分子类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"多源数据融合\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"详细行动计划\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-indigo-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"1小时专家咨询\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-pink-500 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"12个月有效期+2次免费复测\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                className: \"w-full py-3 text-lg font-semibold bg-purple-600 hover:bg-purple-700\",\n                                                onClick: ()=>handleVersionSelect(\"professional\"),\n                                                disabled: state.isLoading,\n                                                children: state.isLoading && state.selectedVersion === \"professional\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: \"生成中...\"\n                                                }, void 0, false) : \"选择专业版\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-900 mb-3\",\n                                        children: \"标准版适合\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2 text-blue-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 bg-blue-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"初次进行组织能力评估的企业\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 bg-blue-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"50人以下的小型组织\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 bg-blue-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"希望快速了解组织现状的团队\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 bg-blue-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"预算有限但需要专业评估的组织\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 rounded-lg p-6 border-l-4 border-purple-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-purple-900 mb-3\",\n                                        children: \"专业版适合\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2 text-purple-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 bg-purple-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"50人以上的中大型组织\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 bg-purple-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"需要制定详细改进计划的企业\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 bg-purple-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"希望获得专家指导的组织\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1.5 h-1.5 bg-purple-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"需要持续跟踪改进效果的团队\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 max-w-2xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-red-50 border border-red-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600\",\n                                    children: state.error\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"mt-2\",\n                                    onClick: ()=>setState((prev)=>({\n                                                ...prev,\n                                                error: null\n                                            })),\n                                    children: \"重试\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n            lineNumber: 57,\n            columnNumber: 5\n        }, this);\n    // 渲染问卷界面\n    const renderQuestionnaire = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_questionnaire_QuestionnaireLoader__WEBPACK_IMPORTED_MODULE_2__.QuestionnaireLoader, {\n                version: state.selectedVersion || \"standard\",\n                organizationId: \"零活实验室（环保公益组织）\",\n                onComplete: handleQuestionnaireComplete\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染完成界面\n    const renderCompleted = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-green-50 to-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8 max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-xl p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-10 h-10 text-green-500\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"评估完成！\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 mb-6\",\n                                    children: \"感谢您完成OCTI组织能力评估。我们正在基于四维八极理论分析您的回答，稍后将为您生成详细的评估报告。\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 rounded-lg p-4 mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-800 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"预计分析时间：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"3-5分钟\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 46\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"报告内容：\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"四维能力分析、组织类型识别、发展建议\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"w-full py-3 text-lg\",\n                                    children: \"查看评估报告\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleRestart,\n                                    variant: \"outline\",\n                                    className: \"w-full py-3\",\n                                    children: \"重新开始评估\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx\",\n            lineNumber: 271,\n            columnNumber: 5\n        }, this);\n    // 主渲染逻辑\n    switch(state.mode){\n        case \"selection\":\n            return renderVersionSelection();\n        case \"questionnaire\":\n            return renderQuestionnaire();\n        case \"completed\":\n            return renderCompleted();\n        default:\n            return renderVersionSelection();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/assessment/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/ProgressBar.tsx":
/*!******************************************************!*\
  !*** ./src/components/questionnaire/ProgressBar.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgressBar: () => (/* binding */ ProgressBar),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ProgressBar,default auto */ \n\nconst ProgressBar = ({ progress, className = \"\", showPercentage = true })=>{\n    const clampedProgress = Math.min(Math.max(progress, 0), 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `w-full ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"问卷进度\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    showPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            Math.round(clampedProgress),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2.5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out\",\n                    style: {\n                        width: `${clampedProgress}%`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProgressBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/ProgressBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionCounter.tsx":
/*!**********************************************************!*\
  !*** ./src/components/questionnaire/QuestionCounter.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionCounter: () => (/* binding */ QuestionCounter),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QuestionCounter,default auto */ \n\nconst dimensionNames = {\n    SF: \"S/F维度：战略聚焦度\",\n    IT: \"I/T维度：团队协同度\",\n    MV: \"M/V维度：价值导向度\",\n    AD: \"A/D维度：能力发展度\"\n};\nconst dimensionColors = {\n    SF: \"bg-blue-100 text-blue-800 border-blue-200\",\n    IT: \"bg-green-100 text-green-800 border-green-200\",\n    MV: \"bg-purple-100 text-purple-800 border-purple-200\",\n    AD: \"bg-orange-100 text-orange-800 border-orange-200\"\n};\nconst QuestionCounter = ({ current, total, dimension, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center justify-between ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `px-3 py-1 rounded-full text-sm font-medium border ${dimensionColors[dimension]}`,\n                        children: dimensionNames[dimension]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"第 \",\n                            current,\n                            \" 题，共 \",\n                            total,\n                            \" 题\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: [\n                            current,\n                            \"/\",\n                            total\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 bg-gray-200 rounded-full h-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-1.5 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${current / total * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionCounter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionCounter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionDisplay.tsx":
/*!**********************************************************!*\
  !*** ./src/components/questionnaire/QuestionDisplay.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionDisplay: () => (/* binding */ QuestionDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QuestionDisplay auto */ \n\n/**\n * 问题显示组件\n * 根据问题类型渲染不同的输入控件\n */ const QuestionDisplay = ({ question, value, onChange, error })=>{\n    // 渲染单选题\n    const renderSingleChoice = ()=>{\n        if (!question.options || question.options.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: \"错误：单选题缺少选项数据\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: question.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"radio\",\n                            name: question.id,\n                            value: option.value || option.text,\n                            checked: value === (option.value || option.text),\n                            onChange: (e)=>onChange(e.target.value),\n                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-900 flex-1\",\n                            children: option.text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, undefined),\n                        option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"(\",\n                                option.score,\n                                \"分)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, `${question.id}-option-${index}`, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染多选题\n    const renderMultipleChoice = ()=>{\n        if (!question.options || question.options.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: \"错误：多选题缺少选项数据\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined);\n        }\n        const selectedValues = Array.isArray(value) ? value : [];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: question.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"checkbox\",\n                            value: option.value || option.text,\n                            checked: selectedValues.includes(option.value || option.text),\n                            onChange: (e)=>{\n                                const optionValue = option.value || option.text;\n                                let newValues = [\n                                    ...selectedValues\n                                ];\n                                if (e.target.checked) {\n                                    if (!newValues.includes(optionValue)) {\n                                        newValues.push(optionValue);\n                                    }\n                                } else {\n                                    newValues = newValues.filter((v)=>v !== optionValue);\n                                }\n                                onChange(newValues);\n                            },\n                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-900 flex-1\",\n                            children: option.text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, undefined),\n                        option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"(\",\n                                option.score,\n                                \"分)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, `${question.id}-option-${index}`, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染李克特量表\n    const renderLikertScale = ()=>{\n        const scaleOptions = question.options || [\n            {\n                text: \"非常不同意\",\n                value: \"1\",\n                score: 1\n            },\n            {\n                text: \"不同意\",\n                value: \"2\",\n                score: 2\n            },\n            {\n                text: \"中立\",\n                value: \"3\",\n                score: 3\n            },\n            {\n                text: \"同意\",\n                value: \"4\",\n                score: 4\n            },\n            {\n                text: \"非常同意\",\n                value: \"5\",\n                score: 5\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-5 gap-2\",\n                children: scaleOptions.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex flex-col items-center space-y-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                name: question.id,\n                                value: option.value,\n                                checked: value === option.value,\n                                onChange: (e)=>onChange(e.target.value),\n                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-center text-gray-900\",\n                                children: option.text\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, undefined),\n                            option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: option.score\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, `${question.id}-scale-${index}`, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染文本输入\n    const renderTextInput = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n            value: value || \"\",\n            onChange: (e)=>onChange(e.target.value),\n            placeholder: \"请输入您的答案...\",\n            rows: 4,\n            className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 根据问题类型渲染对应的输入控件\n    const renderQuestionInput = ()=>{\n        switch(question.type){\n            case \"single_choice\":\n            case \"single-choice\":\n                return renderSingleChoice();\n            case \"multiple_choice\":\n            case \"multiple-choice\":\n                return renderMultipleChoice();\n            case \"likert_scale\":\n            case \"likert-scale\":\n                return renderLikertScale();\n            case \"text\":\n            case \"textarea\":\n                return renderTextInput();\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"不支持的问题类型:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" \",\n                                question.type\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-yellow-600 mt-2\",\n                            children: \"请联系开发人员添加对此问题类型的支持\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 text-xs text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"问题数据:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"mt-1 bg-white p-2 rounded border text-xs overflow-auto\",\n                                    children: JSON.stringify(question, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: question.text || question.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined),\n                    question.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: question.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, undefined),\n                    question.dimension && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                children: question.dimension\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined),\n                            question.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: \"*\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: renderQuestionInput()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionnaireLoader.tsx":
/*!**************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireLoader.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireLoader: () => (/* binding */ QuestionnaireLoader),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _QuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuestionnaireRenderer */ \"(ssr)/./src/components/questionnaire/QuestionnaireRenderer.tsx\");\n/* harmony import */ var _StreamQuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StreamQuestionnaireRenderer */ \"(ssr)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireLoader,default auto */ \n\n\n\n\n\n/**\n * 问卷加载器组件\n * 负责从API加载问卷配置并渲染问卷界面\n */ const QuestionnaireLoader = ({ version, organizationId, onComplete, onConfigLoaded })=>{\n    // 统一状态管理\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        config: null,\n        loading: true,\n        error: null,\n        retryCount: 0,\n        // 流式加载状态初始化\n        streamLoading: false,\n        loadedQuestions: [],\n        totalExpected: 60,\n        currentProgress: {\n            completed: 0,\n            total: 60,\n            percentage: 0\n        },\n        questionnaireId: null\n    });\n    /**\n   * 转换API数据为QuestionnaireConfig格式\n   */ const transformApiData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((apiData)=>{\n        const questionnaireConfig = {\n            version: apiData.version || version,\n            total_questions: apiData.questions?.length || 0,\n            dimensions: {\n                SF: {\n                    questions: []\n                },\n                IT: {\n                    questions: []\n                },\n                MV: {\n                    questions: []\n                },\n                AD: {\n                    questions: []\n                }\n            }\n        };\n        // 按维度分组问题\n        if (apiData.questions && Array.isArray(apiData.questions)) {\n            apiData.questions.forEach((q)=>{\n                // 处理维度名称映射\n                let dimensionKey;\n                switch(q.dimension){\n                    case \"S/F\":\n                        dimensionKey = \"SF\";\n                        break;\n                    case \"I/T\":\n                        dimensionKey = \"IT\";\n                        break;\n                    case \"M/V\":\n                        dimensionKey = \"MV\";\n                        break;\n                    case \"A/D\":\n                        dimensionKey = \"AD\";\n                        break;\n                    default:\n                        console.warn(`未知维度: ${q.dimension}，默认使用SF`);\n                        dimensionKey = \"SF\";\n                }\n                const configQuestion = {\n                    id: q.id,\n                    dimension: dimensionKey,\n                    sub_dimension: q.subdimension || \"\",\n                    type: q.type,\n                    text: q.title || q.text || \"\",\n                    options: q.options?.map((opt, index)=>({\n                            id: opt.id || `${q.id}_opt_${index}`,\n                            text: opt.text || opt,\n                            value: opt.value || opt,\n                            score: opt.score || index + 1\n                        })) || [],\n                    scoring: {\n                        dimension_weight: q.weight || 1,\n                        sub_dimension_weight: 1,\n                        option_scores: q.options?.map((_, index)=>index + 1) || [],\n                        reverse_scoring: false\n                    }\n                };\n                questionnaireConfig.dimensions[dimensionKey].questions.push(configQuestion);\n            });\n        }\n        return questionnaireConfig;\n    }, [\n        version\n    ]);\n    /**\n   * 流式加载问卷配置 - 新的核心方法\n   */ const loadQuestionnaireStreamly = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (state.retryCount >= 3) {\n            setState((prev)=>({\n                    ...prev,\n                    error: \"重试次数过多，请稍后再试\",\n                    loading: false,\n                    streamLoading: false\n                }));\n            return;\n        }\n        setState((prev)=>({\n                ...prev,\n                loading: true,\n                streamLoading: true,\n                error: null,\n                loadedQuestions: [],\n                currentProgress: {\n                    completed: 0,\n                    total: 60,\n                    percentage: 0\n                }\n            }));\n        try {\n            console.log(\"开始流式加载问卷配置:\", {\n                version,\n                organizationId\n            });\n            // 第一步：启动问卷生成，获取第一批题目\n            const firstBatchResponse = await fetch(\"/api/questionnaire/batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    organizationType: \"technology\",\n                    version,\n                    batchSize: 5\n                })\n            });\n            if (!firstBatchResponse.ok) {\n                throw new Error(`启动问卷生成失败: ${firstBatchResponse.status}`);\n            }\n            const firstBatch = await firstBatchResponse.json();\n            if (!firstBatch.success) {\n                throw new Error(firstBatch.error || \"启动问卷生成失败\");\n            }\n            console.log(\"收到第一批题目:\", firstBatch.data);\n            // 更新状态：显示第一批题目\n            setState((prev)=>({\n                    ...prev,\n                    loadedQuestions: firstBatch.data.questions,\n                    totalExpected: firstBatch.data.totalExpected,\n                    currentProgress: firstBatch.data.progress,\n                    questionnaireId: firstBatch.data.questionnaireId,\n                    loading: false // 第一批加载完成，可以开始显示\n                }));\n            // 第二步：继续加载剩余题目\n            await loadRemainingQuestions(firstBatch.data);\n        } catch (err) {\n            console.error(\"流式加载问卷配置失败:\", err);\n            setState((prev)=>({\n                    ...prev,\n                    error: err instanceof Error ? err.message : \"加载问卷配置失败\",\n                    loading: false,\n                    streamLoading: false,\n                    retryCount: prev.retryCount + 1\n                }));\n        }\n    }, [\n        version,\n        organizationId,\n        state.retryCount\n    ]);\n    /**\n   * 加载剩余题目\n   */ const loadRemainingQuestions = async (initialBatch)=>{\n        let nextBatch = initialBatch.nextBatch;\n        while(nextBatch && state.streamLoading){\n            try {\n                console.log(`加载下一批题目: ${nextBatch.dimension} 从 ${nextBatch.startIndex}`);\n                const response = await fetch(`/api/questionnaire/batch?questionnaireId=${initialBatch.questionnaireId}&dimension=${nextBatch.dimension}&startIndex=${nextBatch.startIndex}&batchSize=5`);\n                if (!response.ok) {\n                    console.warn(`批次加载失败: ${response.status}，跳过此批次`);\n                    break;\n                }\n                const batchData = await response.json();\n                if (!batchData.success) {\n                    console.warn(\"批次数据无效，跳过此批次\");\n                    break;\n                }\n                console.log(`收到批次数据:`, batchData.data);\n                // 累积添加新题目\n                setState((prev)=>({\n                        ...prev,\n                        loadedQuestions: [\n                            ...prev.loadedQuestions,\n                            ...batchData.data.questions\n                        ],\n                        currentProgress: batchData.data.progress\n                    }));\n                // 准备下一批\n                nextBatch = batchData.data.nextBatch;\n                // 如果没有下一批，说明全部完成\n                if (!nextBatch) {\n                    setState((prev)=>({\n                            ...prev,\n                            streamLoading: false\n                        }));\n                    console.log(\"所有题目加载完成!\");\n                    break;\n                }\n                // 短暂延迟，避免请求过于频繁\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            } catch (error) {\n                console.error(\"加载批次失败:\", error);\n                break;\n            }\n        }\n    };\n    /**\n   * 传统的完整加载方法（保留作为备用）\n   * 加载问卷配置\n   */ const loadQuestionnaireConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    loading: true,\n                    error: null\n                }));\n            console.log(\"开始加载问卷配置:\", {\n                version,\n                organizationId\n            });\n            // 创建AbortController用于超时控制\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 15 * 60 * 1000) // 15分钟超时\n            ;\n            try {\n                const response = await fetch(\"/api/questionnaire/generate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        version,\n                        organizationType: \"technology\",\n                        organizationId\n                    }),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    throw new Error(`生成问卷失败: ${response.status} ${response.statusText}`);\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    throw new Error(result.error?.message || \"生成问卷失败\");\n                }\n                console.log(\"API返回数据:\", result.data);\n                // 转换API数据\n                const questionnaireConfig = transformApiData(result.data);\n                setState((prev)=>({\n                        ...prev,\n                        config: questionnaireConfig,\n                        loading: false,\n                        retryCount: 0\n                    }));\n                // 通知父组件配置已加载\n                if (onConfigLoaded) {\n                    onConfigLoaded(questionnaireConfig);\n                }\n            } catch (error) {\n                clearTimeout(timeoutId);\n                if (error instanceof Error && error.name === \"AbortError\") {\n                    throw new Error(\"请求超时，问卷生成时间过长，请稍后重试\");\n                }\n                throw error;\n            }\n        } catch (err) {\n            console.error(\"加载问卷配置失败:\", err);\n            setState((prev)=>({\n                    ...prev,\n                    error: err instanceof Error ? err.message : \"加载问卷配置失败\",\n                    loading: false,\n                    retryCount: prev.retryCount + 1\n                }));\n        }\n    }, [\n        version,\n        organizationId,\n        onConfigLoaded,\n        transformApiData\n    ]);\n    /**\n   * 处理问卷完成\n   */ const handleQuestionnaireComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (responses)=>{\n        console.log(\"问卷完成，回答数量:\", responses.length);\n        try {\n            if (onComplete) {\n                await onComplete(responses);\n            }\n        } catch (error) {\n            console.error(\"处理问卷完成失败:\", error);\n        // 这里可以添加错误处理逻辑\n        }\n    }, [\n        onComplete\n    ]);\n    /**\n   * 重试加载\n   */ const handleRetry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (state.retryCount < 3) {\n            loadQuestionnaireConfig();\n        } else {\n            setState((prev)=>({\n                    ...prev,\n                    error: \"重试次数过多，请刷新页面重试\"\n                }));\n        }\n    }, [\n        loadQuestionnaireConfig,\n        state.retryCount\n    ]);\n    // 初始化加载 - 使用流式加载\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadQuestionnaireStreamly();\n    }, [\n        loadQuestionnaireStreamly\n    ]);\n    // 渲染初始加载状态（只在第一批题目加载前显示）\n    if (state.loading && state.loadedQuestions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 font-medium\",\n                                children: \"正在生成第一批题目...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400 mt-1\",\n                                children: [\n                                    \"版本: \",\n                                    version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, undefined),\n                            organizationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"组织ID: \",\n                                    organizationId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 397,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 396,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (state.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"max-w-md w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-500 text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: \"加载失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4 text-sm\",\n                            children: state.error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleRetry,\n                                    disabled: state.retryCount >= 3,\n                                    className: \"w-full\",\n                                    children: state.retryCount >= 3 ? \"重试次数已用完\" : `重新加载 (${state.retryCount}/3)`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, undefined),\n                                state.retryCount >= 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"w-full\",\n                                    children: \"刷新页面\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-xs text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"版本: \",\n                                        version\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, undefined),\n                                organizationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"组织ID: \",\n                                        organizationId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 34\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 416,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 415,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 414,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染流式加载状态 - 显示已加载的题目\n    if (state.loadedQuestions.length > 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: `border-2 ${state.streamLoading ? \"bg-blue-50 border-blue-200\" : \"bg-green-50 border-green-200\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `text-xl mr-3 ${state.streamLoading ? \"text-blue-500\" : \"text-green-500\"}`,\n                                    children: state.streamLoading ? \"\\uD83D\\uDD04\" : \"✅\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: `text-sm font-semibold ${state.streamLoading ? \"text-blue-900\" : \"text-green-900\"}`,\n                                            children: state.streamLoading ? \"问卷生成中...\" : \"问卷生成完成\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-sm ${state.streamLoading ? \"text-blue-700\" : \"text-green-700\"}`,\n                                            children: [\n                                                \"已生成 \",\n                                                state.loadedQuestions.length,\n                                                \" / \",\n                                                state.totalExpected,\n                                                \" 道题目\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        state.streamLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-blue-200 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: `${state.currentProgress.percentage}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: [\n                                                        state.currentProgress.percentage,\n                                                        \"% 完成\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right text-xs text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"版本: \",\n                                                version\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"已加载: \",\n                                                state.loadedQuestions.length,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamQuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_5__.StreamQuestionnaireRenderer, {\n                    questions: state.loadedQuestions,\n                    isLoading: state.streamLoading,\n                    progress: state.currentProgress,\n                    onAnswerChange: (questionId, answer)=>{\n                        console.log(\"答案变更:\", questionId, answer);\n                    },\n                    onComplete: (responses)=>{\n                        console.log(\"问卷完成:\", responses);\n                        if (handleQuestionnaireComplete) {\n                            // 转换为旧格式兼容\n                            const convertedResponses = responses.map((r)=>({\n                                    questionId: r.questionId,\n                                    answer: r.answer,\n                                    timestamp: r.timestamp\n                                }));\n                            handleQuestionnaireComplete(convertedResponses);\n                        }\n                    },\n                    className: \"bg-white\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 454,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 传统的完整加载成功状态（保留兼容性）\n    if (state.config) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_4__.QuestionnaireRenderer, {\n                config: state.config,\n                onComplete: handleQuestionnaireComplete,\n                className: \"bg-white\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 521,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 520,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\n// 默认导出以保持兼容性\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionnaireLoader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionnaireLoader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionnaireProgress.tsx":
/*!****************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireProgress.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireProgress: () => (/* binding */ QuestionnaireProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressBar */ \"(ssr)/./src/components/questionnaire/ProgressBar.tsx\");\n/* harmony import */ var _QuestionCounter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuestionCounter */ \"(ssr)/./src/components/questionnaire/QuestionCounter.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireProgress auto */ \n\n\n\n/**\n * 问卷进度组件\n * 显示当前进度和问题计数\n */ const QuestionnaireProgress = ({ current, total, progress, dimension = \"SF\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionCounter__WEBPACK_IMPORTED_MODULE_3__.QuestionCounter, {\n                current: current,\n                total: total,\n                dimension: dimension\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_2__.ProgressBar, {\n                progress: progress\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9xdWVzdGlvbm5haXJlL1F1ZXN0aW9ubmFpcmVQcm9ncmVzcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFeUI7QUFDa0I7QUFDUTtBQVNuRDs7O0NBR0MsR0FDTSxNQUFNRyx3QkFBOEQsQ0FBQyxFQUMxRUMsT0FBTyxFQUNQQyxLQUFLLEVBQ0xDLFFBQVEsRUFDUkMsWUFBWSxJQUFJLEVBQ2pCO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDUCw2REFBZUE7Z0JBQUNFLFNBQVNBO2dCQUFTQyxPQUFPQTtnQkFBT0UsV0FBV0E7Ozs7OzswQkFDNUQsOERBQUNOLHFEQUFXQTtnQkFBQ0ssVUFBVUE7Ozs7Ozs7Ozs7OztBQUc3QixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9jb21wb25lbnRzL3F1ZXN0aW9ubmFpcmUvUXVlc3Rpb25uYWlyZVByb2dyZXNzLnRzeD8wYjM5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBQcm9ncmVzc0JhciB9IGZyb20gJy4vUHJvZ3Jlc3NCYXInXG5pbXBvcnQgeyBRdWVzdGlvbkNvdW50ZXIgfSBmcm9tICcuL1F1ZXN0aW9uQ291bnRlcidcblxuaW50ZXJmYWNlIFF1ZXN0aW9ubmFpcmVQcm9ncmVzc1Byb3BzIHtcbiAgY3VycmVudDogbnVtYmVyXG4gIHRvdGFsOiBudW1iZXJcbiAgcHJvZ3Jlc3M6IG51bWJlclxuICBkaW1lbnNpb24/OiAnU0YnIHwgJ0lUJyB8ICdNVicgfCAnQUQnXG59XG5cbi8qKlxuICog6Zeu5Y236L+b5bqm57uE5Lu2XG4gKiDmmL7npLrlvZPliY3ov5vluqblkozpl67popjorqHmlbBcbiAqL1xuZXhwb3J0IGNvbnN0IFF1ZXN0aW9ubmFpcmVQcm9ncmVzczogUmVhY3QuRkM8UXVlc3Rpb25uYWlyZVByb2dyZXNzUHJvcHM+ID0gKHtcbiAgY3VycmVudCxcbiAgdG90YWwsXG4gIHByb2dyZXNzLFxuICBkaW1lbnNpb24gPSAnU0YnXG59KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgIDxRdWVzdGlvbkNvdW50ZXIgY3VycmVudD17Y3VycmVudH0gdG90YWw9e3RvdGFsfSBkaW1lbnNpb249e2RpbWVuc2lvbn0gLz5cbiAgICAgIDxQcm9ncmVzc0JhciBwcm9ncmVzcz17cHJvZ3Jlc3N9IC8+XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJQcm9ncmVzc0JhciIsIlF1ZXN0aW9uQ291bnRlciIsIlF1ZXN0aW9ubmFpcmVQcm9ncmVzcyIsImN1cnJlbnQiLCJ0b3RhbCIsInByb2dyZXNzIiwiZGltZW5zaW9uIiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionnaireProgress.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/QuestionnaireRenderer.tsx":
/*!****************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireRenderer.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireRenderer: () => (/* binding */ QuestionnaireRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useQuestionnaire__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useQuestionnaire */ \"(ssr)/./src/hooks/useQuestionnaire.ts\");\n/* harmony import */ var _QuestionDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuestionDisplay */ \"(ssr)/./src/components/questionnaire/QuestionDisplay.tsx\");\n/* harmony import */ var _QuestionnaireProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuestionnaireProgress */ \"(ssr)/./src/components/questionnaire/QuestionnaireProgress.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Loading */ \"(ssr)/./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireRenderer auto */ \n\n\n\n\n\n\n\n/**\n * 问卷渲染器主组件\n * 负责协调各个子组件，保持简洁\n */ const QuestionnaireRenderer = ({ config, initialResponses = [], onComplete, onSave, className })=>{\n    const { currentQuestion, currentQuestionIndex, responses, errors, isLoading, isFirst, isLast, handleNext, handlePrevious, updateResponse, progress } = (0,_hooks_useQuestionnaire__WEBPACK_IMPORTED_MODULE_2__.useQuestionnaire)({\n        config,\n        initialResponses,\n        onComplete,\n        onSave\n    });\n    // 获取当前问题的回答值\n    const getCurrentValue = ()=>{\n        if (!currentQuestion) return undefined;\n        const response = responses.find((r)=>r.questionId === currentQuestion.id);\n        return response?.answer;\n    };\n    // 处理答案更新\n    const handleAnswerChange = (value)=>{\n        if (currentQuestion) {\n            updateResponse(currentQuestion.id, value);\n        }\n    };\n    // 获取当前问题的维度\n    const getCurrentDimension = ()=>{\n        return currentQuestion?.dimension || \"SF\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_6__.Loading, {\n                size: \"lg\",\n                text: \"正在提交问卷...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionnaireProgress__WEBPACK_IMPORTED_MODULE_4__.QuestionnaireProgress, {\n                current: currentQuestionIndex + 1,\n                total: config.total_questions,\n                progress: progress,\n                dimension: getCurrentDimension()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6 mt-4\",\n                children: [\n                    currentQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionDisplay__WEBPACK_IMPORTED_MODULE_3__.QuestionDisplay, {\n                        question: currentQuestion,\n                        value: getCurrentValue(),\n                        onChange: handleAnswerChange,\n                        error: errors[currentQuestion.id]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: \"问题加载中...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8 pt-6 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrevious,\n                                disabled: isFirst || isLoading,\n                                className: \"px-6 py-2\",\n                                children: \"上一题\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"第 \",\n                                    currentQuestionIndex + 1,\n                                    \" 题，共 \",\n                                    config.total_questions,\n                                    \" 题\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: handleNext,\n                                disabled: isLoading,\n                                className: \"px-6 py-2\",\n                                children: isLast ? \"提交问卷\" : \"下一题\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    currentQuestion && errors[currentQuestion.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-red-500\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: errors[currentQuestion.id]\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/QuestionnaireRenderer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/questionnaire/StreamQuestionnaireRenderer.tsx ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamQuestionnaireRenderer: () => (/* binding */ StreamQuestionnaireRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamQuestionnaireRenderer auto */ \n\n\n\n/**\n * 流式问卷渲染器\n * 支持动态加载和显示题目\n */ const StreamQuestionnaireRenderer = ({ questions, isLoading, progress, onAnswerChange, onComplete, className = \"\" })=>{\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    /**\n   * 处理答案变更\n   */ const handleAnswerChange = (questionId, answer)=>{\n        const newAnswers = {\n            ...answers,\n            [questionId]: answer\n        };\n        setAnswers(newAnswers);\n        if (onAnswerChange) {\n            onAnswerChange(questionId, answer);\n        }\n    };\n    /**\n   * 下一题\n   */ const handleNext = ()=>{\n        if (currentQuestionIndex < questions.length - 1) {\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n        }\n    };\n    /**\n   * 上一题\n   */ const handlePrevious = ()=>{\n        if (currentQuestionIndex > 0) {\n            setCurrentQuestionIndex(currentQuestionIndex - 1);\n        }\n    };\n    /**\n   * 提交问卷\n   */ const handleSubmit = ()=>{\n        const responses = Object.entries(answers).map(([questionId, answer])=>({\n                questionId,\n                answer,\n                timestamp: new Date()\n            }));\n        if (onComplete) {\n            onComplete(responses);\n        }\n    };\n    // 如果没有题目，显示等待状态\n    if (questions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: `p-8 text-center ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"正在生成第一批题目...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentQuestion = questions[currentQuestionIndex];\n    const isLastQuestion = currentQuestionIndex === questions.length - 1;\n    const canProceed = answers[currentQuestion?.id];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"题目进度\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    currentQuestionIndex + 1,\n                                    \" / \",\n                                    questions.length,\n                                    isLoading && ` (共${progress.total}题)`\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${(currentQuestionIndex + 1) / Math.max(questions.length, progress.total) * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-blue-600 mt-1\",\n                        children: [\n                            \"正在生成更多题目... (\",\n                            progress.percentage,\n                            \"% 完成)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-600 font-medium\",\n                                            children: [\n                                                currentQuestion.dimension,\n                                                \" 维度\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"第 \",\n                                                currentQuestion.order,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 leading-relaxed\",\n                                    children: currentQuestion.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQuestion.options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            name: currentQuestion.id,\n                                            value: option.score,\n                                            checked: answers[currentQuestion.id] === option.score,\n                                            onChange: ()=>handleAnswerChange(currentQuestion.id, option.score),\n                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-gray-700 flex-1\",\n                                            children: option.text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                option.score,\n                                                \"分\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, option.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePrevious,\n                                    disabled: currentQuestionIndex === 0,\n                                    variant: \"outline\",\n                                    children: \"上一题\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: !isLastQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleNext,\n                                        disabled: !canProceed,\n                                        children: \"下一题\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleSubmit,\n                                        disabled: !canProceed || isLoading,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: isLoading ? \"等待更多题目...\" : \"提交问卷\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, undefined),\n            Object.keys(answers).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: [\n                            \"已回答题目 (\",\n                            Object.keys(answers).length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-10 gap-2\",\n                        children: questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentQuestionIndex(index),\n                                className: `\n                  w-8 h-8 rounded text-xs font-medium transition-colors\n                  ${answers[question.id] ? \"bg-green-100 text-green-700 border border-green-300\" : \"bg-gray-100 text-gray-500 border border-gray-300\"}\n                  ${index === currentQuestionIndex ? \"ring-2 ring-blue-500\" : \"hover:bg-gray-200\"}\n                `,\n                                children: index + 1\n                            }, question.id, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Button = ({ children, onClick, disabled = false, variant = \"default\", size = \"md\", className = \"\", type = \"button\" })=>{\n    const baseClasses = \"inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    const variantClasses = {\n        default: \"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500\",\n        outline: \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500\",\n        ghost: \"text-gray-700 hover:bg-gray-100 focus:ring-blue-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm\",\n        md: \"px-4 py-2 text-sm\",\n        lg: \"px-6 py-3 text-base\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled,\n        className: `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Button.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg border border-gray-200 shadow-sm ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardHeader = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `px-6 py-4 border-b border-gray-200 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardContent = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `px-6 py-4 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardTitle = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        className: `text-lg font-semibold text-gray-900 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardDescription = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: `text-sm text-gray-600 mt-1 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXlCO0FBT2xCLE1BQU1DLE9BQTRCLENBQUMsRUFBRUMsUUFBUSxFQUFFQyxZQUFZLEVBQUUsRUFBRTtJQUNwRSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVyxDQUFDLHFEQUFxRCxFQUFFQSxVQUFVLENBQUM7a0JBQ2hGRDs7Ozs7O0FBR1AsRUFBQztBQUVNLE1BQU1HLGFBQWtDLENBQUMsRUFBRUgsUUFBUSxFQUFFQyxZQUFZLEVBQUUsRUFBRTtJQUMxRSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVyxDQUFDLG1DQUFtQyxFQUFFQSxVQUFVLENBQUM7a0JBQzlERDs7Ozs7O0FBR1AsRUFBQztBQUVNLE1BQU1JLGNBQW1DLENBQUMsRUFBRUosUUFBUSxFQUFFQyxZQUFZLEVBQUUsRUFBRTtJQUMzRSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVyxDQUFDLFVBQVUsRUFBRUEsVUFBVSxDQUFDO2tCQUNyQ0Q7Ozs7OztBQUdQLEVBQUM7QUFFTSxNQUFNSyxZQUFpQyxDQUFDLEVBQUVMLFFBQVEsRUFBRUMsWUFBWSxFQUFFLEVBQUU7SUFDekUscUJBQ0UsOERBQUNLO1FBQUdMLFdBQVcsQ0FBQyxvQ0FBb0MsRUFBRUEsVUFBVSxDQUFDO2tCQUM5REQ7Ozs7OztBQUdQLEVBQUM7QUFFTSxNQUFNTyxrQkFBdUMsQ0FBQyxFQUFFUCxRQUFRLEVBQUVDLFlBQVksRUFBRSxFQUFFO0lBQy9FLHFCQUNFLDhEQUFDTztRQUFFUCxXQUFXLENBQUMsMkJBQTJCLEVBQUVBLFVBQVUsQ0FBQztrQkFDcEREOzs7Ozs7QUFHUCxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9jb21wb25lbnRzL3VpL0NhcmQudHN4PzhkZDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgQ2FyZFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGNvbnN0IENhcmQ6IFJlYWN0LkZDPENhcmRQcm9wcz4gPSAoeyBjaGlsZHJlbiwgY2xhc3NOYW1lID0gJycgfSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHNoYWRvdy1zbSAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgY29uc3QgQ2FyZEhlYWRlcjogUmVhY3QuRkM8Q2FyZFByb3BzPiA9ICh7IGNoaWxkcmVuLCBjbGFzc05hbWUgPSAnJyB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BweC02IHB5LTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwICR7Y2xhc3NOYW1lfWB9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCBjb25zdCBDYXJkQ29udGVudDogUmVhY3QuRkM8Q2FyZFByb3BzPiA9ICh7IGNoaWxkcmVuLCBjbGFzc05hbWUgPSAnJyB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BweC02IHB5LTQgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGNvbnN0IENhcmRUaXRsZTogUmVhY3QuRkM8Q2FyZFByb3BzPiA9ICh7IGNoaWxkcmVuLCBjbGFzc05hbWUgPSAnJyB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGgzIGNsYXNzTmFtZT17YHRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwICR7Y2xhc3NOYW1lfWB9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvaDM+XG4gIClcbn1cblxuZXhwb3J0IGNvbnN0IENhcmREZXNjcmlwdGlvbjogUmVhY3QuRkM8Q2FyZFByb3BzPiA9ICh7IGNoaWxkcmVuLCBjbGFzc05hbWUgPSAnJyB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSB0ZXh0LWdyYXktNjAwIG10LTEgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9wPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDYXJkIiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJkaXYiLCJDYXJkSGVhZGVyIiwiQ2FyZENvbnRlbnQiLCJDYXJkVGl0bGUiLCJoMyIsIkNhcmREZXNjcmlwdGlvbiIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Loading.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/Loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loading: () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Loading = ({ size = \"md\", text = \"加载中...\", className = \"\" })=>{\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        md: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col items-center justify-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `animate-spin rounded-full border-b-2 border-blue-600 ${sizeClasses[size]} mb-2`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 text-sm\",\n                children: text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n                lineNumber: 23,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBeUI7QUFRbEIsTUFBTUMsVUFBa0MsQ0FBQyxFQUM5Q0MsT0FBTyxJQUFJLEVBQ1hDLE9BQU8sUUFBUSxFQUNmQyxZQUFZLEVBQUUsRUFDZjtJQUNDLE1BQU1DLGNBQWM7UUFDbEJDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUwsV0FBVyxDQUFDLDBDQUEwQyxFQUFFQSxVQUFVLENBQUM7OzBCQUN0RSw4REFBQ0s7Z0JBQUlMLFdBQVcsQ0FBQyxxREFBcUQsRUFBRUMsV0FBVyxDQUFDSCxLQUFLLENBQUMsS0FBSyxDQUFDOzs7Ozs7WUFDL0ZDLHNCQUFRLDhEQUFDTztnQkFBRU4sV0FBVTswQkFBeUJEOzs7Ozs7Ozs7Ozs7QUFHckQsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL29jdGktYXNzZXNzbWVudC1zeXN0ZW0vLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nLnRzeD9kZmQyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIExvYWRpbmdQcm9wcyB7XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZydcbiAgdGV4dD86IHN0cmluZ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGNvbnN0IExvYWRpbmc6IFJlYWN0LkZDPExvYWRpbmdQcm9wcz4gPSAoeyBcbiAgc2l6ZSA9ICdtZCcsIFxuICB0ZXh0ID0gJ+WKoOi9veS4rS4uLicsIFxuICBjbGFzc05hbWUgPSAnJyBcbn0pID0+IHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206ICdoLTQgdy00JyxcbiAgICBtZDogJ2gtOCB3LTgnLCBcbiAgICBsZzogJ2gtMTIgdy0xMidcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciAke2NsYXNzTmFtZX1gfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCAke3NpemVDbGFzc2VzW3NpemVdfSBtYi0yYH0+PC9kaXY+XG4gICAgICB7dGV4dCAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc21cIj57dGV4dH08L3A+fVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMb2FkaW5nIiwic2l6ZSIsInRleHQiLCJjbGFzc05hbWUiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsImRpdiIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Button = ({ children, onClick, disabled = false, variant = \"default\", size = \"md\", className = \"\", type = \"button\" })=>{\n    const baseClasses = \"inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    const variantClasses = {\n        default: \"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500\",\n        outline: \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500\",\n        ghost: \"text-gray-700 hover:bg-gray-100 focus:ring-blue-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm\",\n        md: \"px-4 py-2 text-sm\",\n        lg: \"px-6 py-3 text-base\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled,\n        className: `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/button.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg border border-gray-200 shadow-sm ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/card.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardHeader = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `px-6 py-4 border-b border-gray-200 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/card.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardContent = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `px-6 py-4 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/card.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardTitle = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        className: `text-lg font-semibold text-gray-900 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/card.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\nconst CardDescription = ({ children, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: `text-sm text-gray-600 mt-1 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useQuestionnaire.ts":
/*!***************************************!*\
  !*** ./src/hooks/useQuestionnaire.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuestionnaire: () => (/* binding */ useQuestionnaire)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useQuestionnaire auto */ \n/**\n * 问卷逻辑管理Hook\n * 将复杂的状态管理逻辑从组件中抽离\n */ function useQuestionnaire({ config, initialResponses, onComplete, onSave }) {\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [responses, setResponses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // 从QuestionnaireConfig中提取所有问题\n    const allQuestions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const questions = [];\n        Object.values(config.dimensions).forEach((dimension)=>{\n            questions.push(...dimension.questions);\n        });\n        return questions;\n    }, [\n        config\n    ]);\n    // 计算派生状态\n    const currentQuestion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>allQuestions[currentQuestionIndex], [\n        allQuestions,\n        currentQuestionIndex\n    ]);\n    const isFirst = currentQuestionIndex === 0;\n    const isLast = currentQuestionIndex === allQuestions.length - 1;\n    const progress = (currentQuestionIndex + 1) / allQuestions.length * 100;\n    // 更新回答 - 修复参数类型\n    const updateResponse = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((questionId, answer)=>{\n        setResponses((prev)=>{\n            const existing = prev.find((r)=>r.questionId === questionId);\n            if (existing) {\n                return prev.map((r)=>r.questionId === questionId ? {\n                        ...r,\n                        answer\n                    } : r);\n            }\n            return [\n                ...prev,\n                {\n                    questionId,\n                    answer\n                }\n            ];\n        });\n        // 清除错误\n        setErrors((prev)=>({\n                ...prev,\n                [questionId]: \"\"\n            }));\n    }, []);\n    // 验证当前问题\n    const validateCurrentQuestion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!currentQuestion) return true;\n        const response = responses.find((r)=>r.questionId === currentQuestion.id);\n        if (!response || response.answer === undefined || response.answer === null || response.answer === \"\") {\n            setErrors((prev)=>({\n                    ...prev,\n                    [currentQuestion.id]: \"请回答此问题后再继续\"\n                }));\n            return false;\n        }\n        return true;\n    }, [\n        currentQuestion,\n        responses\n    ]);\n    // 下一题\n    const handleNext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!validateCurrentQuestion()) return;\n        if (isLast) {\n            setIsLoading(true);\n            try {\n                // 转换为QuestionnaireResponse格式\n                const questionnaireResponse = {\n                    id: `response_${Date.now()}`,\n                    questionnaireId: \"questionnaire_1\",\n                    responses: responses.map((r)=>{\n                        // 找到对应的问题以获取dimension\n                        const question = allQuestions.find((q)=>q.id === r.questionId);\n                        return {\n                            id: `resp_${r.questionId}`,\n                            assessmentId: \"assessment_1\",\n                            questionId: r.questionId,\n                            respondentId: \"user_1\",\n                            answer: r.answer,\n                            dimension: question?.dimension || \"SF\",\n                            createdAt: new Date(),\n                            updatedAt: new Date()\n                        };\n                    }),\n                    metadata: {\n                        startTime: new Date().toISOString(),\n                        endTime: new Date().toISOString(),\n                        totalTimeSpent: 0,\n                        submittedAt: new Date().toISOString(),\n                        version: config.version,\n                        completionRate: 100\n                    },\n                    status: \"completed\",\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                await onComplete([\n                    questionnaireResponse\n                ]);\n            } catch (error) {\n                console.error(\"提交问卷失败:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        } else {\n            setCurrentQuestionIndex((prev)=>prev + 1);\n        }\n    }, [\n        validateCurrentQuestion,\n        isLast,\n        responses,\n        onComplete\n    ]);\n    // 上一题\n    const handlePrevious = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isFirst) {\n            setCurrentQuestionIndex((prev)=>prev - 1);\n        }\n    }, [\n        isFirst\n    ]);\n    return {\n        currentQuestion,\n        currentQuestionIndex,\n        responses,\n        errors,\n        isLoading,\n        isFirst,\n        isLast,\n        progress,\n        handleNext,\n        handlePrevious,\n        updateResponse\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useQuestionnaire.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"72fd08dda53a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2N0aS1hc3Nlc3NtZW50LXN5c3RlbS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YzU3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcyZmQwOGRkYTUzYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/assessment/page.tsx":
/*!*************************************!*\
  !*** ./src/app/assessment/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/assessment/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"OCTI智能评估系统\",\n    description: \"基于OCTI四维八极理论的智能组织评估平台\",\n    keywords: [\n        \"OCTI\",\n        \"组织评估\",\n        \"智能评估\",\n        \"四维八极\"\n    ],\n    authors: [\n        {\n            name: \"OCTI Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"OCTI智能评估系统\",\n        description: \"基于OCTI四维八极理论的智能组织评估平台\",\n        type: \"website\",\n        locale: \"zh_CN\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} h-full antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"root\",\n                className: \"min-h-full\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassessment%2Fpage&page=%2Fassessment%2Fpage&appPaths=%2Fassessment%2Fpage&pagePath=private-next-app-dir%2Fassessment%2Fpage.tsx&appDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();