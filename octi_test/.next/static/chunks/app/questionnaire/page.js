/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/questionnaire/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fquestionnaire%2Fpage.tsx&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fquestionnaire%2Fpage.tsx&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/questionnaire/page.tsx */ \"(app-pages-browser)/./src/app/questionnaire/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lMkZVc2VycyUyRmFwcGxlJTJGRG9jdW1lbnRzJTJGMi4xJTIwQUklMjBKb3VybmV5JTJGQ3Vyc29yX3Byb2plY3RzJTJGb2N0aV90ZXN0JTJGc3JjJTJGYXBwJTJGcXVlc3Rpb25uYWlyZSUyRnBhZ2UudHN4JnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9kYzljIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FwcGxlL0RvY3VtZW50cy8yLjEgQUkgSm91cm5leS9DdXJzb3JfcHJvamVjdHMvb2N0aV90ZXN0L3NyYy9hcHAvcXVlc3Rpb25uYWlyZS9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fquestionnaire%2Fpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/questionnaire/page.tsx":
/*!****************************************!*\
  !*** ./src/app/questionnaire/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QuestionnairePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_questionnaire_QuestionnaireLoader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/questionnaire/QuestionnaireLoader */ \"(app-pages-browser)/./src/components/questionnaire/QuestionnaireLoader.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction QuestionnairePage() {\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"selection\",\n        selectedVersion: null,\n        questionnaireConfig: null,\n        isLoading: false,\n        error: null\n    });\n    // 版本选择处理 - 简化为直接切换到问卷模式，让QuestionnaireLoader处理流式加载\n    const handleVersionSelect = async (version)=>{\n        setState((prev)=>({\n                ...prev,\n                selectedVersion: version,\n                mode: \"questionnaire\",\n                error: null,\n                isLoading: false\n            }));\n    };\n    // 问卷完成处理\n    const handleQuestionnaireComplete = async (responses)=>{\n        console.log(\"问卷完成:\", responses);\n        setState((prev)=>({\n                ...prev,\n                mode: \"completed\"\n            }));\n    };\n    // 重新开始\n    const handleRestart = ()=>{\n        setState({\n            mode: \"selection\",\n            selectedVersion: null,\n            questionnaireConfig: null,\n            isLoading: false,\n            error: null\n        });\n    };\n    // 渲染版本选择界面\n    const renderVersionSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: \"OCTI 组织文化评估问卷\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-8\",\n                            children: \"选择适合您组织的评估版本，开始深入了解您的组织文化特征\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: \"标准版\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\",\n                                                    children: \"推荐\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"适合大多数组织的基础评估，快速了解组织文化现状\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"约 40 道题目\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"预计 15-20 分钟\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"适合中小型团队\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"基础分析报告\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full mt-6\",\n                                            onClick: ()=>handleVersionSelect(\"standard\"),\n                                            disabled: state.isLoading,\n                                            children: state.isLoading && state.selectedVersion === \"standard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: \"生成中...\"\n                                            }, void 0, false) : \"选择标准版\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-purple-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: \"专业版\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 border border-gray-300 text-gray-700 text-xs rounded-full\",\n                                                    children: \"深度分析\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"深度评估组织文化，提供详细的分析和改进建议\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"约 60 道题目\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"预计 25-35 分钟\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"适合大型组织\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-orange-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"详细分析报告\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"w-full mt-6\",\n                                            variant: \"outline\",\n                                            onClick: ()=>handleVersionSelect(\"professional\"),\n                                            disabled: state.isLoading,\n                                            children: state.isLoading && state.selectedVersion === \"professional\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: \"生成中...\"\n                                            }, void 0, false) : \"选择专业版\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 7\n                }, this),\n                state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: state.error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"mt-2\",\n                            onClick: ()=>setState((prev)=>({\n                                        ...prev,\n                                        error: null\n                                    })),\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n            lineNumber: 57,\n            columnNumber: 5\n        }, this);\n    // 渲染问卷界面\n    const renderQuestionnaire = ()=>{\n        if (!state.questionnaireConfig) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_questionnaire_QuestionnaireLoader__WEBPACK_IMPORTED_MODULE_2__.QuestionnaireLoader, {\n                version: state.selectedVersion || \"standard\",\n                organizationId: \"demo-org\",\n                onComplete: handleQuestionnaireComplete\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染完成界面\n    const renderCompleted = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 max-w-2xl text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-green-500\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M5 13l4 4L19 7\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                children: \"问卷完成！\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"感谢您完成 OCTI 组织文化评估问卷。我们正在分析您的回答，稍后将为您生成详细的评估报告。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleRestart,\n                                variant: \"outline\",\n                                className: \"w-full\",\n                                children: \"重新开始评估\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"w-full\",\n                                children: \"查看评估报告\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/questionnaire/page.tsx\",\n            lineNumber: 191,\n            columnNumber: 5\n        }, this);\n    // 主渲染逻辑\n    switch(state.mode){\n        case \"selection\":\n            return renderVersionSelection();\n        case \"questionnaire\":\n            return renderQuestionnaire();\n        case \"completed\":\n            return renderCompleted();\n        default:\n            return renderVersionSelection();\n    }\n}\n_s(QuestionnairePage, \"qIXU70ZgNLqepfuLOgHFAXSPfyQ=\");\n_c = QuestionnairePage;\nvar _c;\n$RefreshReg$(_c, \"QuestionnairePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/questionnaire/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/questionnaire/ProgressBar.tsx":
/*!******************************************************!*\
  !*** ./src/components/questionnaire/ProgressBar.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgressBar: function() { return /* binding */ ProgressBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ProgressBar,default auto */ \n\nconst ProgressBar = (param)=>{\n    let { progress, className = \"\", showPercentage = true } = param;\n    const clampedProgress = Math.min(Math.max(progress, 0), 100);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"问卷进度\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    showPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            Math.round(clampedProgress),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2.5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out\",\n                    style: {\n                        width: \"\".concat(clampedProgress, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/ProgressBar.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ProgressBar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProgressBar);\nvar _c;\n$RefreshReg$(_c, \"ProgressBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/ProgressBar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/questionnaire/QuestionCounter.tsx":
/*!**********************************************************!*\
  !*** ./src/components/questionnaire/QuestionCounter.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionCounter: function() { return /* binding */ QuestionCounter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QuestionCounter,default auto */ \n\nconst dimensionNames = {\n    SF: \"S/F维度：战略聚焦度\",\n    IT: \"I/T维度：团队协同度\",\n    MV: \"M/V维度：价值导向度\",\n    AD: \"A/D维度：能力发展度\"\n};\nconst dimensionColors = {\n    SF: \"bg-blue-100 text-blue-800 border-blue-200\",\n    IT: \"bg-green-100 text-green-800 border-green-200\",\n    MV: \"bg-purple-100 text-purple-800 border-purple-200\",\n    AD: \"bg-orange-100 text-orange-800 border-orange-200\"\n};\nconst QuestionCounter = (param)=>{\n    let { current, total, dimension, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-1 rounded-full text-sm font-medium border \".concat(dimensionColors[dimension]),\n                        children: dimensionNames[dimension]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"第 \",\n                            current,\n                            \" 题，共 \",\n                            total,\n                            \" 题\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: [\n                            current,\n                            \"/\",\n                            total\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 bg-gray-200 rounded-full h-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-1.5 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(current / total * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionCounter.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n_c = QuestionCounter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (QuestionCounter);\nvar _c;\n$RefreshReg$(_c, \"QuestionCounter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/QuestionCounter.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/questionnaire/QuestionDisplay.tsx":
/*!**********************************************************!*\
  !*** ./src/components/questionnaire/QuestionDisplay.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionDisplay: function() { return /* binding */ QuestionDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QuestionDisplay auto */ \n\n/**\n * 问题显示组件\n * 根据问题类型渲染不同的输入控件\n */ const QuestionDisplay = (param)=>{\n    let { question, value, onChange, error } = param;\n    // 渲染单选题\n    const renderSingleChoice = ()=>{\n        if (!question.options || question.options.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: \"错误：单选题缺少选项数据\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: question.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"radio\",\n                            name: question.id,\n                            value: option.value || option.text,\n                            checked: value === (option.value || option.text),\n                            onChange: (e)=>onChange(e.target.value),\n                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-900 flex-1\",\n                            children: option.text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, undefined),\n                        option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"(\",\n                                option.score,\n                                \"分)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, \"\".concat(question.id, \"-option-\").concat(index), true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染多选题\n    const renderMultipleChoice = ()=>{\n        if (!question.options || question.options.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: \"错误：多选题缺少选项数据\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined);\n        }\n        const selectedValues = Array.isArray(value) ? value : [];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: question.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"checkbox\",\n                            value: option.value || option.text,\n                            checked: selectedValues.includes(option.value || option.text),\n                            onChange: (e)=>{\n                                const optionValue = option.value || option.text;\n                                let newValues = [\n                                    ...selectedValues\n                                ];\n                                if (e.target.checked) {\n                                    if (!newValues.includes(optionValue)) {\n                                        newValues.push(optionValue);\n                                    }\n                                } else {\n                                    newValues = newValues.filter((v)=>v !== optionValue);\n                                }\n                                onChange(newValues);\n                            },\n                            className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-900 flex-1\",\n                            children: option.text\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, undefined),\n                        option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                \"(\",\n                                option.score,\n                                \"分)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, \"\".concat(question.id, \"-option-\").concat(index), true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染李克特量表\n    const renderLikertScale = ()=>{\n        const scaleOptions = question.options || [\n            {\n                text: \"非常不同意\",\n                value: \"1\",\n                score: 1\n            },\n            {\n                text: \"不同意\",\n                value: \"2\",\n                score: 2\n            },\n            {\n                text: \"中立\",\n                value: \"3\",\n                score: 3\n            },\n            {\n                text: \"同意\",\n                value: \"4\",\n                score: 4\n            },\n            {\n                text: \"非常同意\",\n                value: \"5\",\n                score: 5\n            }\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-5 gap-2\",\n                children: scaleOptions.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"flex flex-col items-center space-y-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                name: question.id,\n                                value: option.value,\n                                checked: value === option.value,\n                                onChange: (e)=>onChange(e.target.value),\n                                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-center text-gray-900\",\n                                children: option.text\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, undefined),\n                            option.score !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: option.score\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, \"\".concat(question.id, \"-scale-\").concat(index), true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 渲染文本输入\n    const renderTextInput = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n            value: value || \"\",\n            onChange: (e)=>onChange(e.target.value),\n            placeholder: \"请输入您的答案...\",\n            rows: 4,\n            className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, undefined);\n    };\n    // 根据问题类型渲染对应的输入控件\n    const renderQuestionInput = ()=>{\n        switch(question.type){\n            case \"single_choice\":\n            case \"single-choice\":\n                return renderSingleChoice();\n            case \"multiple_choice\":\n            case \"multiple-choice\":\n                return renderMultipleChoice();\n            case \"likert_scale\":\n            case \"likert-scale\":\n                return renderLikertScale();\n            case \"text\":\n            case \"textarea\":\n                return renderTextInput();\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"不支持的问题类型:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" \",\n                                question.type\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-yellow-600 mt-2\",\n                            children: \"请联系开发人员添加对此问题类型的支持\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 text-xs text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"问题数据:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"mt-1 bg-white p-2 rounded border text-xs overflow-auto\",\n                                    children: JSON.stringify(question, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900\",\n                        children: question.text || question.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined),\n                    question.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: question.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, undefined),\n                    question.dimension && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                children: question.dimension\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined),\n                            question.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: \"*\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: renderQuestionInput()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionDisplay.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, undefined);\n};\n_c = QuestionDisplay;\nvar _c;\n$RefreshReg$(_c, \"QuestionDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/QuestionDisplay.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/questionnaire/QuestionnaireLoader.tsx":
/*!**************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireLoader.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireLoader: function() { return /* binding */ QuestionnaireLoader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _QuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuestionnaireRenderer */ \"(app-pages-browser)/./src/components/questionnaire/QuestionnaireRenderer.tsx\");\n/* harmony import */ var _StreamQuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StreamQuestionnaireRenderer */ \"(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireLoader,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\n * 问卷加载器组件\n * 负责从API加载问卷配置并渲染问卷界面\n */ const QuestionnaireLoader = (param)=>{\n    let { version, organizationId, onComplete, onConfigLoaded } = param;\n    _s();\n    // 统一状态管理\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        config: null,\n        loading: true,\n        error: null,\n        retryCount: 0,\n        // 流式加载状态初始化\n        streamLoading: false,\n        loadedQuestions: [],\n        totalExpected: 60,\n        currentProgress: {\n            completed: 0,\n            total: 60,\n            percentage: 0\n        },\n        questionnaireId: null\n    });\n    /**\n   * 转换API数据为QuestionnaireConfig格式\n   */ const transformApiData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((apiData)=>{\n        var _apiData_questions;\n        const questionnaireConfig = {\n            version: apiData.version || version,\n            total_questions: ((_apiData_questions = apiData.questions) === null || _apiData_questions === void 0 ? void 0 : _apiData_questions.length) || 0,\n            dimensions: {\n                SF: {\n                    questions: []\n                },\n                IT: {\n                    questions: []\n                },\n                MV: {\n                    questions: []\n                },\n                AD: {\n                    questions: []\n                }\n            }\n        };\n        // 按维度分组问题\n        if (apiData.questions && Array.isArray(apiData.questions)) {\n            apiData.questions.forEach((q)=>{\n                var _q_options, _q_options1;\n                // 处理维度名称映射\n                let dimensionKey;\n                switch(q.dimension){\n                    case \"S/F\":\n                        dimensionKey = \"SF\";\n                        break;\n                    case \"I/T\":\n                        dimensionKey = \"IT\";\n                        break;\n                    case \"M/V\":\n                        dimensionKey = \"MV\";\n                        break;\n                    case \"A/D\":\n                        dimensionKey = \"AD\";\n                        break;\n                    default:\n                        console.warn(\"未知维度: \".concat(q.dimension, \"，默认使用SF\"));\n                        dimensionKey = \"SF\";\n                }\n                const configQuestion = {\n                    id: q.id,\n                    dimension: dimensionKey,\n                    sub_dimension: q.subdimension || \"\",\n                    type: q.type,\n                    text: q.title || q.text || \"\",\n                    options: ((_q_options = q.options) === null || _q_options === void 0 ? void 0 : _q_options.map((opt, index)=>({\n                            id: opt.id || \"\".concat(q.id, \"_opt_\").concat(index),\n                            text: opt.text || opt,\n                            value: opt.value || opt,\n                            score: opt.score || index + 1\n                        }))) || [],\n                    scoring: {\n                        dimension_weight: q.weight || 1,\n                        sub_dimension_weight: 1,\n                        option_scores: ((_q_options1 = q.options) === null || _q_options1 === void 0 ? void 0 : _q_options1.map((_, index)=>index + 1)) || [],\n                        reverse_scoring: false\n                    }\n                };\n                questionnaireConfig.dimensions[dimensionKey].questions.push(configQuestion);\n            });\n        }\n        return questionnaireConfig;\n    }, [\n        version\n    ]);\n    /**\n   * 流式加载问卷配置 - 新的核心方法\n   */ const loadQuestionnaireStreamly = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (state.retryCount >= 3) {\n            setState((prev)=>({\n                    ...prev,\n                    error: \"重试次数过多，请稍后再试\",\n                    loading: false,\n                    streamLoading: false\n                }));\n            return;\n        }\n        setState((prev)=>({\n                ...prev,\n                loading: true,\n                streamLoading: true,\n                error: null,\n                loadedQuestions: [],\n                currentProgress: {\n                    completed: 0,\n                    total: 60,\n                    percentage: 0\n                }\n            }));\n        try {\n            console.log(\"开始流式加载问卷配置:\", {\n                version,\n                organizationId\n            });\n            // 第一步：启动问卷生成，获取第一批题目\n            const firstBatchResponse = await fetch(\"/api/questionnaire/batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    organizationType: \"technology\",\n                    version,\n                    batchSize: 5\n                })\n            });\n            if (!firstBatchResponse.ok) {\n                throw new Error(\"启动问卷生成失败: \".concat(firstBatchResponse.status));\n            }\n            const firstBatch = await firstBatchResponse.json();\n            if (!firstBatch.success) {\n                throw new Error(firstBatch.error || \"启动问卷生成失败\");\n            }\n            console.log(\"收到第一批题目:\", firstBatch.data);\n            // 更新状态：显示第一批题目\n            setState((prev)=>({\n                    ...prev,\n                    loadedQuestions: firstBatch.data.questions,\n                    totalExpected: firstBatch.data.totalExpected,\n                    currentProgress: firstBatch.data.progress,\n                    questionnaireId: firstBatch.data.questionnaireId,\n                    loading: false // 第一批加载完成，可以开始显示\n                }));\n            // 第二步：继续加载剩余题目\n            await loadRemainingQuestions(firstBatch.data);\n        } catch (err) {\n            console.error(\"流式加载问卷配置失败:\", err);\n            setState((prev)=>({\n                    ...prev,\n                    error: err instanceof Error ? err.message : \"加载问卷配置失败\",\n                    loading: false,\n                    streamLoading: false,\n                    retryCount: prev.retryCount + 1\n                }));\n        }\n    }, [\n        version,\n        organizationId,\n        state.retryCount\n    ]);\n    /**\n   * 加载剩余题目\n   */ const loadRemainingQuestions = async (initialBatch)=>{\n        let nextBatch = initialBatch.nextBatch;\n        let shouldContinueLoading = true // 使用局部变量控制循环\n        ;\n        let batchCount = 0;\n        const maxBatches = 20 // 防止无限循环的保护机制\n        ;\n        console.log(\"开始加载剩余题目，初始nextBatch:\", nextBatch);\n        while(nextBatch && shouldContinueLoading && batchCount < maxBatches){\n            try {\n                var _batchData_data_questions;\n                batchCount++;\n                console.log(\"[批次 \".concat(batchCount, \"] 加载下一批题目: \").concat(nextBatch.dimension, \" 从 \").concat(nextBatch.startIndex));\n                const response = await fetch(\"/api/questionnaire/batch?questionnaireId=\".concat(initialBatch.questionnaireId, \"&dimension=\").concat(nextBatch.dimension, \"&startIndex=\").concat(nextBatch.startIndex, \"&batchSize=5\"));\n                if (!response.ok) {\n                    console.warn(\"[批次 \".concat(batchCount, \"] 批次加载失败: \").concat(response.status, \"，停止加载\"));\n                    shouldContinueLoading = false;\n                    break;\n                }\n                const batchData = await response.json();\n                if (!batchData.success) {\n                    console.warn(\"[批次 \".concat(batchCount, \"] 批次数据无效，停止加载:\"), batchData.error);\n                    shouldContinueLoading = false;\n                    break;\n                }\n                console.log(\"[批次 \".concat(batchCount, \"] 收到批次数据:\"), {\n                    dimension: batchData.data.dimension,\n                    questionCount: ((_batchData_data_questions = batchData.data.questions) === null || _batchData_data_questions === void 0 ? void 0 : _batchData_data_questions.length) || 0,\n                    progress: batchData.data.progress,\n                    hasNextBatch: !!batchData.data.nextBatch\n                });\n                // 累积添加新题目\n                setState((prev)=>({\n                        ...prev,\n                        loadedQuestions: [\n                            ...prev.loadedQuestions,\n                            ...batchData.data.questions\n                        ],\n                        currentProgress: batchData.data.progress\n                    }));\n                // 准备下一批\n                nextBatch = batchData.data.nextBatch;\n                // 如果没有下一批，说明全部完成\n                if (!nextBatch) {\n                    console.log(\"[批次 \".concat(batchCount, \"] 所有题目加载完成!\"));\n                    shouldContinueLoading = false;\n                    break;\n                }\n                // 短暂延迟，避免请求过于频繁\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            } catch (error) {\n                console.error(\"[批次 \".concat(batchCount, \"] 加载批次失败:\"), error);\n                shouldContinueLoading = false;\n                break;\n            }\n        }\n        // 确保在所有退出路径都更新streamLoading状态\n        setState((prev)=>({\n                ...prev,\n                streamLoading: false\n            }));\n        if (batchCount >= maxBatches) {\n            console.warn(\"达到最大批次限制，停止加载\");\n        }\n        console.log(\"题目加载流程结束，共处理 \".concat(batchCount, \" 个批次\"));\n    };\n    /**\n   * 传统的完整加载方法（保留作为备用）\n   * 加载问卷配置\n   */ const loadQuestionnaireConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            setState((prev)=>({\n                    ...prev,\n                    loading: true,\n                    error: null\n                }));\n            console.log(\"开始加载问卷配置:\", {\n                version,\n                organizationId\n            });\n            // 创建AbortController用于超时控制\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 15 * 60 * 1000) // 15分钟超时\n            ;\n            try {\n                const response = await fetch(\"/api/questionnaire/generate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        version,\n                        organizationType: \"technology\",\n                        organizationId\n                    }),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    throw new Error(\"生成问卷失败: \".concat(response.status, \" \").concat(response.statusText));\n                }\n                const result = await response.json();\n                if (!result.success) {\n                    var _result_error;\n                    throw new Error(((_result_error = result.error) === null || _result_error === void 0 ? void 0 : _result_error.message) || \"生成问卷失败\");\n                }\n                console.log(\"API返回数据:\", result.data);\n                // 转换API数据\n                const questionnaireConfig = transformApiData(result.data);\n                setState((prev)=>({\n                        ...prev,\n                        config: questionnaireConfig,\n                        loading: false,\n                        retryCount: 0\n                    }));\n                // 通知父组件配置已加载\n                if (onConfigLoaded) {\n                    onConfigLoaded(questionnaireConfig);\n                }\n            } catch (error) {\n                clearTimeout(timeoutId);\n                if (error instanceof Error && error.name === \"AbortError\") {\n                    throw new Error(\"请求超时，问卷生成时间过长，请稍后重试\");\n                }\n                throw error;\n            }\n        } catch (err) {\n            console.error(\"加载问卷配置失败:\", err);\n            setState((prev)=>({\n                    ...prev,\n                    error: err instanceof Error ? err.message : \"加载问卷配置失败\",\n                    loading: false,\n                    retryCount: prev.retryCount + 1\n                }));\n        }\n    }, [\n        version,\n        organizationId,\n        onConfigLoaded,\n        transformApiData\n    ]);\n    /**\n   * 处理问卷完成\n   */ const handleQuestionnaireComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (responses)=>{\n        console.log(\"问卷完成，回答数量:\", responses.length);\n        try {\n            if (onComplete) {\n                await onComplete(responses);\n            }\n        } catch (error) {\n            console.error(\"处理问卷完成失败:\", error);\n        // 这里可以添加错误处理逻辑\n        }\n    }, [\n        onComplete\n    ]);\n    /**\n   * 重试加载\n   */ const handleRetry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (state.retryCount < 3) {\n            loadQuestionnaireConfig();\n        } else {\n            setState((prev)=>({\n                    ...prev,\n                    error: \"重试次数过多，请刷新页面重试\"\n                }));\n        }\n    }, [\n        loadQuestionnaireConfig,\n        state.retryCount\n    ]);\n    // 初始化加载 - 使用流式加载\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadQuestionnaireStreamly();\n    }, [\n        loadQuestionnaireStreamly\n    ]);\n    // 渲染初始加载状态（只在第一批题目加载前显示）\n    if (state.loading && state.loadedQuestions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 font-medium\",\n                                children: \"正在生成第一批题目...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400 mt-1\",\n                                children: [\n                                    \"版本: \",\n                                    version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, undefined),\n                            organizationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"组织ID: \",\n                                    organizationId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 419,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 418,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (state.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"max-w-md w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-500 text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: \"加载失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4 text-sm\",\n                            children: state.error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleRetry,\n                                    disabled: state.retryCount >= 3,\n                                    className: \"w-full\",\n                                    children: state.retryCount >= 3 ? \"重试次数已用完\" : \"重新加载 (\".concat(state.retryCount, \"/3)\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, undefined),\n                                state.retryCount >= 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"w-full\",\n                                    children: \"刷新页面\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-xs text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"版本: \",\n                                        version\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, undefined),\n                                organizationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"组织ID: \",\n                                        organizationId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 34\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 437,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 436,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染流式加载状态 - 显示已加载的题目\n    if (state.loadedQuestions.length > 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"border-2 \".concat(state.streamLoading ? \"bg-blue-50 border-blue-200\" : \"bg-green-50 border-green-200\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xl mr-3 \".concat(state.streamLoading ? \"text-blue-500\" : \"text-green-500\"),\n                                    children: state.streamLoading ? \"\\uD83D\\uDD04\" : \"✅\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold \".concat(state.streamLoading ? \"text-blue-900\" : \"text-green-900\"),\n                                            children: state.streamLoading ? \"问卷生成中...\" : \"问卷生成完成\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm \".concat(state.streamLoading ? \"text-blue-700\" : \"text-green-700\"),\n                                            children: [\n                                                \"已生成 \",\n                                                state.loadedQuestions.length,\n                                                \" / \",\n                                                state.totalExpected,\n                                                \" 道题目\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        state.streamLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-blue-200 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(state.currentProgress.percentage, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                    children: [\n                                                        state.currentProgress.percentage,\n                                                        \"% 完成\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right text-xs text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"版本: \",\n                                                version\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"已加载: \",\n                                                state.loadedQuestions.length,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamQuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_5__.StreamQuestionnaireRenderer, {\n                    questions: state.loadedQuestions,\n                    isLoading: state.streamLoading,\n                    progress: state.currentProgress,\n                    onAnswerChange: (questionId, answer)=>{\n                        console.log(\"答案变更:\", questionId, answer);\n                    },\n                    onComplete: (responses)=>{\n                        console.log(\"问卷完成:\", responses);\n                        if (handleQuestionnaireComplete) {\n                            // 转换为旧格式兼容\n                            const convertedResponses = responses.map((r)=>({\n                                    questionId: r.questionId,\n                                    answer: r.answer,\n                                    timestamp: r.timestamp\n                                }));\n                            handleQuestionnaireComplete(convertedResponses);\n                        }\n                    },\n                    className: \"bg-white\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 476,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 传统的完整加载成功状态（保留兼容性）\n    if (state.config) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionnaireRenderer__WEBPACK_IMPORTED_MODULE_4__.QuestionnaireRenderer, {\n                config: state.config,\n                onComplete: handleQuestionnaireComplete,\n                className: \"bg-white\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n                lineNumber: 543,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireLoader.tsx\",\n            lineNumber: 542,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\n_s(QuestionnaireLoader, \"rfKJcX0DfAx6p2+l6/8/uVlBNfk=\");\n_c = QuestionnaireLoader;\n// 默认导出以保持兼容性\n/* harmony default export */ __webpack_exports__[\"default\"] = (QuestionnaireLoader);\nvar _c;\n$RefreshReg$(_c, \"QuestionnaireLoader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3F1ZXN0aW9ubmFpcmUvUXVlc3Rpb25uYWlyZUxvYWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUUrRDtBQWtCcEI7QUFDSTtBQUNnQjtBQUNZO0FBMEIzRTs7O0NBR0MsR0FDTSxNQUFNUSxzQkFBMEQ7UUFBQyxFQUN0RUMsT0FBTyxFQUNQQyxjQUFjLEVBQ2RDLFVBQVUsRUFDVkMsY0FBYyxFQUNmOztJQUNDLFNBQVM7SUFDVCxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR2IsK0NBQVFBLENBQWM7UUFDOUNjLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxPQUFPO1FBQ1BDLFlBQVk7UUFDWixZQUFZO1FBQ1pDLGVBQWU7UUFDZkMsaUJBQWlCLEVBQUU7UUFDbkJDLGVBQWU7UUFDZkMsaUJBQWlCO1lBQ2ZDLFdBQVc7WUFDWEMsT0FBTztZQUNQQyxZQUFZO1FBQ2Q7UUFDQUMsaUJBQWlCO0lBQ25CO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxtQkFBbUJ4QixrREFBV0EsQ0FBQyxDQUFDeUI7WUFHakJBO1FBRm5CLE1BQU1DLHNCQUEyQztZQUMvQ3BCLFNBQVNtQixRQUFRbkIsT0FBTyxJQUFJQTtZQUM1QnFCLGlCQUFpQkYsRUFBQUEscUJBQUFBLFFBQVFHLFNBQVMsY0FBakJILHlDQUFBQSxtQkFBbUJJLE1BQU0sS0FBSTtZQUM5Q0MsWUFBWTtnQkFDVkMsSUFBSTtvQkFBRUgsV0FBVyxFQUFFO2dCQUFDO2dCQUNwQkksSUFBSTtvQkFBRUosV0FBVyxFQUFFO2dCQUFDO2dCQUNwQkssSUFBSTtvQkFBRUwsV0FBVyxFQUFFO2dCQUFDO2dCQUNwQk0sSUFBSTtvQkFBRU4sV0FBVyxFQUFFO2dCQUFDO1lBQ3RCO1FBQ0Y7UUFFQSxVQUFVO1FBQ1YsSUFBSUgsUUFBUUcsU0FBUyxJQUFJTyxNQUFNQyxPQUFPLENBQUNYLFFBQVFHLFNBQVMsR0FBRztZQUN6REgsUUFBUUcsU0FBUyxDQUFDUyxPQUFPLENBQUMsQ0FBQ0M7b0JBMkJkQSxZQVNRQTtnQkFuQ25CLFdBQVc7Z0JBQ1gsSUFBSUM7Z0JBQ0osT0FBUUQsRUFBRUUsU0FBUztvQkFDakIsS0FBSzt3QkFDSEQsZUFBZTt3QkFDZjtvQkFDRixLQUFLO3dCQUNIQSxlQUFlO3dCQUNmO29CQUNGLEtBQUs7d0JBQ0hBLGVBQWU7d0JBQ2Y7b0JBQ0YsS0FBSzt3QkFDSEEsZUFBZTt3QkFDZjtvQkFDRjt3QkFDRUUsUUFBUUMsSUFBSSxDQUFDLFNBQXFCLE9BQVpKLEVBQUVFLFNBQVMsRUFBQzt3QkFDbENELGVBQWU7Z0JBQ25CO2dCQUVBLE1BQU1JLGlCQUEyQjtvQkFDL0JDLElBQUlOLEVBQUVNLEVBQUU7b0JBQ1JKLFdBQVdEO29CQUNYTSxlQUFlUCxFQUFFUSxZQUFZLElBQUk7b0JBQ2pDQyxNQUFNVCxFQUFFUyxJQUFJO29CQUNaQyxNQUFNVixFQUFFVyxLQUFLLElBQUlYLEVBQUVVLElBQUksSUFBSTtvQkFDM0JFLFNBQVNaLEVBQUFBLGFBQUFBLEVBQUVZLE9BQU8sY0FBVFosaUNBQUFBLFdBQVdhLEdBQUcsQ0FBQyxDQUFDQyxLQUFVQyxRQUFtQjs0QkFDcERULElBQUlRLElBQUlSLEVBQUUsSUFBSSxHQUFlUyxPQUFaZixFQUFFTSxFQUFFLEVBQUMsU0FBYSxPQUFOUzs0QkFDN0JMLE1BQU1JLElBQUlKLElBQUksSUFBSUk7NEJBQ2xCRSxPQUFPRixJQUFJRSxLQUFLLElBQUlGOzRCQUNwQkcsT0FBT0gsSUFBSUcsS0FBSyxJQUFJRixRQUFRO3dCQUM5QixRQUFPLEVBQUU7b0JBQ1RHLFNBQVM7d0JBQ1BDLGtCQUFrQm5CLEVBQUVvQixNQUFNLElBQUk7d0JBQzlCQyxzQkFBc0I7d0JBQ3RCQyxlQUFldEIsRUFBQUEsY0FBQUEsRUFBRVksT0FBTyxjQUFUWixrQ0FBQUEsWUFBV2EsR0FBRyxDQUFDLENBQUNVLEdBQVFSLFFBQWtCQSxRQUFRLE9BQU0sRUFBRTt3QkFDekVTLGlCQUFpQjtvQkFDbkI7Z0JBQ0Y7Z0JBRUFwQyxvQkFBb0JJLFVBQVUsQ0FBQ1MsYUFBYSxDQUFDWCxTQUFTLENBQUNtQyxJQUFJLENBQUNwQjtZQUM5RDtRQUNGO1FBRUEsT0FBT2pCO0lBQ1QsR0FBRztRQUFDcEI7S0FBUTtJQUVaOztHQUVDLEdBQ0QsTUFBTTBELDRCQUE0QmhFLGtEQUFXQSxDQUFDO1FBQzVDLElBQUlVLE1BQU1LLFVBQVUsSUFBSSxHQUFHO1lBQ3pCSixTQUFTc0QsQ0FBQUEsT0FBUztvQkFDaEIsR0FBR0EsSUFBSTtvQkFDUG5ELE9BQU87b0JBQ1BELFNBQVM7b0JBQ1RHLGVBQWU7Z0JBQ2pCO1lBQ0E7UUFDRjtRQUVBTCxTQUFTc0QsQ0FBQUEsT0FBUztnQkFDaEIsR0FBR0EsSUFBSTtnQkFDUHBELFNBQVM7Z0JBQ1RHLGVBQWU7Z0JBQ2ZGLE9BQU87Z0JBQ1BHLGlCQUFpQixFQUFFO2dCQUNuQkUsaUJBQWlCO29CQUFFQyxXQUFXO29CQUFHQyxPQUFPO29CQUFJQyxZQUFZO2dCQUFFO1lBQzVEO1FBRUEsSUFBSTtZQUNGbUIsUUFBUXlCLEdBQUcsQ0FBQyxlQUFlO2dCQUFFNUQ7Z0JBQVNDO1lBQWU7WUFFckQscUJBQXFCO1lBQ3JCLE1BQU00RCxxQkFBcUIsTUFBTUMsTUFBTSw0QkFBNEI7Z0JBQ2pFQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLGtCQUFrQjtvQkFDbEJwRTtvQkFDQXFFLFdBQVc7Z0JBQ2I7WUFDRjtZQUVBLElBQUksQ0FBQ1IsbUJBQW1CUyxFQUFFLEVBQUU7Z0JBQzFCLE1BQU0sSUFBSUMsTUFBTSxhQUF1QyxPQUExQlYsbUJBQW1CVyxNQUFNO1lBQ3hEO1lBRUEsTUFBTUMsYUFBYSxNQUFNWixtQkFBbUJhLElBQUk7WUFFaEQsSUFBSSxDQUFDRCxXQUFXRSxPQUFPLEVBQUU7Z0JBQ3ZCLE1BQU0sSUFBSUosTUFBTUUsV0FBV2pFLEtBQUssSUFBSTtZQUN0QztZQUVBMkIsUUFBUXlCLEdBQUcsQ0FBQyxZQUFZYSxXQUFXRyxJQUFJO1lBRXZDLGVBQWU7WUFDZnZFLFNBQVNzRCxDQUFBQSxPQUFTO29CQUNoQixHQUFHQSxJQUFJO29CQUNQaEQsaUJBQWlCOEQsV0FBV0csSUFBSSxDQUFDdEQsU0FBUztvQkFDMUNWLGVBQWU2RCxXQUFXRyxJQUFJLENBQUNoRSxhQUFhO29CQUM1Q0MsaUJBQWlCNEQsV0FBV0csSUFBSSxDQUFDQyxRQUFRO29CQUN6QzVELGlCQUFpQndELFdBQVdHLElBQUksQ0FBQzNELGVBQWU7b0JBQ2hEVixTQUFTLE1BQU0saUJBQWlCO2dCQUNsQztZQUVBLGVBQWU7WUFDZixNQUFNdUUsdUJBQXVCTCxXQUFXRyxJQUFJO1FBRTlDLEVBQUUsT0FBT0csS0FBSztZQUNaNUMsUUFBUTNCLEtBQUssQ0FBQyxlQUFldUU7WUFDN0IxRSxTQUFTc0QsQ0FBQUEsT0FBUztvQkFDaEIsR0FBR0EsSUFBSTtvQkFDUG5ELE9BQU91RSxlQUFlUixRQUFRUSxJQUFJQyxPQUFPLEdBQUc7b0JBQzVDekUsU0FBUztvQkFDVEcsZUFBZTtvQkFDZkQsWUFBWWtELEtBQUtsRCxVQUFVLEdBQUc7Z0JBQ2hDO1FBQ0Y7SUFDRixHQUFHO1FBQUNUO1FBQVNDO1FBQWdCRyxNQUFNSyxVQUFVO0tBQUM7SUFFOUM7O0dBRUMsR0FDRCxNQUFNcUUseUJBQXlCLE9BQU9HO1FBQ3BDLElBQUlDLFlBQVlELGFBQWFDLFNBQVM7UUFDdEMsSUFBSUMsd0JBQXdCLEtBQUssYUFBYTs7UUFDOUMsSUFBSUMsYUFBYTtRQUNqQixNQUFNQyxhQUFhLEdBQUcsY0FBYzs7UUFFcENsRCxRQUFReUIsR0FBRyxDQUFDLHlCQUF5QnNCO1FBRXJDLE1BQU9BLGFBQWFDLHlCQUF5QkMsYUFBYUMsV0FBWTtZQUNwRSxJQUFJO29CQXdCZUM7Z0JBdkJqQkY7Z0JBQ0FqRCxRQUFReUIsR0FBRyxDQUFDLE9BQStCc0IsT0FBeEJFLFlBQVcsZUFBc0NGLE9BQXpCQSxVQUFVaEQsU0FBUyxFQUFDLE9BQTBCLE9BQXJCZ0QsVUFBVUssVUFBVTtnQkFFeEYsTUFBTUMsV0FBVyxNQUFNMUIsTUFDckIsNENBQXNGb0IsT0FBMUNELGFBQWFoRSxlQUFlLEVBQUMsZUFBK0NpRSxPQUFsQ0EsVUFBVWhELFNBQVMsRUFBQyxnQkFBbUMsT0FBckJnRCxVQUFVSyxVQUFVLEVBQUM7Z0JBRy9JLElBQUksQ0FBQ0MsU0FBU2xCLEVBQUUsRUFBRTtvQkFDaEJuQyxRQUFRQyxJQUFJLENBQUMsT0FBOEJvRCxPQUF2QkosWUFBVyxjQUE0QixPQUFoQkksU0FBU2hCLE1BQU0sRUFBQztvQkFDM0RXLHdCQUF3QjtvQkFDeEI7Z0JBQ0Y7Z0JBRUEsTUFBTUcsWUFBWSxNQUFNRSxTQUFTZCxJQUFJO2dCQUVyQyxJQUFJLENBQUNZLFVBQVVYLE9BQU8sRUFBRTtvQkFDdEJ4QyxRQUFRQyxJQUFJLENBQUMsT0FBa0IsT0FBWGdELFlBQVcsbUJBQWlCRSxVQUFVOUUsS0FBSztvQkFDL0QyRSx3QkFBd0I7b0JBQ3hCO2dCQUNGO2dCQUVBaEQsUUFBUXlCLEdBQUcsQ0FBQyxPQUFrQixPQUFYd0IsWUFBVyxjQUFZO29CQUN4Q2xELFdBQVdvRCxVQUFVVixJQUFJLENBQUMxQyxTQUFTO29CQUNuQ3VELGVBQWVILEVBQUFBLDRCQUFBQSxVQUFVVixJQUFJLENBQUN0RCxTQUFTLGNBQXhCZ0UsZ0RBQUFBLDBCQUEwQi9ELE1BQU0sS0FBSTtvQkFDbkRzRCxVQUFVUyxVQUFVVixJQUFJLENBQUNDLFFBQVE7b0JBQ2pDYSxjQUFjLENBQUMsQ0FBQ0osVUFBVVYsSUFBSSxDQUFDTSxTQUFTO2dCQUMxQztnQkFFQSxVQUFVO2dCQUNWN0UsU0FBU3NELENBQUFBLE9BQVM7d0JBQ2hCLEdBQUdBLElBQUk7d0JBQ1BoRCxpQkFBaUI7K0JBQUlnRCxLQUFLaEQsZUFBZTsrQkFBSzJFLFVBQVVWLElBQUksQ0FBQ3RELFNBQVM7eUJBQUM7d0JBQ3ZFVCxpQkFBaUJ5RSxVQUFVVixJQUFJLENBQUNDLFFBQVE7b0JBQzFDO2dCQUVBLFFBQVE7Z0JBQ1JLLFlBQVlJLFVBQVVWLElBQUksQ0FBQ00sU0FBUztnQkFFcEMsaUJBQWlCO2dCQUNqQixJQUFJLENBQUNBLFdBQVc7b0JBQ2QvQyxRQUFReUIsR0FBRyxDQUFDLE9BQWtCLE9BQVh3QixZQUFXO29CQUM5QkQsd0JBQXdCO29CQUN4QjtnQkFDRjtnQkFFQSxnQkFBZ0I7Z0JBQ2hCLE1BQU0sSUFBSVEsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztZQUVuRCxFQUFFLE9BQU9wRixPQUFPO2dCQUNkMkIsUUFBUTNCLEtBQUssQ0FBQyxPQUFrQixPQUFYNEUsWUFBVyxjQUFZNUU7Z0JBQzVDMkUsd0JBQXdCO2dCQUN4QjtZQUNGO1FBQ0Y7UUFFQSw4QkFBOEI7UUFDOUI5RSxTQUFTc0QsQ0FBQUEsT0FBUztnQkFDaEIsR0FBR0EsSUFBSTtnQkFDUGpELGVBQWU7WUFDakI7UUFFQSxJQUFJMEUsY0FBY0MsWUFBWTtZQUM1QmxELFFBQVFDLElBQUksQ0FBQztRQUNmO1FBRUFELFFBQVF5QixHQUFHLENBQUMsZ0JBQTJCLE9BQVh3QixZQUFXO0lBQ3pDO0lBRUE7OztHQUdDLEdBQ0QsTUFBTVUsMEJBQTBCcEcsa0RBQVdBLENBQUM7UUFDMUMsSUFBSTtZQUNGVyxTQUFTc0QsQ0FBQUEsT0FBUztvQkFDaEIsR0FBR0EsSUFBSTtvQkFDUHBELFNBQVM7b0JBQ1RDLE9BQU87Z0JBQ1Q7WUFFQTJCLFFBQVF5QixHQUFHLENBQUMsYUFBYTtnQkFBRTVEO2dCQUFTQztZQUFlO1lBRW5ELDBCQUEwQjtZQUMxQixNQUFNOEYsYUFBYSxJQUFJQztZQUN2QixNQUFNQyxZQUFZSixXQUFXLElBQU1FLFdBQVdHLEtBQUssSUFBSSxLQUFLLEtBQUssTUFBTSxTQUFTOztZQUVoRixJQUFJO2dCQUNGLE1BQU1WLFdBQVcsTUFBTTFCLE1BQU0sK0JBQStCO29CQUMxREMsUUFBUTtvQkFDUkMsU0FBUzt3QkFDUCxnQkFBZ0I7b0JBQ2xCO29CQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7d0JBQ25CbkU7d0JBQ0FvRSxrQkFBa0I7d0JBQ2xCbkU7b0JBQ0Y7b0JBQ0FrRyxRQUFRSixXQUFXSSxNQUFNO2dCQUMzQjtnQkFFQUMsYUFBYUg7Z0JBRWIsSUFBSSxDQUFDVCxTQUFTbEIsRUFBRSxFQUFFO29CQUNoQixNQUFNLElBQUlDLE1BQU0sV0FBOEJpQixPQUFuQkEsU0FBU2hCLE1BQU0sRUFBQyxLQUF1QixPQUFwQmdCLFNBQVNhLFVBQVU7Z0JBQ25FO2dCQUVBLE1BQU1DLFNBQVMsTUFBTWQsU0FBU2QsSUFBSTtnQkFFbEMsSUFBSSxDQUFDNEIsT0FBTzNCLE9BQU8sRUFBRTt3QkFDSDJCO29CQUFoQixNQUFNLElBQUkvQixNQUFNK0IsRUFBQUEsZ0JBQUFBLE9BQU85RixLQUFLLGNBQVo4RixvQ0FBQUEsY0FBY3RCLE9BQU8sS0FBSTtnQkFDM0M7Z0JBRUE3QyxRQUFReUIsR0FBRyxDQUFDLFlBQVkwQyxPQUFPMUIsSUFBSTtnQkFFbkMsVUFBVTtnQkFDVixNQUFNeEQsc0JBQXNCRixpQkFBaUJvRixPQUFPMUIsSUFBSTtnQkFFeER2RSxTQUFTc0QsQ0FBQUEsT0FBUzt3QkFDaEIsR0FBR0EsSUFBSTt3QkFDUHJELFFBQVFjO3dCQUNSYixTQUFTO3dCQUNURSxZQUFZO29CQUNkO2dCQUVBLGFBQWE7Z0JBQ2IsSUFBSU4sZ0JBQWdCO29CQUNsQkEsZUFBZWlCO2dCQUNqQjtZQUVGLEVBQUUsT0FBT1osT0FBTztnQkFDZDRGLGFBQWFIO2dCQUNiLElBQUl6RixpQkFBaUIrRCxTQUFTL0QsTUFBTStGLElBQUksS0FBSyxjQUFjO29CQUN6RCxNQUFNLElBQUloQyxNQUFNO2dCQUNsQjtnQkFDQSxNQUFNL0Q7WUFDUjtRQUVGLEVBQUUsT0FBT3VFLEtBQUs7WUFDWjVDLFFBQVEzQixLQUFLLENBQUMsYUFBYXVFO1lBQzNCMUUsU0FBU3NELENBQUFBLE9BQVM7b0JBQ2hCLEdBQUdBLElBQUk7b0JBQ1BuRCxPQUFPdUUsZUFBZVIsUUFBUVEsSUFBSUMsT0FBTyxHQUFHO29CQUM1Q3pFLFNBQVM7b0JBQ1RFLFlBQVlrRCxLQUFLbEQsVUFBVSxHQUFHO2dCQUNoQztRQUNGO0lBQ0YsR0FBRztRQUFDVDtRQUFTQztRQUFnQkU7UUFBZ0JlO0tBQWlCO0lBRTlEOztHQUVDLEdBQ0QsTUFBTXNGLDhCQUE4QjlHLGtEQUFXQSxDQUFDLE9BQU8rRztRQUNyRHRFLFFBQVF5QixHQUFHLENBQUMsY0FBYzZDLFVBQVVsRixNQUFNO1FBRTFDLElBQUk7WUFDRixJQUFJckIsWUFBWTtnQkFDZCxNQUFNQSxXQUFXdUc7WUFDbkI7UUFDRixFQUFFLE9BQU9qRyxPQUFPO1lBQ2QyQixRQUFRM0IsS0FBSyxDQUFDLGFBQWFBO1FBQzNCLGVBQWU7UUFDakI7SUFDRixHQUFHO1FBQUNOO0tBQVc7SUFFZjs7R0FFQyxHQUNELE1BQU13RyxjQUFjaEgsa0RBQVdBLENBQUM7UUFDOUIsSUFBSVUsTUFBTUssVUFBVSxHQUFHLEdBQUc7WUFDeEJxRjtRQUNGLE9BQU87WUFDTHpGLFNBQVNzRCxDQUFBQSxPQUFTO29CQUNoQixHQUFHQSxJQUFJO29CQUNQbkQsT0FBTztnQkFDVDtRQUNGO0lBQ0YsR0FBRztRQUFDc0Y7UUFBeUIxRixNQUFNSyxVQUFVO0tBQUM7SUFFOUMsaUJBQWlCO0lBQ2pCaEIsZ0RBQVNBLENBQUM7UUFDUmlFO0lBQ0YsR0FBRztRQUFDQTtLQUEwQjtJQUU5Qix5QkFBeUI7SUFDekIsSUFBSXRELE1BQU1HLE9BQU8sSUFBSUgsTUFBTU8sZUFBZSxDQUFDWSxNQUFNLEtBQUssR0FBRztRQUN2RCxxQkFDRSw4REFBQ29GO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0Q7OzBDQUNDLDhEQUFDRTtnQ0FBRUQsV0FBVTswQ0FBNEI7Ozs7OzswQ0FDekMsOERBQUNDO2dDQUFFRCxXQUFVOztvQ0FBNkI7b0NBQUs1Rzs7Ozs7Ozs0QkFDOUNDLGdDQUNDLDhEQUFDNEc7Z0NBQUVELFdBQVU7O29DQUF3QjtvQ0FBTzNHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNeEQ7SUFFQSxTQUFTO0lBQ1QsSUFBSUcsTUFBTUksS0FBSyxFQUFFO1FBQ2YscUJBQ0UsOERBQUNtRztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDakgscURBQUlBO2dCQUFDaUgsV0FBVTswQkFDZCw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FBNkI7Ozs7OztzQ0FDNUMsOERBQUNFOzRCQUFHRixXQUFVO3NDQUEyQzs7Ozs7O3NDQUN6RCw4REFBQ0M7NEJBQUVELFdBQVU7c0NBQThCeEcsTUFBTUksS0FBSzs7Ozs7O3NDQUV0RCw4REFBQ21HOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2hILHlEQUFNQTtvQ0FDTG1ILFNBQVNMO29DQUNUTSxVQUFVNUcsTUFBTUssVUFBVSxJQUFJO29DQUM5Qm1HLFdBQVU7OENBRVR4RyxNQUFNSyxVQUFVLElBQUksSUFBSSxZQUFZLFNBQTBCLE9BQWpCTCxNQUFNSyxVQUFVLEVBQUM7Ozs7OztnQ0FHaEVMLE1BQU1LLFVBQVUsSUFBSSxtQkFDbkIsOERBQUNiLHlEQUFNQTtvQ0FDTHFILFNBQVE7b0NBQ1JGLFNBQVMsSUFBTUcsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO29DQUNyQ1IsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7O3NDQU1MLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDOzt3Q0FBRTt3Q0FBSzdHOzs7Ozs7O2dDQUNQQyxnQ0FBa0IsOERBQUM0Rzs7d0NBQUU7d0NBQU81Rzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNekM7SUFFQSxzQkFBc0I7SUFDdEIsSUFBSUcsTUFBTU8sZUFBZSxDQUFDWSxNQUFNLEdBQUcsR0FBRztRQUNwQyxxQkFDRSw4REFBQ29GO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDakgscURBQUlBO29CQUFDaUgsV0FBVyxZQUFnRyxPQUFwRnhHLE1BQU1NLGFBQWEsR0FBRywrQkFBK0I7OEJBQ2hGLDRFQUFDaUc7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVcsZ0JBQXlFLE9BQXpEeEcsTUFBTU0sYUFBYSxHQUFHLGtCQUFrQjs4Q0FDckVOLE1BQU1NLGFBQWEsR0FBRyxpQkFBTzs7Ozs7OzhDQUVoQyw4REFBQ2lHO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0U7NENBQUdGLFdBQVcseUJBQWtGLE9BQXpEeEcsTUFBTU0sYUFBYSxHQUFHLGtCQUFrQjtzREFDN0VOLE1BQU1NLGFBQWEsR0FBRyxhQUFhOzs7Ozs7c0RBRXRDLDhEQUFDbUc7NENBQUVELFdBQVcsV0FBb0UsT0FBekR4RyxNQUFNTSxhQUFhLEdBQUcsa0JBQWtCOztnREFBb0I7Z0RBQzlFTixNQUFNTyxlQUFlLENBQUNZLE1BQU07Z0RBQUM7Z0RBQUluQixNQUFNUSxhQUFhO2dEQUFDOzs7Ozs7O3dDQUUzRFIsTUFBTU0sYUFBYSxrQkFDbEIsOERBQUNpRzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFDQ0MsV0FBVTt3REFDVlMsT0FBTzs0REFBRUMsT0FBTyxHQUFvQyxPQUFqQ2xILE1BQU1TLGVBQWUsQ0FBQ0csVUFBVSxFQUFDO3dEQUFHOzs7Ozs7Ozs7Ozs4REFHM0QsOERBQUM2RjtvREFBRUQsV0FBVTs7d0RBQ1Z4RyxNQUFNUyxlQUFlLENBQUNHLFVBQVU7d0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzFDLDhEQUFDMkY7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7Z0RBQUk7Z0RBQUszRzs7Ozs7OztzREFDViw4REFBQzJHOztnREFBSTtnREFBTXZHLE1BQU1PLGVBQWUsQ0FBQ1ksTUFBTTtnREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT2hELDhEQUFDekIscUZBQTJCQTtvQkFDMUJ3QixXQUFXbEIsTUFBTU8sZUFBZTtvQkFDaEM0RyxXQUFXbkgsTUFBTU0sYUFBYTtvQkFDOUJtRSxVQUFVekUsTUFBTVMsZUFBZTtvQkFDL0IyRyxnQkFBZ0IsQ0FBQ0MsWUFBWUM7d0JBQzNCdkYsUUFBUXlCLEdBQUcsQ0FBQyxTQUFTNkQsWUFBWUM7b0JBQ25DO29CQUNBeEgsWUFBWSxDQUFDdUc7d0JBQ1h0RSxRQUFReUIsR0FBRyxDQUFDLFNBQVM2Qzt3QkFDckIsSUFBSUQsNkJBQTZCOzRCQUMvQixXQUFXOzRCQUNYLE1BQU1tQixxQkFBcUJsQixVQUFVNUQsR0FBRyxDQUFDK0UsQ0FBQUEsSUFBTTtvQ0FDN0NILFlBQVlHLEVBQUVILFVBQVU7b0NBQ3hCQyxRQUFRRSxFQUFFRixNQUFNO29DQUNoQkcsV0FBV0QsRUFBRUMsU0FBUztnQ0FDeEI7NEJBQ0FyQiw0QkFBNEJtQjt3QkFDOUI7b0JBQ0Y7b0JBQ0FmLFdBQVU7Ozs7Ozs7Ozs7OztJQUlsQjtJQUVBLHFCQUFxQjtJQUNyQixJQUFJeEcsTUFBTUUsTUFBTSxFQUFFO1FBQ2hCLHFCQUNFLDhEQUFDcUc7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQy9HLHlFQUFxQkE7Z0JBQ3BCUyxRQUFRRixNQUFNRSxNQUFNO2dCQUNwQkosWUFBWXNHO2dCQUNaSSxXQUFVOzs7Ozs7Ozs7OztJQUlsQjtJQUVBLE9BQU87QUFDVCxFQUFDO0dBbmZZN0c7S0FBQUE7QUFxZmIsYUFBYTtBQUNiLCtEQUFlQSxtQkFBbUJBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvcXVlc3Rpb25uYWlyZS9RdWVzdGlvbm5haXJlTG9hZGVyLnRzeD80ZTVhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFF1ZXN0aW9ubmFpcmVDb25maWcsIFF1ZXN0aW9ubmFpcmVSZXNwb25zZSwgUXVlc3Rpb24gfSBmcm9tICdAL3R5cGVzJ1xuXG4vLyDmtYHlvI/popjnm67nsbvlnovlrprkuYlcbmludGVyZmFjZSBTdHJlYW1RdWVzdGlvbiB7XG4gIGlkOiBzdHJpbmdcbiAgZGltZW5zaW9uOiBzdHJpbmdcbiAgdHlwZTogc3RyaW5nXG4gIHRleHQ6IHN0cmluZ1xuICBvcHRpb25zOiBBcnJheTx7XG4gICAgaWQ6IHN0cmluZ1xuICAgIHRleHQ6IHN0cmluZ1xuICAgIHNjb3JlOiBudW1iZXJcbiAgfT5cbiAgcmVxdWlyZWQ6IGJvb2xlYW5cbiAgb3JkZXI6IG51bWJlclxufVxuaW1wb3J0IHsgTG9hZGluZyB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9Mb2FkaW5nJ1xuaW1wb3J0IHsgQ2FyZCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9DYXJkJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0J1dHRvbidcbmltcG9ydCB7IFF1ZXN0aW9ubmFpcmVSZW5kZXJlciB9IGZyb20gJy4vUXVlc3Rpb25uYWlyZVJlbmRlcmVyJ1xuaW1wb3J0IHsgU3RyZWFtUXVlc3Rpb25uYWlyZVJlbmRlcmVyIH0gZnJvbSAnLi9TdHJlYW1RdWVzdGlvbm5haXJlUmVuZGVyZXInXG5cbmludGVyZmFjZSBRdWVzdGlvbm5haXJlTG9hZGVyUHJvcHMge1xuICB2ZXJzaW9uOiAnc3RhbmRhcmQnIHwgJ3Byb2Zlc3Npb25hbCdcbiAgb3JnYW5pemF0aW9uSWQ/OiBzdHJpbmdcbiAgb25Db21wbGV0ZT86IChyZXNwb25zZXM6IFF1ZXN0aW9ubmFpcmVSZXNwb25zZVtdKSA9PiBQcm9taXNlPHZvaWQ+XG4gIG9uQ29uZmlnTG9hZGVkPzogKGNvbmZpZzogUXVlc3Rpb25uYWlyZUNvbmZpZykgPT4gdm9pZFxufVxuXG5pbnRlcmZhY2UgTG9hZGVyU3RhdGUge1xuICBjb25maWc6IFF1ZXN0aW9ubmFpcmVDb25maWcgfCBudWxsXG4gIGxvYWRpbmc6IGJvb2xlYW5cbiAgZXJyb3I6IHN0cmluZyB8IG51bGxcbiAgcmV0cnlDb3VudDogbnVtYmVyXG4gIC8vIOaWsOWinua1geW8j+WKoOi9veeKtuaAgVxuICBzdHJlYW1Mb2FkaW5nOiBib29sZWFuXG4gIGxvYWRlZFF1ZXN0aW9uczogU3RyZWFtUXVlc3Rpb25bXVxuICB0b3RhbEV4cGVjdGVkOiBudW1iZXJcbiAgY3VycmVudFByb2dyZXNzOiB7XG4gICAgY29tcGxldGVkOiBudW1iZXJcbiAgICB0b3RhbDogbnVtYmVyXG4gICAgcGVyY2VudGFnZTogbnVtYmVyXG4gIH1cbiAgcXVlc3Rpb25uYWlyZUlkOiBzdHJpbmcgfCBudWxsXG59XG5cbi8qKlxuICog6Zeu5Y235Yqg6L295Zmo57uE5Lu2XG4gKiDotJ/otKPku45BUEnliqDovb3pl67ljbfphY3nva7lubbmuLLmn5Ppl67ljbfnlYzpnaJcbiAqL1xuZXhwb3J0IGNvbnN0IFF1ZXN0aW9ubmFpcmVMb2FkZXI6IFJlYWN0LkZDPFF1ZXN0aW9ubmFpcmVMb2FkZXJQcm9wcz4gPSAoeyBcbiAgdmVyc2lvbiwgXG4gIG9yZ2FuaXphdGlvbklkLCBcbiAgb25Db21wbGV0ZSxcbiAgb25Db25maWdMb2FkZWQgXG59KSA9PiB7XG4gIC8vIOe7n+S4gOeKtuaAgeeuoeeQhlxuICBjb25zdCBbc3RhdGUsIHNldFN0YXRlXSA9IHVzZVN0YXRlPExvYWRlclN0YXRlPih7XG4gICAgY29uZmlnOiBudWxsLFxuICAgIGxvYWRpbmc6IHRydWUsXG4gICAgZXJyb3I6IG51bGwsXG4gICAgcmV0cnlDb3VudDogMCxcbiAgICAvLyDmtYHlvI/liqDovb3nirbmgIHliJ3lp4vljJZcbiAgICBzdHJlYW1Mb2FkaW5nOiBmYWxzZSxcbiAgICBsb2FkZWRRdWVzdGlvbnM6IFtdLFxuICAgIHRvdGFsRXhwZWN0ZWQ6IDYwLFxuICAgIGN1cnJlbnRQcm9ncmVzczoge1xuICAgICAgY29tcGxldGVkOiAwLFxuICAgICAgdG90YWw6IDYwLFxuICAgICAgcGVyY2VudGFnZTogMFxuICAgIH0sXG4gICAgcXVlc3Rpb25uYWlyZUlkOiBudWxsXG4gIH0pXG5cbiAgLyoqXG4gICAqIOi9rOaNokFQSeaVsOaNruS4ulF1ZXN0aW9ubmFpcmVDb25maWfmoLzlvI9cbiAgICovXG4gIGNvbnN0IHRyYW5zZm9ybUFwaURhdGEgPSB1c2VDYWxsYmFjaygoYXBpRGF0YTogYW55KTogUXVlc3Rpb25uYWlyZUNvbmZpZyA9PiB7XG4gICAgY29uc3QgcXVlc3Rpb25uYWlyZUNvbmZpZzogUXVlc3Rpb25uYWlyZUNvbmZpZyA9IHtcbiAgICAgIHZlcnNpb246IGFwaURhdGEudmVyc2lvbiB8fCB2ZXJzaW9uLFxuICAgICAgdG90YWxfcXVlc3Rpb25zOiBhcGlEYXRhLnF1ZXN0aW9ucz8ubGVuZ3RoIHx8IDAsXG4gICAgICBkaW1lbnNpb25zOiB7XG4gICAgICAgIFNGOiB7IHF1ZXN0aW9uczogW10gfSxcbiAgICAgICAgSVQ6IHsgcXVlc3Rpb25zOiBbXSB9LFxuICAgICAgICBNVjogeyBxdWVzdGlvbnM6IFtdIH0sXG4gICAgICAgIEFEOiB7IHF1ZXN0aW9uczogW10gfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOaMiee7tOW6puWIhue7hOmXrumimFxuICAgIGlmIChhcGlEYXRhLnF1ZXN0aW9ucyAmJiBBcnJheS5pc0FycmF5KGFwaURhdGEucXVlc3Rpb25zKSkge1xuICAgICAgYXBpRGF0YS5xdWVzdGlvbnMuZm9yRWFjaCgocTogYW55KSA9PiB7XG4gICAgICAgIC8vIOWkhOeQhue7tOW6puWQjeensOaYoOWwhFxuICAgICAgICBsZXQgZGltZW5zaW9uS2V5OiAnU0YnIHwgJ0lUJyB8ICdNVicgfCAnQUQnXG4gICAgICAgIHN3aXRjaCAocS5kaW1lbnNpb24pIHtcbiAgICAgICAgICBjYXNlICdTL0YnOlxuICAgICAgICAgICAgZGltZW5zaW9uS2V5ID0gJ1NGJ1xuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICBjYXNlICdJL1QnOlxuICAgICAgICAgICAgZGltZW5zaW9uS2V5ID0gJ0lUJ1xuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICBjYXNlICdNL1YnOlxuICAgICAgICAgICAgZGltZW5zaW9uS2V5ID0gJ01WJ1xuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICBjYXNlICdBL0QnOlxuICAgICAgICAgICAgZGltZW5zaW9uS2V5ID0gJ0FEJ1xuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgY29uc29sZS53YXJuKGDmnKrnn6Xnu7TluqY6ICR7cS5kaW1lbnNpb25977yM6buY6K6k5L2/55SoU0ZgKVxuICAgICAgICAgICAgZGltZW5zaW9uS2V5ID0gJ1NGJ1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICBjb25zdCBjb25maWdRdWVzdGlvbjogUXVlc3Rpb24gPSB7XG4gICAgICAgICAgaWQ6IHEuaWQsXG4gICAgICAgICAgZGltZW5zaW9uOiBkaW1lbnNpb25LZXksXG4gICAgICAgICAgc3ViX2RpbWVuc2lvbjogcS5zdWJkaW1lbnNpb24gfHwgJycsXG4gICAgICAgICAgdHlwZTogcS50eXBlIGFzICdjaG9pY2UnIHwgJ3NjZW5hcmlvJyB8ICdyYW5raW5nJyB8ICdzY2FsZScsXG4gICAgICAgICAgdGV4dDogcS50aXRsZSB8fCBxLnRleHQgfHwgJycsXG4gICAgICAgICAgb3B0aW9uczogcS5vcHRpb25zPy5tYXAoKG9wdDogYW55LCBpbmRleDogbnVtYmVyKSA9PiAoe1xuICAgICAgICAgICAgaWQ6IG9wdC5pZCB8fCBgJHtxLmlkfV9vcHRfJHtpbmRleH1gLFxuICAgICAgICAgICAgdGV4dDogb3B0LnRleHQgfHwgb3B0LFxuICAgICAgICAgICAgdmFsdWU6IG9wdC52YWx1ZSB8fCBvcHQsXG4gICAgICAgICAgICBzY29yZTogb3B0LnNjb3JlIHx8IGluZGV4ICsgMVxuICAgICAgICAgIH0pKSB8fCBbXSxcbiAgICAgICAgICBzY29yaW5nOiB7XG4gICAgICAgICAgICBkaW1lbnNpb25fd2VpZ2h0OiBxLndlaWdodCB8fCAxLFxuICAgICAgICAgICAgc3ViX2RpbWVuc2lvbl93ZWlnaHQ6IDEsXG4gICAgICAgICAgICBvcHRpb25fc2NvcmVzOiBxLm9wdGlvbnM/Lm1hcCgoXzogYW55LCBpbmRleDogbnVtYmVyKSA9PiBpbmRleCArIDEpIHx8IFtdLFxuICAgICAgICAgICAgcmV2ZXJzZV9zY29yaW5nOiBmYWxzZVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgcXVlc3Rpb25uYWlyZUNvbmZpZy5kaW1lbnNpb25zW2RpbWVuc2lvbktleV0ucXVlc3Rpb25zLnB1c2goY29uZmlnUXVlc3Rpb24pXG4gICAgICB9KVxuICAgIH1cblxuICAgIHJldHVybiBxdWVzdGlvbm5haXJlQ29uZmlnXG4gIH0sIFt2ZXJzaW9uXSlcblxuICAvKipcbiAgICog5rWB5byP5Yqg6L296Zeu5Y236YWN572uIC0g5paw55qE5qC45b+D5pa55rOVXG4gICAqL1xuICBjb25zdCBsb2FkUXVlc3Rpb25uYWlyZVN0cmVhbWx5ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmIChzdGF0ZS5yZXRyeUNvdW50ID49IDMpIHtcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgZXJyb3I6ICfph43or5XmrKHmlbDov4flpJrvvIzor7fnqI3lkI7lho3or5UnLFxuICAgICAgICBsb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgc3RyZWFtTG9hZGluZzogZmFsc2VcbiAgICAgIH0pKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGxvYWRpbmc6IHRydWUsXG4gICAgICBzdHJlYW1Mb2FkaW5nOiB0cnVlLFxuICAgICAgZXJyb3I6IG51bGwsXG4gICAgICBsb2FkZWRRdWVzdGlvbnM6IFtdLFxuICAgICAgY3VycmVudFByb2dyZXNzOiB7IGNvbXBsZXRlZDogMCwgdG90YWw6IDYwLCBwZXJjZW50YWdlOiAwIH1cbiAgICB9KSlcblxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn5byA5aeL5rWB5byP5Yqg6L296Zeu5Y236YWN572uOicsIHsgdmVyc2lvbiwgb3JnYW5pemF0aW9uSWQgfSlcblxuICAgICAgLy8g56ys5LiA5q2l77ya5ZCv5Yqo6Zeu5Y2355Sf5oiQ77yM6I635Y+W56ys5LiA5om56aKY55uuXG4gICAgICBjb25zdCBmaXJzdEJhdGNoUmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9xdWVzdGlvbm5haXJlL2JhdGNoJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIG9yZ2FuaXphdGlvblR5cGU6ICd0ZWNobm9sb2d5JyxcbiAgICAgICAgICB2ZXJzaW9uLFxuICAgICAgICAgIGJhdGNoU2l6ZTogNVxuICAgICAgICB9KVxuICAgICAgfSlcblxuICAgICAgaWYgKCFmaXJzdEJhdGNoUmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGDlkK/liqjpl67ljbfnlJ/miJDlpLHotKU6ICR7Zmlyc3RCYXRjaFJlc3BvbnNlLnN0YXR1c31gKVxuICAgICAgfVxuXG4gICAgICBjb25zdCBmaXJzdEJhdGNoID0gYXdhaXQgZmlyc3RCYXRjaFJlc3BvbnNlLmpzb24oKVxuXG4gICAgICBpZiAoIWZpcnN0QmF0Y2guc3VjY2Vzcykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZmlyc3RCYXRjaC5lcnJvciB8fCAn5ZCv5Yqo6Zeu5Y2355Sf5oiQ5aSx6LSlJylcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ+aUtuWIsOesrOS4gOaJuemimOebrjonLCBmaXJzdEJhdGNoLmRhdGEpXG5cbiAgICAgIC8vIOabtOaWsOeKtuaAge+8muaYvuekuuesrOS4gOaJuemimOebrlxuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBsb2FkZWRRdWVzdGlvbnM6IGZpcnN0QmF0Y2guZGF0YS5xdWVzdGlvbnMsXG4gICAgICAgIHRvdGFsRXhwZWN0ZWQ6IGZpcnN0QmF0Y2guZGF0YS50b3RhbEV4cGVjdGVkLFxuICAgICAgICBjdXJyZW50UHJvZ3Jlc3M6IGZpcnN0QmF0Y2guZGF0YS5wcm9ncmVzcyxcbiAgICAgICAgcXVlc3Rpb25uYWlyZUlkOiBmaXJzdEJhdGNoLmRhdGEucXVlc3Rpb25uYWlyZUlkLFxuICAgICAgICBsb2FkaW5nOiBmYWxzZSAvLyDnrKzkuIDmibnliqDovb3lrozmiJDvvIzlj6/ku6XlvIDlp4vmmL7npLpcbiAgICAgIH0pKVxuXG4gICAgICAvLyDnrKzkuozmraXvvJrnu6fnu63liqDovb3liankvZnpopjnm65cbiAgICAgIGF3YWl0IGxvYWRSZW1haW5pbmdRdWVzdGlvbnMoZmlyc3RCYXRjaC5kYXRhKVxuXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfmtYHlvI/liqDovb3pl67ljbfphY3nva7lpLHotKU6JywgZXJyKVxuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBlcnJvcjogZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICfliqDovb3pl67ljbfphY3nva7lpLHotKUnLFxuICAgICAgICBsb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgc3RyZWFtTG9hZGluZzogZmFsc2UsIC8vIOehruS/neWcqOmUmeivr+aXtuS5n+WBnOatoua1geW8j+WKoOi9veeKtuaAgVxuICAgICAgICByZXRyeUNvdW50OiBwcmV2LnJldHJ5Q291bnQgKyAxXG4gICAgICB9KSlcbiAgICB9XG4gIH0sIFt2ZXJzaW9uLCBvcmdhbml6YXRpb25JZCwgc3RhdGUucmV0cnlDb3VudF0pXG5cbiAgLyoqXG4gICAqIOWKoOi9veWJqeS9memimOebrlxuICAgKi9cbiAgY29uc3QgbG9hZFJlbWFpbmluZ1F1ZXN0aW9ucyA9IGFzeW5jIChpbml0aWFsQmF0Y2g6IGFueSkgPT4ge1xuICAgIGxldCBuZXh0QmF0Y2ggPSBpbml0aWFsQmF0Y2gubmV4dEJhdGNoXG4gICAgbGV0IHNob3VsZENvbnRpbnVlTG9hZGluZyA9IHRydWUgLy8g5L2/55So5bGA6YOo5Y+Y6YeP5o6n5Yi25b6q546vXG4gICAgbGV0IGJhdGNoQ291bnQgPSAwXG4gICAgY29uc3QgbWF4QmF0Y2hlcyA9IDIwIC8vIOmYsuatouaXoOmZkOW+queOr+eahOS/neaKpOacuuWItlxuXG4gICAgY29uc29sZS5sb2coJ+W8gOWni+WKoOi9veWJqeS9memimOebru+8jOWIneWni25leHRCYXRjaDonLCBuZXh0QmF0Y2gpXG5cbiAgICB3aGlsZSAobmV4dEJhdGNoICYmIHNob3VsZENvbnRpbnVlTG9hZGluZyAmJiBiYXRjaENvdW50IDwgbWF4QmF0Y2hlcykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYmF0Y2hDb3VudCsrXG4gICAgICAgIGNvbnNvbGUubG9nKGBb5om55qyhICR7YmF0Y2hDb3VudH1dIOWKoOi9veS4i+S4gOaJuemimOebrjogJHtuZXh0QmF0Y2guZGltZW5zaW9ufSDku44gJHtuZXh0QmF0Y2guc3RhcnRJbmRleH1gKVxuXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goXG4gICAgICAgICAgYC9hcGkvcXVlc3Rpb25uYWlyZS9iYXRjaD9xdWVzdGlvbm5haXJlSWQ9JHtpbml0aWFsQmF0Y2gucXVlc3Rpb25uYWlyZUlkfSZkaW1lbnNpb249JHtuZXh0QmF0Y2guZGltZW5zaW9ufSZzdGFydEluZGV4PSR7bmV4dEJhdGNoLnN0YXJ0SW5kZXh9JmJhdGNoU2l6ZT01YFxuICAgICAgICApXG5cbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnNvbGUud2FybihgW+aJueasoSAke2JhdGNoQ291bnR9XSDmibnmrKHliqDovb3lpLHotKU6ICR7cmVzcG9uc2Uuc3RhdHVzfe+8jOWBnOatouWKoOi9vWApXG4gICAgICAgICAgc2hvdWxkQ29udGludWVMb2FkaW5nID0gZmFsc2VcbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgYmF0Y2hEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICAgICAgaWYgKCFiYXRjaERhdGEuc3VjY2Vzcykge1xuICAgICAgICAgIGNvbnNvbGUud2FybihgW+aJueasoSAke2JhdGNoQ291bnR9XSDmibnmrKHmlbDmja7ml6DmlYjvvIzlgZzmraLliqDovb06YCwgYmF0Y2hEYXRhLmVycm9yKVxuICAgICAgICAgIHNob3VsZENvbnRpbnVlTG9hZGluZyA9IGZhbHNlXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnNvbGUubG9nKGBb5om55qyhICR7YmF0Y2hDb3VudH1dIOaUtuWIsOaJueasoeaVsOaNrjpgLCB7XG4gICAgICAgICAgZGltZW5zaW9uOiBiYXRjaERhdGEuZGF0YS5kaW1lbnNpb24sXG4gICAgICAgICAgcXVlc3Rpb25Db3VudDogYmF0Y2hEYXRhLmRhdGEucXVlc3Rpb25zPy5sZW5ndGggfHwgMCxcbiAgICAgICAgICBwcm9ncmVzczogYmF0Y2hEYXRhLmRhdGEucHJvZ3Jlc3MsXG4gICAgICAgICAgaGFzTmV4dEJhdGNoOiAhIWJhdGNoRGF0YS5kYXRhLm5leHRCYXRjaFxuICAgICAgICB9KVxuXG4gICAgICAgIC8vIOe0r+enr+a3u+WKoOaWsOmimOebrlxuICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBsb2FkZWRRdWVzdGlvbnM6IFsuLi5wcmV2LmxvYWRlZFF1ZXN0aW9ucywgLi4uYmF0Y2hEYXRhLmRhdGEucXVlc3Rpb25zXSxcbiAgICAgICAgICBjdXJyZW50UHJvZ3Jlc3M6IGJhdGNoRGF0YS5kYXRhLnByb2dyZXNzXG4gICAgICAgIH0pKVxuXG4gICAgICAgIC8vIOWHhuWkh+S4i+S4gOaJuVxuICAgICAgICBuZXh0QmF0Y2ggPSBiYXRjaERhdGEuZGF0YS5uZXh0QmF0Y2hcblxuICAgICAgICAvLyDlpoLmnpzmsqHmnInkuIvkuIDmibnvvIzor7TmmI7lhajpg6jlrozmiJBcbiAgICAgICAgaWYgKCFuZXh0QmF0Y2gpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW+aJueasoSAke2JhdGNoQ291bnR9XSDmiYDmnInpopjnm67liqDovb3lrozmiJAhYClcbiAgICAgICAgICBzaG91bGRDb250aW51ZUxvYWRpbmcgPSBmYWxzZVxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cblxuICAgICAgICAvLyDnn63mmoLlu7bov5/vvIzpgb/lhY3or7fmsYLov4fkuo7popHnuYFcbiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMDApKVxuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBb5om55qyhICR7YmF0Y2hDb3VudH1dIOWKoOi9veaJueasoeWksei0pTpgLCBlcnJvcilcbiAgICAgICAgc2hvdWxkQ29udGludWVMb2FkaW5nID0gZmFsc2VcbiAgICAgICAgYnJlYWtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDnoa7kv53lnKjmiYDmnInpgIDlh7rot6/lvoTpg73mm7TmlrBzdHJlYW1Mb2FkaW5n54q25oCBXG4gICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHN0cmVhbUxvYWRpbmc6IGZhbHNlXG4gICAgfSkpXG5cbiAgICBpZiAoYmF0Y2hDb3VudCA+PSBtYXhCYXRjaGVzKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ+i+vuWIsOacgOWkp+aJueasoemZkOWItu+8jOWBnOatouWKoOi9vScpXG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coYOmimOebruWKoOi9vea1geeoi+e7k+adn++8jOWFseWkhOeQhiAke2JhdGNoQ291bnR9IOS4quaJueasoWApXG4gIH1cblxuICAvKipcbiAgICog5Lyg57uf55qE5a6M5pW05Yqg6L295pa55rOV77yI5L+d55WZ5L2c5Li65aSH55So77yJXG4gICAqIOWKoOi9vemXruWNt+mFjee9rlxuICAgKi9cbiAgY29uc3QgbG9hZFF1ZXN0aW9ubmFpcmVDb25maWcgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHsgXG4gICAgICAgIC4uLnByZXYsIFxuICAgICAgICBsb2FkaW5nOiB0cnVlLCBcbiAgICAgICAgZXJyb3I6IG51bGwgXG4gICAgICB9KSlcblxuICAgICAgY29uc29sZS5sb2coJ+W8gOWni+WKoOi9vemXruWNt+mFjee9rjonLCB7IHZlcnNpb24sIG9yZ2FuaXphdGlvbklkIH0pXG5cbiAgICAgIC8vIOWIm+W7ukFib3J0Q29udHJvbGxlcueUqOS6jui2heaXtuaOp+WItlxuICAgICAgY29uc3QgY29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKVxuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiBjb250cm9sbGVyLmFib3J0KCksIDE1ICogNjAgKiAxMDAwKSAvLyAxNeWIhumSn+i2heaXtlxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3F1ZXN0aW9ubmFpcmUvZ2VuZXJhdGUnLCB7XG4gICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICAgIH0sXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAgdmVyc2lvbixcbiAgICAgICAgICAgIG9yZ2FuaXphdGlvblR5cGU6ICd0ZWNobm9sb2d5JyxcbiAgICAgICAgICAgIG9yZ2FuaXphdGlvbklkXG4gICAgICAgICAgfSksXG4gICAgICAgICAgc2lnbmFsOiBjb250cm9sbGVyLnNpZ25hbFxuICAgICAgICB9KVxuXG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpXG5cbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihg55Sf5oiQ6Zeu5Y235aSx6LSlOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApXG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKClcblxuICAgICAgICBpZiAoIXJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3VsdC5lcnJvcj8ubWVzc2FnZSB8fCAn55Sf5oiQ6Zeu5Y235aSx6LSlJylcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnNvbGUubG9nKCdBUEnov5Tlm57mlbDmja46JywgcmVzdWx0LmRhdGEpXG5cbiAgICAgICAgLy8g6L2s5o2iQVBJ5pWw5o2uXG4gICAgICAgIGNvbnN0IHF1ZXN0aW9ubmFpcmVDb25maWcgPSB0cmFuc2Zvcm1BcGlEYXRhKHJlc3VsdC5kYXRhKVxuXG4gICAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIGNvbmZpZzogcXVlc3Rpb25uYWlyZUNvbmZpZyxcbiAgICAgICAgICBsb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICByZXRyeUNvdW50OiAwXG4gICAgICAgIH0pKVxuXG4gICAgICAgIC8vIOmAmuefpeeItue7hOS7tumFjee9ruW3suWKoOi9vVxuICAgICAgICBpZiAob25Db25maWdMb2FkZWQpIHtcbiAgICAgICAgICBvbkNvbmZpZ0xvYWRlZChxdWVzdGlvbm5haXJlQ29uZmlnKVxuICAgICAgICB9XG5cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpXG4gICAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yICYmIGVycm9yLm5hbWUgPT09ICdBYm9ydEVycm9yJykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcign6K+35rGC6LaF5pe277yM6Zeu5Y2355Sf5oiQ5pe26Ze06L+H6ZW/77yM6K+356iN5ZCO6YeN6K+VJylcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBlcnJvclxuICAgICAgfVxuICAgICAgXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfliqDovb3pl67ljbfphY3nva7lpLHotKU6JywgZXJyKVxuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBlcnJvcjogZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICfliqDovb3pl67ljbfphY3nva7lpLHotKUnLFxuICAgICAgICBsb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgcmV0cnlDb3VudDogcHJldi5yZXRyeUNvdW50ICsgMVxuICAgICAgfSkpXG4gICAgfVxuICB9LCBbdmVyc2lvbiwgb3JnYW5pemF0aW9uSWQsIG9uQ29uZmlnTG9hZGVkLCB0cmFuc2Zvcm1BcGlEYXRhXSlcblxuICAvKipcbiAgICog5aSE55CG6Zeu5Y235a6M5oiQXG4gICAqL1xuICBjb25zdCBoYW5kbGVRdWVzdGlvbm5haXJlQ29tcGxldGUgPSB1c2VDYWxsYmFjayhhc3luYyAocmVzcG9uc2VzOiBRdWVzdGlvbm5haXJlUmVzcG9uc2VbXSkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfpl67ljbflrozmiJDvvIzlm57nrZTmlbDph486JywgcmVzcG9uc2VzLmxlbmd0aClcbiAgICBcbiAgICB0cnkge1xuICAgICAgaWYgKG9uQ29tcGxldGUpIHtcbiAgICAgICAgYXdhaXQgb25Db21wbGV0ZShyZXNwb25zZXMpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WkhOeQhumXruWNt+WujOaIkOWksei0pTonLCBlcnJvcilcbiAgICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOmUmeivr+WkhOeQhumAu+i+kVxuICAgIH1cbiAgfSwgW29uQ29tcGxldGVdKVxuXG4gIC8qKlxuICAgKiDph43or5XliqDovb1cbiAgICovXG4gIGNvbnN0IGhhbmRsZVJldHJ5ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChzdGF0ZS5yZXRyeUNvdW50IDwgMykge1xuICAgICAgbG9hZFF1ZXN0aW9ubmFpcmVDb25maWcoKVxuICAgIH0gZWxzZSB7XG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIGVycm9yOiAn6YeN6K+V5qyh5pWw6L+H5aSa77yM6K+35Yi35paw6aG16Z2i6YeN6K+VJ1xuICAgICAgfSkpXG4gICAgfVxuICB9LCBbbG9hZFF1ZXN0aW9ubmFpcmVDb25maWcsIHN0YXRlLnJldHJ5Q291bnRdKVxuXG4gIC8vIOWIneWni+WMluWKoOi9vSAtIOS9v+eUqOa1geW8j+WKoOi9vVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWRRdWVzdGlvbm5haXJlU3RyZWFtbHkoKVxuICB9LCBbbG9hZFF1ZXN0aW9ubmFpcmVTdHJlYW1seV0pXG5cbiAgLy8g5riy5p+T5Yid5aeL5Yqg6L2954q25oCB77yI5Y+q5Zyo56ys5LiA5om56aKY55uu5Yqg6L295YmN5pi+56S677yJXG4gIGlmIChzdGF0ZS5sb2FkaW5nICYmIHN0YXRlLmxvYWRlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC1bNDAwcHhdXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvXCI+PC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZm9udC1tZWRpdW1cIj7mraPlnKjnlJ/miJDnrKzkuIDmibnpopjnm64uLi48L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDAgbXQtMVwiPueJiOacrDoge3ZlcnNpb259PC9wPlxuICAgICAgICAgICAge29yZ2FuaXphdGlvbklkICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+57uE57uHSUQ6IHtvcmdhbml6YXRpb25JZH08L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIC8vIOa4suafk+mUmeivr+eKtuaAgVxuICBpZiAoc3RhdGUuZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC1bNDAwcHhdXCI+XG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1heC13LW1kIHctZnVsbFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LTZ4bCBtYi00XCI+4pqg77iPPC9kaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPuWKoOi9veWksei0pTwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTQgdGV4dC1zbVwiPntzdGF0ZS5lcnJvcn08L3A+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZXRyeX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17c3RhdGUucmV0cnlDb3VudCA+PSAzfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7c3RhdGUucmV0cnlDb3VudCA+PSAzID8gJ+mHjeivleasoeaVsOW3sueUqOWujCcgOiBg6YeN5paw5Yqg6L29ICgke3N0YXRlLnJldHJ5Q291bnR9LzMpYH1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7c3RhdGUucmV0cnlDb3VudCA+PSAzICYmIChcbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cubG9jYXRpb24ucmVsb2FkKCl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOWIt+aWsOmhtemdolxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgPHA+54mI5pysOiB7dmVyc2lvbn08L3A+XG4gICAgICAgICAgICAgIHtvcmdhbml6YXRpb25JZCAmJiA8cD7nu4Tnu4dJRDoge29yZ2FuaXphdGlvbklkfTwvcD59XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgLy8g5riy5p+T5rWB5byP5Yqg6L2954q25oCBIC0g5pi+56S65bey5Yqg6L2955qE6aKY55uuXG4gIGlmIChzdGF0ZS5sb2FkZWRRdWVzdGlvbnMubGVuZ3RoID4gMCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICB7Lyog5rWB5byP5Yqg6L296L+b5bqm5o+Q56S6ICovfVxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9e2Bib3JkZXItMiAke3N0YXRlLnN0cmVhbUxvYWRpbmcgPyAnYmctYmx1ZS01MCBib3JkZXItYmx1ZS0yMDAnIDogJ2JnLWdyZWVuLTUwIGJvcmRlci1ncmVlbi0yMDAnfWB9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC14bCBtci0zICR7c3RhdGUuc3RyZWFtTG9hZGluZyA/ICd0ZXh0LWJsdWUtNTAwJyA6ICd0ZXh0LWdyZWVuLTUwMCd9YH0+XG4gICAgICAgICAgICAgICAge3N0YXRlLnN0cmVhbUxvYWRpbmcgPyAn8J+UhCcgOiAn4pyFJ31cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1zZW1pYm9sZCAke3N0YXRlLnN0cmVhbUxvYWRpbmcgPyAndGV4dC1ibHVlLTkwMCcgOiAndGV4dC1ncmVlbi05MDAnfWB9PlxuICAgICAgICAgICAgICAgICAge3N0YXRlLnN0cmVhbUxvYWRpbmcgPyAn6Zeu5Y2355Sf5oiQ5LitLi4uJyA6ICfpl67ljbfnlJ/miJDlrozmiJAnfVxuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSAke3N0YXRlLnN0cmVhbUxvYWRpbmcgPyAndGV4dC1ibHVlLTcwMCcgOiAndGV4dC1ncmVlbi03MDAnfWB9PlxuICAgICAgICAgICAgICAgICAg5bey55Sf5oiQIHtzdGF0ZS5sb2FkZWRRdWVzdGlvbnMubGVuZ3RofSAvIHtzdGF0ZS50b3RhbEV4cGVjdGVkfSDpgZPpopjnm65cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAge3N0YXRlLnN0cmVhbUxvYWRpbmcgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWJsdWUtMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7c3RhdGUuY3VycmVudFByb2dyZXNzLnBlcmNlbnRhZ2V9JWAgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzdGF0ZS5jdXJyZW50UHJvZ3Jlc3MucGVyY2VudGFnZX0lIOWujOaIkFxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0IHRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXY+54mI5pysOiB7dmVyc2lvbn08L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PuW3suWKoOi9vToge3N0YXRlLmxvYWRlZFF1ZXN0aW9ucy5sZW5ndGh9IOmimDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIOS9v+eUqOa1geW8j+a4suafk+WZqCAqL31cbiAgICAgICAgPFN0cmVhbVF1ZXN0aW9ubmFpcmVSZW5kZXJlclxuICAgICAgICAgIHF1ZXN0aW9ucz17c3RhdGUubG9hZGVkUXVlc3Rpb25zfVxuICAgICAgICAgIGlzTG9hZGluZz17c3RhdGUuc3RyZWFtTG9hZGluZ31cbiAgICAgICAgICBwcm9ncmVzcz17c3RhdGUuY3VycmVudFByb2dyZXNzfVxuICAgICAgICAgIG9uQW5zd2VyQ2hhbmdlPXsocXVlc3Rpb25JZCwgYW5zd2VyKSA9PiB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn562U5qGI5Y+Y5pu0OicsIHF1ZXN0aW9uSWQsIGFuc3dlcilcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uQ29tcGxldGU9eyhyZXNwb25zZXMpID0+IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfpl67ljbflrozmiJA6JywgcmVzcG9uc2VzKVxuICAgICAgICAgICAgaWYgKGhhbmRsZVF1ZXN0aW9ubmFpcmVDb21wbGV0ZSkge1xuICAgICAgICAgICAgICAvLyDovazmjaLkuLrml6fmoLzlvI/lhbzlrrlcbiAgICAgICAgICAgICAgY29uc3QgY29udmVydGVkUmVzcG9uc2VzID0gcmVzcG9uc2VzLm1hcChyID0+ICh7XG4gICAgICAgICAgICAgICAgcXVlc3Rpb25JZDogci5xdWVzdGlvbklkLFxuICAgICAgICAgICAgICAgIGFuc3dlcjogci5hbnN3ZXIsXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiByLnRpbWVzdGFtcFxuICAgICAgICAgICAgICB9KSlcbiAgICAgICAgICAgICAgaGFuZGxlUXVlc3Rpb25uYWlyZUNvbXBsZXRlKGNvbnZlcnRlZFJlc3BvbnNlcyBhcyBhbnkpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZVwiXG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyDkvKDnu5/nmoTlrozmlbTliqDovb3miJDlip/nirbmgIHvvIjkv53nlZnlhbzlrrnmgKfvvIlcbiAgaWYgKHN0YXRlLmNvbmZpZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICA8UXVlc3Rpb25uYWlyZVJlbmRlcmVyXG4gICAgICAgICAgY29uZmlnPXtzdGF0ZS5jb25maWd9XG4gICAgICAgICAgb25Db21wbGV0ZT17aGFuZGxlUXVlc3Rpb25uYWlyZUNvbXBsZXRlfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlXCJcbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiBudWxsXG59XG5cbi8vIOm7mOiupOWvvOWHuuS7peS/neaMgeWFvOWuueaAp1xuZXhwb3J0IGRlZmF1bHQgUXVlc3Rpb25uYWlyZUxvYWRlclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIkNhcmQiLCJCdXR0b24iLCJRdWVzdGlvbm5haXJlUmVuZGVyZXIiLCJTdHJlYW1RdWVzdGlvbm5haXJlUmVuZGVyZXIiLCJRdWVzdGlvbm5haXJlTG9hZGVyIiwidmVyc2lvbiIsIm9yZ2FuaXphdGlvbklkIiwib25Db21wbGV0ZSIsIm9uQ29uZmlnTG9hZGVkIiwic3RhdGUiLCJzZXRTdGF0ZSIsImNvbmZpZyIsImxvYWRpbmciLCJlcnJvciIsInJldHJ5Q291bnQiLCJzdHJlYW1Mb2FkaW5nIiwibG9hZGVkUXVlc3Rpb25zIiwidG90YWxFeHBlY3RlZCIsImN1cnJlbnRQcm9ncmVzcyIsImNvbXBsZXRlZCIsInRvdGFsIiwicGVyY2VudGFnZSIsInF1ZXN0aW9ubmFpcmVJZCIsInRyYW5zZm9ybUFwaURhdGEiLCJhcGlEYXRhIiwicXVlc3Rpb25uYWlyZUNvbmZpZyIsInRvdGFsX3F1ZXN0aW9ucyIsInF1ZXN0aW9ucyIsImxlbmd0aCIsImRpbWVuc2lvbnMiLCJTRiIsIklUIiwiTVYiLCJBRCIsIkFycmF5IiwiaXNBcnJheSIsImZvckVhY2giLCJxIiwiZGltZW5zaW9uS2V5IiwiZGltZW5zaW9uIiwiY29uc29sZSIsIndhcm4iLCJjb25maWdRdWVzdGlvbiIsImlkIiwic3ViX2RpbWVuc2lvbiIsInN1YmRpbWVuc2lvbiIsInR5cGUiLCJ0ZXh0IiwidGl0bGUiLCJvcHRpb25zIiwibWFwIiwib3B0IiwiaW5kZXgiLCJ2YWx1ZSIsInNjb3JlIiwic2NvcmluZyIsImRpbWVuc2lvbl93ZWlnaHQiLCJ3ZWlnaHQiLCJzdWJfZGltZW5zaW9uX3dlaWdodCIsIm9wdGlvbl9zY29yZXMiLCJfIiwicmV2ZXJzZV9zY29yaW5nIiwicHVzaCIsImxvYWRRdWVzdGlvbm5haXJlU3RyZWFtbHkiLCJwcmV2IiwibG9nIiwiZmlyc3RCYXRjaFJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvcmdhbml6YXRpb25UeXBlIiwiYmF0Y2hTaXplIiwib2siLCJFcnJvciIsInN0YXR1cyIsImZpcnN0QmF0Y2giLCJqc29uIiwic3VjY2VzcyIsImRhdGEiLCJwcm9ncmVzcyIsImxvYWRSZW1haW5pbmdRdWVzdGlvbnMiLCJlcnIiLCJtZXNzYWdlIiwiaW5pdGlhbEJhdGNoIiwibmV4dEJhdGNoIiwic2hvdWxkQ29udGludWVMb2FkaW5nIiwiYmF0Y2hDb3VudCIsIm1heEJhdGNoZXMiLCJiYXRjaERhdGEiLCJzdGFydEluZGV4IiwicmVzcG9uc2UiLCJxdWVzdGlvbkNvdW50IiwiaGFzTmV4dEJhdGNoIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwibG9hZFF1ZXN0aW9ubmFpcmVDb25maWciLCJjb250cm9sbGVyIiwiQWJvcnRDb250cm9sbGVyIiwidGltZW91dElkIiwiYWJvcnQiLCJzaWduYWwiLCJjbGVhclRpbWVvdXQiLCJzdGF0dXNUZXh0IiwicmVzdWx0IiwibmFtZSIsImhhbmRsZVF1ZXN0aW9ubmFpcmVDb21wbGV0ZSIsInJlc3BvbnNlcyIsImhhbmRsZVJldHJ5IiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImgzIiwib25DbGljayIsImRpc2FibGVkIiwidmFyaWFudCIsIndpbmRvdyIsImxvY2F0aW9uIiwicmVsb2FkIiwic3R5bGUiLCJ3aWR0aCIsImlzTG9hZGluZyIsIm9uQW5zd2VyQ2hhbmdlIiwicXVlc3Rpb25JZCIsImFuc3dlciIsImNvbnZlcnRlZFJlc3BvbnNlcyIsInIiLCJ0aW1lc3RhbXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/QuestionnaireLoader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/questionnaire/QuestionnaireProgress.tsx":
/*!****************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireProgress.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireProgress: function() { return /* binding */ QuestionnaireProgress; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressBar */ \"(app-pages-browser)/./src/components/questionnaire/ProgressBar.tsx\");\n/* harmony import */ var _QuestionCounter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuestionCounter */ \"(app-pages-browser)/./src/components/questionnaire/QuestionCounter.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireProgress auto */ \n\n\n\n/**\n * 问卷进度组件\n * 显示当前进度和问题计数\n */ const QuestionnaireProgress = (param)=>{\n    let { current, total, progress, dimension = \"SF\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionCounter__WEBPACK_IMPORTED_MODULE_3__.QuestionCounter, {\n                current: current,\n                total: total,\n                dimension: dimension\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_2__.ProgressBar, {\n                progress: progress\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireProgress.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_c = QuestionnaireProgress;\nvar _c;\n$RefreshReg$(_c, \"QuestionnaireProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3F1ZXN0aW9ubmFpcmUvUXVlc3Rpb25uYWlyZVByb2dyZXNzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXlCO0FBQ2tCO0FBQ1E7QUFTbkQ7OztDQUdDLEdBQ00sTUFBTUcsd0JBQThEO1FBQUMsRUFDMUVDLE9BQU8sRUFDUEMsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLFlBQVksSUFBSSxFQUNqQjtJQUNDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ1AsNkRBQWVBO2dCQUFDRSxTQUFTQTtnQkFBU0MsT0FBT0E7Z0JBQU9FLFdBQVdBOzs7Ozs7MEJBQzVELDhEQUFDTixxREFBV0E7Z0JBQUNLLFVBQVVBOzs7Ozs7Ozs7Ozs7QUFHN0IsRUFBQztLQVpZSCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9xdWVzdGlvbm5haXJlL1F1ZXN0aW9ubmFpcmVQcm9ncmVzcy50c3g/MGIzOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgUHJvZ3Jlc3NCYXIgfSBmcm9tICcuL1Byb2dyZXNzQmFyJ1xuaW1wb3J0IHsgUXVlc3Rpb25Db3VudGVyIH0gZnJvbSAnLi9RdWVzdGlvbkNvdW50ZXInXG5cbmludGVyZmFjZSBRdWVzdGlvbm5haXJlUHJvZ3Jlc3NQcm9wcyB7XG4gIGN1cnJlbnQ6IG51bWJlclxuICB0b3RhbDogbnVtYmVyXG4gIHByb2dyZXNzOiBudW1iZXJcbiAgZGltZW5zaW9uPzogJ1NGJyB8ICdJVCcgfCAnTVYnIHwgJ0FEJ1xufVxuXG4vKipcbiAqIOmXruWNt+i/m+W6pue7hOS7tlxuICog5pi+56S65b2T5YmN6L+b5bqm5ZKM6Zeu6aKY6K6h5pWwXG4gKi9cbmV4cG9ydCBjb25zdCBRdWVzdGlvbm5haXJlUHJvZ3Jlc3M6IFJlYWN0LkZDPFF1ZXN0aW9ubmFpcmVQcm9ncmVzc1Byb3BzPiA9ICh7XG4gIGN1cnJlbnQsXG4gIHRvdGFsLFxuICBwcm9ncmVzcyxcbiAgZGltZW5zaW9uID0gJ1NGJ1xufSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICA8UXVlc3Rpb25Db3VudGVyIGN1cnJlbnQ9e2N1cnJlbnR9IHRvdGFsPXt0b3RhbH0gZGltZW5zaW9uPXtkaW1lbnNpb259IC8+XG4gICAgICA8UHJvZ3Jlc3NCYXIgcHJvZ3Jlc3M9e3Byb2dyZXNzfSAvPlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwiUHJvZ3Jlc3NCYXIiLCJRdWVzdGlvbkNvdW50ZXIiLCJRdWVzdGlvbm5haXJlUHJvZ3Jlc3MiLCJjdXJyZW50IiwidG90YWwiLCJwcm9ncmVzcyIsImRpbWVuc2lvbiIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/QuestionnaireProgress.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/questionnaire/QuestionnaireRenderer.tsx":
/*!****************************************************************!*\
  !*** ./src/components/questionnaire/QuestionnaireRenderer.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionnaireRenderer: function() { return /* binding */ QuestionnaireRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useQuestionnaire__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useQuestionnaire */ \"(app-pages-browser)/./src/hooks/useQuestionnaire.ts\");\n/* harmony import */ var _QuestionDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QuestionDisplay */ \"(app-pages-browser)/./src/components/questionnaire/QuestionDisplay.tsx\");\n/* harmony import */ var _QuestionnaireProgress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuestionnaireProgress */ \"(app-pages-browser)/./src/components/questionnaire/QuestionnaireProgress.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Loading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Loading */ \"(app-pages-browser)/./src/components/ui/Loading.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionnaireRenderer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\n * 问卷渲染器主组件\n * 负责协调各个子组件，保持简洁\n */ const QuestionnaireRenderer = (param)=>{\n    let { config, initialResponses = [], onComplete, onSave, className } = param;\n    _s();\n    const { currentQuestion, currentQuestionIndex, responses, errors, isLoading, isFirst, isLast, handleNext, handlePrevious, updateResponse, progress } = (0,_hooks_useQuestionnaire__WEBPACK_IMPORTED_MODULE_2__.useQuestionnaire)({\n        config,\n        initialResponses,\n        onComplete,\n        onSave\n    });\n    // 获取当前问题的回答值\n    const getCurrentValue = ()=>{\n        if (!currentQuestion) return undefined;\n        const response = responses.find((r)=>r.questionId === currentQuestion.id);\n        return response === null || response === void 0 ? void 0 : response.answer;\n    };\n    // 处理答案更新\n    const handleAnswerChange = (value)=>{\n        if (currentQuestion) {\n            updateResponse(currentQuestion.id, value);\n        }\n    };\n    // 获取当前问题的维度\n    const getCurrentDimension = ()=>{\n        return (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.dimension) || \"SF\";\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Loading__WEBPACK_IMPORTED_MODULE_6__.Loading, {\n                size: \"lg\",\n                text: \"正在提交问卷...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionnaireProgress__WEBPACK_IMPORTED_MODULE_4__.QuestionnaireProgress, {\n                current: currentQuestionIndex + 1,\n                total: config.total_questions,\n                progress: progress,\n                dimension: getCurrentDimension()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6 mt-4\",\n                children: [\n                    currentQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuestionDisplay__WEBPACK_IMPORTED_MODULE_3__.QuestionDisplay, {\n                        question: currentQuestion,\n                        value: getCurrentValue(),\n                        onChange: handleAnswerChange,\n                        error: errors[currentQuestion.id]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: \"问题加载中...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8 pt-6 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrevious,\n                                disabled: isFirst || isLoading,\n                                className: \"px-6 py-2\",\n                                children: \"上一题\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"第 \",\n                                    currentQuestionIndex + 1,\n                                    \" 题，共 \",\n                                    config.total_questions,\n                                    \" 题\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: handleNext,\n                                disabled: isLoading,\n                                className: \"px-6 py-2\",\n                                children: isLast ? \"提交问卷\" : \"下一题\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    currentQuestion && errors[currentQuestion.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-red-500\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-700 text-sm\",\n                                    children: errors[currentQuestion.id]\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/QuestionnaireRenderer.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QuestionnaireRenderer, \"YAE0oOyVoveIw+vhWKGjTUCwNn0=\", false, function() {\n    return [\n        _hooks_useQuestionnaire__WEBPACK_IMPORTED_MODULE_2__.useQuestionnaire\n    ];\n});\n_c = QuestionnaireRenderer;\nvar _c;\n$RefreshReg$(_c, \"QuestionnaireRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/QuestionnaireRenderer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/questionnaire/StreamQuestionnaireRenderer.tsx ***!
  \**********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamQuestionnaireRenderer: function() { return /* binding */ StreamQuestionnaireRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamQuestionnaireRenderer auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * 流式问卷渲染器\n * 支持动态加载和显示题目\n */ const StreamQuestionnaireRenderer = (param)=>{\n    let { questions, isLoading, progress, onAnswerChange, onComplete, className = \"\" } = param;\n    _s();\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [wasWaitingForMore, setWasWaitingForMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /**\n   * 监听题目数量变化，自动导航到新加载的题目\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const isLastQuestion = currentQuestionIndex === questions.length - 1;\n        const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total;\n        // 如果之前在等待更多题目，现在有新题目了，自动进入下一题\n        if (wasWaitingForMore && !shouldWaitForMore && questions.length > currentQuestionIndex + 1) {\n            console.log(\"检测到新题目加载，自动进入下一题\");\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n            setWasWaitingForMore(false);\n        } else if (shouldWaitForMore && !wasWaitingForMore) {\n            // 开始等待更多题目\n            setWasWaitingForMore(true);\n        } else if (!shouldWaitForMore && wasWaitingForMore) {\n            // 停止等待\n            setWasWaitingForMore(false);\n        }\n    }, [\n        questions.length,\n        isLoading,\n        currentQuestionIndex,\n        progress.total,\n        wasWaitingForMore\n    ]);\n    /**\n   * 处理答案变更\n   */ const handleAnswerChange = (questionId, answer)=>{\n        const newAnswers = {\n            ...answers,\n            [questionId]: answer\n        };\n        setAnswers(newAnswers);\n        if (onAnswerChange) {\n            onAnswerChange(questionId, answer);\n        }\n    };\n    /**\n   * 下一题\n   */ const handleNext = ()=>{\n        if (currentQuestionIndex < questions.length - 1) {\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n        }\n    };\n    /**\n   * 上一题\n   */ const handlePrevious = ()=>{\n        if (currentQuestionIndex > 0) {\n            setCurrentQuestionIndex(currentQuestionIndex - 1);\n        }\n    };\n    /**\n   * 提交问卷\n   */ const handleSubmit = ()=>{\n        const responses = Object.entries(answers).map((param)=>{\n            let [questionId, answer] = param;\n            return {\n                questionId,\n                answer,\n                timestamp: new Date()\n            };\n        });\n        if (onComplete) {\n            onComplete(responses);\n        }\n    };\n    // 如果没有题目，显示等待状态\n    if (questions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"p-8 text-center \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"正在生成第一批题目...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentQuestion = questions[currentQuestionIndex];\n    const isLastQuestion = currentQuestionIndex === questions.length - 1;\n    const canProceed = answers[currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.id];\n    // 判断是否应该显示\"等待更多题目\"\n    const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"题目进度\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    currentQuestionIndex + 1,\n                                    \" / \",\n                                    questions.length,\n                                    isLoading && \" (共\".concat(progress.total, \"题)\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat((currentQuestionIndex + 1) / Math.max(questions.length, progress.total) * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-blue-600 mt-1\",\n                        children: [\n                            \"正在生成更多题目... (\",\n                            progress.percentage,\n                            \"% 完成)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-600 font-medium\",\n                                            children: [\n                                                currentQuestion.dimension,\n                                                \" 维度\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"第 \",\n                                                currentQuestion.order,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 leading-relaxed\",\n                                    children: currentQuestion.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQuestion.options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            name: currentQuestion.id,\n                                            value: option.score,\n                                            checked: answers[currentQuestion.id] === option.score,\n                                            onChange: ()=>handleAnswerChange(currentQuestion.id, option.score),\n                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-gray-700 flex-1\",\n                                            children: option.text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                option.score,\n                                                \"分\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, option.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePrevious,\n                                    disabled: currentQuestionIndex === 0,\n                                    variant: \"outline\",\n                                    children: \"上一题\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: !isLastQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleNext,\n                                        disabled: !canProceed,\n                                        children: \"下一题\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, undefined) : shouldWaitForMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        disabled: true,\n                                        className: \"bg-blue-500 hover:bg-blue-600\",\n                                        children: [\n                                            \"等待更多题目... (\",\n                                            questions.length,\n                                            \"/\",\n                                            progress.total,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleSubmit,\n                                        disabled: !canProceed,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: \"提交问卷\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, undefined),\n            Object.keys(answers).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: [\n                            \"已回答题目 (\",\n                            Object.keys(answers).length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-10 gap-2\",\n                        children: questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentQuestionIndex(index),\n                                className: \"\\n                  w-8 h-8 rounded text-xs font-medium transition-colors\\n                  \".concat(answers[question.id] ? \"bg-green-100 text-green-700 border border-green-300\" : \"bg-gray-100 text-gray-500 border border-gray-300\", \"\\n                  \").concat(index === currentQuestionIndex ? \"ring-2 ring-blue-500\" : \"hover:bg-gray-200\", \"\\n                \"),\n                                children: index + 1\n                            }, question.id, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StreamQuestionnaireRenderer, \"gdNJ62+wgu5Afx83W3/jL1p7hPY=\");\n_c = StreamQuestionnaireRenderer;\nvar _c;\n$RefreshReg$(_c, \"StreamQuestionnaireRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: function() { return /* binding */ Button; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Button = (param)=>{\n    let { children, onClick, disabled = false, variant = \"default\", size = \"md\", className = \"\", type = \"button\" } = param;\n    const baseClasses = \"inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    const variantClasses = {\n        default: \"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500\",\n        outline: \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500\",\n        ghost: \"text-gray-700 hover:bg-gray-100 focus:ring-blue-500\"\n    };\n    const sizeClasses = {\n        sm: \"px-3 py-1.5 text-sm\",\n        md: \"px-4 py-2 text-sm\",\n        lg: \"px-6 py-3 text-base\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        onClick: onClick,\n        disabled: disabled,\n        className: \"\".concat(baseClasses, \" \").concat(variantClasses[variant], \" \").concat(sizeClasses[size], \" \").concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Button.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: function() { return /* binding */ Card; },\n/* harmony export */   CardContent: function() { return /* binding */ CardContent; },\n/* harmony export */   CardDescription: function() { return /* binding */ CardDescription; },\n/* harmony export */   CardHeader: function() { return /* binding */ CardHeader; },\n/* harmony export */   CardTitle: function() { return /* binding */ CardTitle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Card = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 shadow-sm \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Card;\nconst CardHeader = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = CardHeader;\nconst CardContent = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-6 py-4 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = CardContent;\nconst CardTitle = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n_c3 = CardTitle;\nconst CardDescription = (param)=>{\n    let { children, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: \"text-sm text-gray-600 mt-1 \".concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_c4 = CardDescription;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Card\");\n$RefreshReg$(_c1, \"CardHeader\");\n$RefreshReg$(_c2, \"CardContent\");\n$RefreshReg$(_c3, \"CardTitle\");\n$RefreshReg$(_c4, \"CardDescription\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Loading.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/Loading.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Loading: function() { return /* binding */ Loading; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Loading = (param)=>{\n    let { size = \"md\", text = \"加载中...\", className = \"\" } = param;\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        md: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full border-b-2 border-blue-600 \".concat(sizeClasses[size], \" mb-2\")\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 text-sm\",\n                children: text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n                lineNumber: 23,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/ui/Loading.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXlCO0FBUWxCLE1BQU1DLFVBQWtDO1FBQUMsRUFDOUNDLE9BQU8sSUFBSSxFQUNYQyxPQUFPLFFBQVEsRUFDZkMsWUFBWSxFQUFFLEVBQ2Y7SUFDQyxNQUFNQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVcsNkNBQXVELE9BQVZBOzswQkFDM0QsOERBQUNLO2dCQUFJTCxXQUFXLHdEQUEwRSxPQUFsQkMsV0FBVyxDQUFDSCxLQUFLLEVBQUM7Ozs7OztZQUN6RkMsc0JBQVEsOERBQUNPO2dCQUFFTixXQUFVOzBCQUF5QkQ7Ozs7Ozs7Ozs7OztBQUdyRCxFQUFDO0tBakJZRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy91aS9Mb2FkaW5nLnRzeD9kZmQyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIExvYWRpbmdQcm9wcyB7XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZydcbiAgdGV4dD86IHN0cmluZ1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGNvbnN0IExvYWRpbmc6IFJlYWN0LkZDPExvYWRpbmdQcm9wcz4gPSAoeyBcbiAgc2l6ZSA9ICdtZCcsIFxuICB0ZXh0ID0gJ+WKoOi9veS4rS4uLicsIFxuICBjbGFzc05hbWUgPSAnJyBcbn0pID0+IHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206ICdoLTQgdy00JyxcbiAgICBtZDogJ2gtOCB3LTgnLCBcbiAgICBsZzogJ2gtMTIgdy0xMidcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciAke2NsYXNzTmFtZX1gfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCAke3NpemVDbGFzc2VzW3NpemVdfSBtYi0yYH0+PC9kaXY+XG4gICAgICB7dGV4dCAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc21cIj57dGV4dH08L3A+fVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMb2FkaW5nIiwic2l6ZSIsInRleHQiLCJjbGFzc05hbWUiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsImRpdiIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Loading.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useQuestionnaire.ts":
/*!***************************************!*\
  !*** ./src/hooks/useQuestionnaire.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuestionnaire: function() { return /* binding */ useQuestionnaire; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useQuestionnaire auto */ \n/**\n * 问卷逻辑管理Hook\n * 将复杂的状态管理逻辑从组件中抽离\n */ function useQuestionnaire(param) {\n    let { config, initialResponses, onComplete, onSave } = param;\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [responses, setResponses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // 从QuestionnaireConfig中提取所有问题\n    const allQuestions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const questions = [];\n        Object.values(config.dimensions).forEach((dimension)=>{\n            questions.push(...dimension.questions);\n        });\n        return questions;\n    }, [\n        config\n    ]);\n    // 计算派生状态\n    const currentQuestion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>allQuestions[currentQuestionIndex], [\n        allQuestions,\n        currentQuestionIndex\n    ]);\n    const isFirst = currentQuestionIndex === 0;\n    const isLast = currentQuestionIndex === allQuestions.length - 1;\n    const progress = (currentQuestionIndex + 1) / allQuestions.length * 100;\n    // 更新回答 - 修复参数类型\n    const updateResponse = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((questionId, answer)=>{\n        setResponses((prev)=>{\n            const existing = prev.find((r)=>r.questionId === questionId);\n            if (existing) {\n                return prev.map((r)=>r.questionId === questionId ? {\n                        ...r,\n                        answer\n                    } : r);\n            }\n            return [\n                ...prev,\n                {\n                    questionId,\n                    answer\n                }\n            ];\n        });\n        // 清除错误\n        setErrors((prev)=>({\n                ...prev,\n                [questionId]: \"\"\n            }));\n    }, []);\n    // 验证当前问题\n    const validateCurrentQuestion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!currentQuestion) return true;\n        const response = responses.find((r)=>r.questionId === currentQuestion.id);\n        if (!response || response.answer === undefined || response.answer === null || response.answer === \"\") {\n            setErrors((prev)=>({\n                    ...prev,\n                    [currentQuestion.id]: \"请回答此问题后再继续\"\n                }));\n            return false;\n        }\n        return true;\n    }, [\n        currentQuestion,\n        responses\n    ]);\n    // 下一题\n    const handleNext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!validateCurrentQuestion()) return;\n        if (isLast) {\n            setIsLoading(true);\n            try {\n                // 转换为QuestionnaireResponse格式\n                const questionnaireResponse = {\n                    id: \"response_\".concat(Date.now()),\n                    questionnaireId: \"questionnaire_1\",\n                    responses: responses.map((r)=>{\n                        // 找到对应的问题以获取dimension\n                        const question = allQuestions.find((q)=>q.id === r.questionId);\n                        return {\n                            id: \"resp_\".concat(r.questionId),\n                            assessmentId: \"assessment_1\",\n                            questionId: r.questionId,\n                            respondentId: \"user_1\",\n                            answer: r.answer,\n                            dimension: (question === null || question === void 0 ? void 0 : question.dimension) || \"SF\",\n                            createdAt: new Date(),\n                            updatedAt: new Date()\n                        };\n                    }),\n                    metadata: {\n                        startTime: new Date().toISOString(),\n                        endTime: new Date().toISOString(),\n                        totalTimeSpent: 0,\n                        submittedAt: new Date().toISOString(),\n                        version: config.version,\n                        completionRate: 100\n                    },\n                    status: \"completed\",\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                await onComplete([\n                    questionnaireResponse\n                ]);\n            } catch (error) {\n                console.error(\"提交问卷失败:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        } else {\n            setCurrentQuestionIndex((prev)=>prev + 1);\n        }\n    }, [\n        validateCurrentQuestion,\n        isLast,\n        responses,\n        onComplete\n    ]);\n    // 上一题\n    const handlePrevious = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isFirst) {\n            setCurrentQuestionIndex((prev)=>prev - 1);\n        }\n    }, [\n        isFirst\n    ]);\n    return {\n        currentQuestion,\n        currentQuestionIndex,\n        responses,\n        errors,\n        isLoading,\n        isFirst,\n        isLast,\n        progress,\n        handleNext,\n        handlePrevious,\n        updateResponse\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useQuestionnaire.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe[incompatible-use] This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement$1(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement$1(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner$1.current && self && ReactCurrentOwner$1.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner$1.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner$1.current, props);\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    if (type.$$typeof === REACT_CLIENT_REFERENCE) {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV$1(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (hasOwnProperty.call(props, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(props).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV = jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/OTA3MSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fapple%2FDocuments%2F2.1%20AI%20Journey%2FCursor_projects%2Focti_test%2Fsrc%2Fapp%2Fquestionnaire%2Fpage.tsx&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);