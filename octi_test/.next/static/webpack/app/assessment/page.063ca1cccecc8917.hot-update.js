"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/assessment/page",{

/***/ "(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/questionnaire/StreamQuestionnaireRenderer.tsx ***!
  \**********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamQuestionnaireRenderer: function() { return /* binding */ StreamQuestionnaireRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamQuestionnaireRenderer auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * 流式问卷渲染器\n * 支持动态加载和显示题目\n */ const StreamQuestionnaireRenderer = (param)=>{\n    let { questions, isLoading, progress, onAnswerChange, onComplete, className = \"\" } = param;\n    _s();\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    /**\n   * 处理答案变更\n   */ const handleAnswerChange = (questionId, answer)=>{\n        const newAnswers = {\n            ...answers,\n            [questionId]: answer\n        };\n        setAnswers(newAnswers);\n        if (onAnswerChange) {\n            onAnswerChange(questionId, answer);\n        }\n    };\n    /**\n   * 下一题\n   */ const handleNext = ()=>{\n        if (currentQuestionIndex < questions.length - 1) {\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n        }\n    };\n    /**\n   * 上一题\n   */ const handlePrevious = ()=>{\n        if (currentQuestionIndex > 0) {\n            setCurrentQuestionIndex(currentQuestionIndex - 1);\n        }\n    };\n    /**\n   * 提交问卷\n   */ const handleSubmit = ()=>{\n        const responses = Object.entries(answers).map((param)=>{\n            let [questionId, answer] = param;\n            return {\n                questionId,\n                answer,\n                timestamp: new Date()\n            };\n        });\n        if (onComplete) {\n            onComplete(responses);\n        }\n    };\n    // 如果没有题目，显示等待状态\n    if (questions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"p-8 text-center \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"正在生成第一批题目...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentQuestion = questions[currentQuestionIndex];\n    const isLastQuestion = currentQuestionIndex === questions.length - 1;\n    const canProceed = answers[currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.id];\n    // 判断是否应该显示\"等待更多题目\"\n    const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"题目进度\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    currentQuestionIndex + 1,\n                                    \" / \",\n                                    questions.length,\n                                    isLoading && \" (共\".concat(progress.total, \"题)\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat((currentQuestionIndex + 1) / Math.max(questions.length, progress.total) * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-blue-600 mt-1\",\n                        children: [\n                            \"正在生成更多题目... (\",\n                            progress.percentage,\n                            \"% 完成)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined),\n            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-600 font-medium\",\n                                            children: [\n                                                currentQuestion.dimension,\n                                                \" 维度\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"第 \",\n                                                currentQuestion.order,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 leading-relaxed\",\n                                    children: currentQuestion.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQuestion.options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            name: currentQuestion.id,\n                                            value: option.score,\n                                            checked: answers[currentQuestion.id] === option.score,\n                                            onChange: ()=>handleAnswerChange(currentQuestion.id, option.score),\n                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-gray-700 flex-1\",\n                                            children: option.text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                option.score,\n                                                \"分\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, option.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePrevious,\n                                    disabled: currentQuestionIndex === 0,\n                                    variant: \"outline\",\n                                    children: \"上一题\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: !isLastQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleNext,\n                                        disabled: !canProceed,\n                                        children: \"下一题\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 19\n                                    }, undefined) : shouldWaitForMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        disabled: true,\n                                        className: \"bg-blue-500 hover:bg-blue-600\",\n                                        children: [\n                                            \"等待更多题目... (\",\n                                            questions.length,\n                                            \"/\",\n                                            progress.total,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleSubmit,\n                                        disabled: !canProceed,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: \"提交问卷\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, undefined),\n            Object.keys(answers).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: [\n                            \"已回答题目 (\",\n                            Object.keys(answers).length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-10 gap-2\",\n                        children: questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentQuestionIndex(index),\n                                className: \"\\n                  w-8 h-8 rounded text-xs font-medium transition-colors\\n                  \".concat(answers[question.id] ? \"bg-green-100 text-green-700 border border-green-300\" : \"bg-gray-100 text-gray-500 border border-gray-300\", \"\\n                  \").concat(index === currentQuestionIndex ? \"ring-2 ring-blue-500\" : \"hover:bg-gray-200\", \"\\n                \"),\n                                children: index + 1\n                            }, question.id, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StreamQuestionnaireRenderer, \"pc6S0n4A5jUwejecqoHzsj7uDEg=\");\n_c = StreamQuestionnaireRenderer;\nvar _c;\n$RefreshReg$(_c, \"StreamQuestionnaireRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3F1ZXN0aW9ubmFpcmUvU3RyZWFtUXVlc3Rpb25uYWlyZVJlbmRlcmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVrRDtBQUNQO0FBQ0k7QUFnQy9DOzs7Q0FHQyxHQUNNLE1BQU1JLDhCQUEwRTtRQUFDLEVBQ3RGQyxTQUFTLEVBQ1RDLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxjQUFjLEVBQ2RDLFVBQVUsRUFDVkMsWUFBWSxFQUFFLEVBQ2Y7O0lBQ0MsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdYLCtDQUFRQSxDQUFzQixDQUFDO0lBQzdELE1BQU0sQ0FBQ1ksc0JBQXNCQyx3QkFBd0IsR0FBR2IsK0NBQVFBLENBQUM7SUFFakU7O0dBRUMsR0FDRCxNQUFNYyxxQkFBcUIsQ0FBQ0MsWUFBb0JDO1FBQzlDLE1BQU1DLGFBQWE7WUFBRSxHQUFHUCxPQUFPO1lBQUUsQ0FBQ0ssV0FBVyxFQUFFQztRQUFPO1FBQ3RETCxXQUFXTTtRQUVYLElBQUlWLGdCQUFnQjtZQUNsQkEsZUFBZVEsWUFBWUM7UUFDN0I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUUsYUFBYTtRQUNqQixJQUFJTix1QkFBdUJSLFVBQVVlLE1BQU0sR0FBRyxHQUFHO1lBQy9DTix3QkFBd0JELHVCQUF1QjtRQUNqRDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNUSxpQkFBaUI7UUFDckIsSUFBSVIsdUJBQXVCLEdBQUc7WUFDNUJDLHdCQUF3QkQsdUJBQXVCO1FBQ2pEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1TLGVBQWU7UUFDbkIsTUFBTUMsWUFBWUMsT0FBT0MsT0FBTyxDQUFDZCxTQUFTZSxHQUFHLENBQUM7Z0JBQUMsQ0FBQ1YsWUFBWUMsT0FBTzttQkFBTTtnQkFDdkVEO2dCQUNBQztnQkFDQVUsV0FBVyxJQUFJQztZQUNqQjs7UUFFQSxJQUFJbkIsWUFBWTtZQUNkQSxXQUFXYztRQUNiO0lBQ0Y7SUFFQSxnQkFBZ0I7SUFDaEIsSUFBSWxCLFVBQVVlLE1BQU0sS0FBSyxHQUFHO1FBQzFCLHFCQUNFLDhEQUFDbEIscURBQUlBO1lBQUNRLFdBQVcsbUJBQTZCLE9BQVZBO3NCQUNsQyw0RUFBQ21CO2dCQUFJbkIsV0FBVTswQkFDYiw0RUFBQ21CO29CQUFJbkIsV0FBVTs7c0NBQ2IsOERBQUNvQjtzQ0FBRTs7Ozs7O3NDQUNILDhEQUFDRDs0QkFBSW5CLFdBQVU7c0NBQ2IsNEVBQUNtQjtnQ0FBSW5CLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU0zQjtJQUVBLE1BQU1xQixrQkFBa0IxQixTQUFTLENBQUNRLHFCQUFxQjtJQUN2RCxNQUFNbUIsaUJBQWlCbkIseUJBQXlCUixVQUFVZSxNQUFNLEdBQUc7SUFDbkUsTUFBTWEsYUFBYXRCLE9BQU8sQ0FBQ29CLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCRyxFQUFFLENBQUM7SUFFL0MsbUJBQW1CO0lBQ25CLE1BQU1DLG9CQUFvQkgsa0JBQWtCMUIsYUFBYUQsVUFBVWUsTUFBTSxHQUFHYixTQUFTNkIsS0FBSztJQUUxRixxQkFDRSw4REFBQ1A7UUFBSW5CLFdBQVcsYUFBdUIsT0FBVkE7OzBCQUUzQiw4REFBQ1IscURBQUlBO2dCQUFDUSxXQUFVOztrQ0FDZCw4REFBQ21CO3dCQUFJbkIsV0FBVTs7MENBQ2IsOERBQUMyQjtnQ0FBSzNCLFdBQVU7MENBQW9DOzs7Ozs7MENBR3BELDhEQUFDMkI7Z0NBQUszQixXQUFVOztvQ0FDYkcsdUJBQXVCO29DQUFFO29DQUFJUixVQUFVZSxNQUFNO29DQUM3Q2QsYUFBYSxNQUFxQixPQUFmQyxTQUFTNkIsS0FBSyxFQUFDOzs7Ozs7Ozs7Ozs7O2tDQUd2Qyw4REFBQ1A7d0JBQUluQixXQUFVO2tDQUNiLDRFQUFDbUI7NEJBQ0NuQixXQUFVOzRCQUNWNEIsT0FBTztnQ0FDTEMsT0FBTyxHQUFtRixPQUFoRixDQUFFMUIsdUJBQXVCLEtBQUsyQixLQUFLQyxHQUFHLENBQUNwQyxVQUFVZSxNQUFNLEVBQUViLFNBQVM2QixLQUFLLElBQUssS0FBSTs0QkFDNUY7Ozs7Ozs7Ozs7O29CQUdIOUIsMkJBQ0MsOERBQUN3Qjt3QkFBRXBCLFdBQVU7OzRCQUE2Qjs0QkFDMUJILFNBQVNtQyxVQUFVOzRCQUFDOzs7Ozs7Ozs7Ozs7O1lBTXZDWCxpQ0FDQyw4REFBQzdCLHFEQUFJQTtnQkFBQ1EsV0FBVTswQkFDZCw0RUFBQ21CO29CQUFJbkIsV0FBVTs7c0NBRWIsOERBQUNtQjs7OENBQ0MsOERBQUNBO29DQUFJbkIsV0FBVTs7c0RBQ2IsOERBQUMyQjs0Q0FBSzNCLFdBQVU7O2dEQUNicUIsZ0JBQWdCWSxTQUFTO2dEQUFDOzs7Ozs7O3NEQUU3Qiw4REFBQ047NENBQUszQixXQUFVOztnREFBd0I7Z0RBQ25DcUIsZ0JBQWdCYSxLQUFLO2dEQUFDOzs7Ozs7Ozs7Ozs7OzhDQUc3Qiw4REFBQ0M7b0NBQUduQyxXQUFVOzhDQUNYcUIsZ0JBQWdCZSxJQUFJOzs7Ozs7Ozs7Ozs7c0NBS3pCLDhEQUFDakI7NEJBQUluQixXQUFVO3NDQUNacUIsZ0JBQWdCZ0IsT0FBTyxDQUFDckIsR0FBRyxDQUFDLENBQUNzQix1QkFDNUIsOERBQUNDO29DQUVDdkMsV0FBVTs7c0RBRVYsOERBQUN3Qzs0Q0FDQ0MsTUFBSzs0Q0FDTEMsTUFBTXJCLGdCQUFnQkcsRUFBRTs0Q0FDeEJtQixPQUFPTCxPQUFPTSxLQUFLOzRDQUNuQkMsU0FBUzVDLE9BQU8sQ0FBQ29CLGdCQUFnQkcsRUFBRSxDQUFDLEtBQUtjLE9BQU9NLEtBQUs7NENBQ3JERSxVQUFVLElBQU16QyxtQkFBbUJnQixnQkFBZ0JHLEVBQUUsRUFBRWMsT0FBT00sS0FBSzs0Q0FDbkU1QyxXQUFVOzs7Ozs7c0RBRVosOERBQUMyQjs0Q0FBSzNCLFdBQVU7c0RBQ2JzQyxPQUFPRixJQUFJOzs7Ozs7c0RBRWQsOERBQUNUOzRDQUFLM0IsV0FBVTs7Z0RBQ2JzQyxPQUFPTSxLQUFLO2dEQUFDOzs7Ozs7OzttQ0FmWE4sT0FBT2QsRUFBRTs7Ozs7Ozs7OztzQ0FzQnBCLDhEQUFDTDs0QkFBSW5CLFdBQVU7OzhDQUNiLDhEQUFDUCx5REFBTUE7b0NBQ0xzRCxTQUFTcEM7b0NBQ1RxQyxVQUFVN0MseUJBQXlCO29DQUNuQzhDLFNBQVE7OENBQ1Q7Ozs7Ozs4Q0FJRCw4REFBQzlCO29DQUFJbkIsV0FBVTs4Q0FDWixDQUFDc0IsK0JBQ0EsOERBQUM3Qix5REFBTUE7d0NBQ0xzRCxTQUFTdEM7d0NBQ1R1QyxVQUFVLENBQUN6QjtrREFDWjs7Ozs7b0RBR0NFLGtDQUNGLDhEQUFDaEMseURBQU1BO3dDQUNMdUQsVUFBVTt3Q0FDVmhELFdBQVU7OzRDQUNYOzRDQUNhTCxVQUFVZSxNQUFNOzRDQUFDOzRDQUFFYixTQUFTNkIsS0FBSzs0Q0FBQzs7Ozs7O2tFQUdoRCw4REFBQ2pDLHlEQUFNQTt3Q0FDTHNELFNBQVNuQzt3Q0FDVG9DLFVBQVUsQ0FBQ3pCO3dDQUNYdkIsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVdaYyxPQUFPb0MsSUFBSSxDQUFDakQsU0FBU1MsTUFBTSxHQUFHLG1CQUM3Qiw4REFBQ2xCLHFEQUFJQTtnQkFBQ1EsV0FBVTs7a0NBQ2QsOERBQUNtRDt3QkFBR25ELFdBQVU7OzRCQUF5Qzs0QkFDN0NjLE9BQU9vQyxJQUFJLENBQUNqRCxTQUFTUyxNQUFNOzRCQUFDOzs7Ozs7O2tDQUV0Qyw4REFBQ1M7d0JBQUluQixXQUFVO2tDQUNaTCxVQUFVcUIsR0FBRyxDQUFDLENBQUNvQyxVQUFVQyxzQkFDeEIsOERBQUNDO2dDQUVDUCxTQUFTLElBQU0zQyx3QkFBd0JpRDtnQ0FDdkNyRCxXQUFXLGdHQU1QcUQsT0FKQXBELE9BQU8sQ0FBQ21ELFNBQVM1QixFQUFFLENBQUMsR0FDbEIsd0RBQ0Esb0RBQ0gsd0JBSUEsT0FIQzZCLFVBQVVsRCx1QkFDUix5QkFDQSxxQkFDSDswQ0FHRmtELFFBQVE7K0JBZEpELFNBQVM1QixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBc0JoQyxFQUFDO0dBOU5ZOUI7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvcXVlc3Rpb25uYWlyZS9TdHJlYW1RdWVzdGlvbm5haXJlUmVuZGVyZXIudHN4PzYwYTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0NhcmQnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvQnV0dG9uJ1xuXG4vKipcbiAqIOa1geW8j+mXruWNt+mimOebruexu+Wei1xuICovXG5pbnRlcmZhY2UgU3RyZWFtUXVlc3Rpb24ge1xuICBpZDogc3RyaW5nXG4gIGRpbWVuc2lvbjogc3RyaW5nXG4gIHR5cGU6IHN0cmluZ1xuICB0ZXh0OiBzdHJpbmdcbiAgb3B0aW9uczogQXJyYXk8e1xuICAgIGlkOiBzdHJpbmdcbiAgICB0ZXh0OiBzdHJpbmdcbiAgICBzY29yZTogbnVtYmVyXG4gIH0+XG4gIHJlcXVpcmVkOiBib29sZWFuXG4gIG9yZGVyOiBudW1iZXJcbn1cblxuaW50ZXJmYWNlIFN0cmVhbVF1ZXN0aW9ubmFpcmVSZW5kZXJlclByb3BzIHtcbiAgcXVlc3Rpb25zOiBTdHJlYW1RdWVzdGlvbltdXG4gIGlzTG9hZGluZzogYm9vbGVhblxuICBwcm9ncmVzczoge1xuICAgIGNvbXBsZXRlZDogbnVtYmVyXG4gICAgdG90YWw6IG51bWJlclxuICAgIHBlcmNlbnRhZ2U6IG51bWJlclxuICB9XG4gIG9uQW5zd2VyQ2hhbmdlPzogKHF1ZXN0aW9uSWQ6IHN0cmluZywgYW5zd2VyOiBhbnkpID0+IHZvaWRcbiAgb25Db21wbGV0ZT86IChyZXNwb25zZXM6IGFueVtdKSA9PiB2b2lkXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG4vKipcbiAqIOa1geW8j+mXruWNt+a4suafk+WZqFxuICog5pSv5oyB5Yqo5oCB5Yqg6L295ZKM5pi+56S66aKY55uuXG4gKi9cbmV4cG9ydCBjb25zdCBTdHJlYW1RdWVzdGlvbm5haXJlUmVuZGVyZXI6IFJlYWN0LkZDPFN0cmVhbVF1ZXN0aW9ubmFpcmVSZW5kZXJlclByb3BzPiA9ICh7XG4gIHF1ZXN0aW9ucyxcbiAgaXNMb2FkaW5nLFxuICBwcm9ncmVzcyxcbiAgb25BbnN3ZXJDaGFuZ2UsXG4gIG9uQ29tcGxldGUsXG4gIGNsYXNzTmFtZSA9ICcnXG59KSA9PiB7XG4gIGNvbnN0IFthbnN3ZXJzLCBzZXRBbnN3ZXJzXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIGFueT4+KHt9KVxuICBjb25zdCBbY3VycmVudFF1ZXN0aW9uSW5kZXgsIHNldEN1cnJlbnRRdWVzdGlvbkluZGV4XSA9IHVzZVN0YXRlKDApXG5cbiAgLyoqXG4gICAqIOWkhOeQhuetlOahiOWPmOabtFxuICAgKi9cbiAgY29uc3QgaGFuZGxlQW5zd2VyQ2hhbmdlID0gKHF1ZXN0aW9uSWQ6IHN0cmluZywgYW5zd2VyOiBhbnkpID0+IHtcbiAgICBjb25zdCBuZXdBbnN3ZXJzID0geyAuLi5hbnN3ZXJzLCBbcXVlc3Rpb25JZF06IGFuc3dlciB9XG4gICAgc2V0QW5zd2VycyhuZXdBbnN3ZXJzKVxuICAgIFxuICAgIGlmIChvbkFuc3dlckNoYW5nZSkge1xuICAgICAgb25BbnN3ZXJDaGFuZ2UocXVlc3Rpb25JZCwgYW5zd2VyKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDkuIvkuIDpophcbiAgICovXG4gIGNvbnN0IGhhbmRsZU5leHQgPSAoKSA9PiB7XG4gICAgaWYgKGN1cnJlbnRRdWVzdGlvbkluZGV4IDwgcXVlc3Rpb25zLmxlbmd0aCAtIDEpIHtcbiAgICAgIHNldEN1cnJlbnRRdWVzdGlvbkluZGV4KGN1cnJlbnRRdWVzdGlvbkluZGV4ICsgMSlcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5LiK5LiA6aKYXG4gICAqL1xuICBjb25zdCBoYW5kbGVQcmV2aW91cyA9ICgpID0+IHtcbiAgICBpZiAoY3VycmVudFF1ZXN0aW9uSW5kZXggPiAwKSB7XG4gICAgICBzZXRDdXJyZW50UXVlc3Rpb25JbmRleChjdXJyZW50UXVlc3Rpb25JbmRleCAtIDEpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOaPkOS6pOmXruWNt1xuICAgKi9cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gKCkgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlcyA9IE9iamVjdC5lbnRyaWVzKGFuc3dlcnMpLm1hcCgoW3F1ZXN0aW9uSWQsIGFuc3dlcl0pID0+ICh7XG4gICAgICBxdWVzdGlvbklkLFxuICAgICAgYW5zd2VyLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpXG4gICAgfSkpXG4gICAgXG4gICAgaWYgKG9uQ29tcGxldGUpIHtcbiAgICAgIG9uQ29tcGxldGUocmVzcG9uc2VzKVxuICAgIH1cbiAgfVxuXG4gIC8vIOWmguaenOayoeaciemimOebru+8jOaYvuekuuetieW+heeKtuaAgVxuICBpZiAocXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiAoXG4gICAgICA8Q2FyZCBjbGFzc05hbWU9e2BwLTggdGV4dC1jZW50ZXIgJHtjbGFzc05hbWV9YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgPHA+5q2j5Zyo55Sf5oiQ56ys5LiA5om56aKY55uuLi4uPC9wPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQ2FyZD5cbiAgICApXG4gIH1cblxuICBjb25zdCBjdXJyZW50UXVlc3Rpb24gPSBxdWVzdGlvbnNbY3VycmVudFF1ZXN0aW9uSW5kZXhdXG4gIGNvbnN0IGlzTGFzdFF1ZXN0aW9uID0gY3VycmVudFF1ZXN0aW9uSW5kZXggPT09IHF1ZXN0aW9ucy5sZW5ndGggLSAxXG4gIGNvbnN0IGNhblByb2NlZWQgPSBhbnN3ZXJzW2N1cnJlbnRRdWVzdGlvbj8uaWRdXG5cbiAgLy8g5Yik5pat5piv5ZCm5bqU6K+l5pi+56S6XCLnrYnlvoXmm7TlpJrpopjnm65cIlxuICBjb25zdCBzaG91bGRXYWl0Rm9yTW9yZSA9IGlzTGFzdFF1ZXN0aW9uICYmIGlzTG9hZGluZyAmJiBxdWVzdGlvbnMubGVuZ3RoIDwgcHJvZ3Jlc3MudG90YWxcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgc3BhY2UteS02ICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgey8qIOi/m+W6puaMh+ekuuWZqCAqL31cbiAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICDpopjnm67ov5vluqZcbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICB7Y3VycmVudFF1ZXN0aW9uSW5kZXggKyAxfSAvIHtxdWVzdGlvbnMubGVuZ3RofVxuICAgICAgICAgICAge2lzTG9hZGluZyAmJiBgICjlhbEke3Byb2dyZXNzLnRvdGFsfemimClgfVxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgc3R5bGU9e3sgXG4gICAgICAgICAgICAgIHdpZHRoOiBgJHsoKGN1cnJlbnRRdWVzdGlvbkluZGV4ICsgMSkgLyBNYXRoLm1heChxdWVzdGlvbnMubGVuZ3RoLCBwcm9ncmVzcy50b3RhbCkpICogMTAwfSVgIFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICB7aXNMb2FkaW5nICYmIChcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgbXQtMVwiPlxuICAgICAgICAgICAg5q2j5Zyo55Sf5oiQ5pu05aSa6aKY55uuLi4uICh7cHJvZ3Jlc3MucGVyY2VudGFnZX0lIOWujOaIkClcbiAgICAgICAgICA8L3A+XG4gICAgICAgICl9XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIHsvKiDlvZPliY3popjnm64gKi99XG4gICAgICB7Y3VycmVudFF1ZXN0aW9uICYmIChcbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIHsvKiDpopjnm67moIfpopggKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICB7Y3VycmVudFF1ZXN0aW9uLmRpbWVuc2lvbn0g57u05bqmXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAg56ysIHtjdXJyZW50UXVlc3Rpb24ub3JkZXJ9IOmimFxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRRdWVzdGlvbi50ZXh0fVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDpgInpobkgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICB7Y3VycmVudFF1ZXN0aW9uLm9wdGlvbnMubWFwKChvcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICA8bGFiZWxcbiAgICAgICAgICAgICAgICAgIGtleT17b3B0aW9uLmlkfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC0zIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcbiAgICAgICAgICAgICAgICAgICAgbmFtZT17Y3VycmVudFF1ZXN0aW9uLmlkfVxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17b3B0aW9uLnNjb3JlfVxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXthbnN3ZXJzW2N1cnJlbnRRdWVzdGlvbi5pZF0gPT09IG9wdGlvbi5zY29yZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IGhhbmRsZUFuc3dlckNoYW5nZShjdXJyZW50UXVlc3Rpb24uaWQsIG9wdGlvbi5zY29yZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ibHVlLTYwMCBib3JkZXItZ3JheS0zMDAgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMyB0ZXh0LWdyYXktNzAwIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICB7b3B0aW9uLnRleHR9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge29wdGlvbi5zY29yZX3liIZcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog5a+86Iiq5oyJ6ZKuICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBwdC00XCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVQcmV2aW91c31cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17Y3VycmVudFF1ZXN0aW9uSW5kZXggPT09IDB9XG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5LiK5LiA6aKYXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICB7IWlzTGFzdFF1ZXN0aW9uID8gKFxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVOZXh0fVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWNhblByb2NlZWR9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIOS4i+S4gOmimFxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgKSA6IHNob3VsZFdhaXRGb3JNb3JlID8gKFxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17dHJ1ZX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDnrYnlvoXmm7TlpJrpopjnm64uLi4gKHtxdWVzdGlvbnMubGVuZ3RofS97cHJvZ3Jlc3MudG90YWx9KVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU3VibWl0fVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWNhblByb2NlZWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDmj5DkuqTpl67ljbdcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDlt7Llm57nrZTpopjnm67mpoLop4ggKi99XG4gICAgICB7T2JqZWN0LmtleXMoYW5zd2VycykubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItM1wiPlxuICAgICAgICAgICAg5bey5Zue562U6aKY55uuICh7T2JqZWN0LmtleXMoYW5zd2VycykubGVuZ3RofSlcbiAgICAgICAgICA8L2g0PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMTAgZ2FwLTJcIj5cbiAgICAgICAgICAgIHtxdWVzdGlvbnMubWFwKChxdWVzdGlvbiwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIGtleT17cXVlc3Rpb24uaWR9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q3VycmVudFF1ZXN0aW9uSW5kZXgoaW5kZXgpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICAgICAgdy04IGgtOCByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnNcbiAgICAgICAgICAgICAgICAgICR7YW5zd2Vyc1txdWVzdGlvbi5pZF0gXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTcwMCBib3JkZXIgYm9yZGVyLWdyZWVuLTMwMCcgXG4gICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS01MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICR7aW5kZXggPT09IGN1cnJlbnRRdWVzdGlvbkluZGV4IFxuICAgICAgICAgICAgICAgICAgICA/ICdyaW5nLTIgcmluZy1ibHVlLTUwMCcgXG4gICAgICAgICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWdyYXktMjAwJ1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aW5kZXggKyAxfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkNhcmQiLCJCdXR0b24iLCJTdHJlYW1RdWVzdGlvbm5haXJlUmVuZGVyZXIiLCJxdWVzdGlvbnMiLCJpc0xvYWRpbmciLCJwcm9ncmVzcyIsIm9uQW5zd2VyQ2hhbmdlIiwib25Db21wbGV0ZSIsImNsYXNzTmFtZSIsImFuc3dlcnMiLCJzZXRBbnN3ZXJzIiwiY3VycmVudFF1ZXN0aW9uSW5kZXgiLCJzZXRDdXJyZW50UXVlc3Rpb25JbmRleCIsImhhbmRsZUFuc3dlckNoYW5nZSIsInF1ZXN0aW9uSWQiLCJhbnN3ZXIiLCJuZXdBbnN3ZXJzIiwiaGFuZGxlTmV4dCIsImxlbmd0aCIsImhhbmRsZVByZXZpb3VzIiwiaGFuZGxlU3VibWl0IiwicmVzcG9uc2VzIiwiT2JqZWN0IiwiZW50cmllcyIsIm1hcCIsInRpbWVzdGFtcCIsIkRhdGUiLCJkaXYiLCJwIiwiY3VycmVudFF1ZXN0aW9uIiwiaXNMYXN0UXVlc3Rpb24iLCJjYW5Qcm9jZWVkIiwiaWQiLCJzaG91bGRXYWl0Rm9yTW9yZSIsInRvdGFsIiwic3BhbiIsInN0eWxlIiwid2lkdGgiLCJNYXRoIiwibWF4IiwicGVyY2VudGFnZSIsImRpbWVuc2lvbiIsIm9yZGVyIiwiaDMiLCJ0ZXh0Iiwib3B0aW9ucyIsIm9wdGlvbiIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwibmFtZSIsInZhbHVlIiwic2NvcmUiLCJjaGVja2VkIiwib25DaGFuZ2UiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJ2YXJpYW50Iiwia2V5cyIsImg0IiwicXVlc3Rpb24iLCJpbmRleCIsImJ1dHRvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\n"));

/***/ })

});