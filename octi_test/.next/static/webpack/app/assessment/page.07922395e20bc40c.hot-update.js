"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/assessment/page",{

/***/ "(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/questionnaire/StreamQuestionnaireRenderer.tsx ***!
  \**********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamQuestionnaireRenderer: function() { return /* binding */ StreamQuestionnaireRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamQuestionnaireRenderer auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * 流式问卷渲染器\n * 支持动态加载和显示题目\n */ const StreamQuestionnaireRenderer = (param)=>{\n    let { questions, isLoading, progress, onAnswerChange, onComplete, className = \"\" } = param;\n    _s();\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [wasWaitingForMore, setWasWaitingForMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /**\n   * 处理答案变更\n   */ const handleAnswerChange = (questionId, answer)=>{\n        const newAnswers = {\n            ...answers,\n            [questionId]: answer\n        };\n        setAnswers(newAnswers);\n        if (onAnswerChange) {\n            onAnswerChange(questionId, answer);\n        }\n    };\n    /**\n   * 下一题\n   */ const handleNext = ()=>{\n        if (currentQuestionIndex < questions.length - 1) {\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n        }\n    };\n    /**\n   * 上一题\n   */ const handlePrevious = ()=>{\n        if (currentQuestionIndex > 0) {\n            setCurrentQuestionIndex(currentQuestionIndex - 1);\n        }\n    };\n    /**\n   * 提交问卷\n   */ const handleSubmit = ()=>{\n        const responses = Object.entries(answers).map((param)=>{\n            let [questionId, answer] = param;\n            return {\n                questionId,\n                answer,\n                timestamp: new Date()\n            };\n        });\n        if (onComplete) {\n            onComplete(responses);\n        }\n    };\n    // 如果没有题目，显示等待状态\n    if (questions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"p-8 text-center \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"正在生成第一批题目...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentQuestion = questions[currentQuestionIndex];\n    const isLastQuestion = currentQuestionIndex === questions.length - 1;\n    const canProceed = answers[currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.id];\n    // 判断是否应该显示\"等待更多题目\"\n    const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"题目进度\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    currentQuestionIndex + 1,\n                                    \" / \",\n                                    questions.length,\n                                    isLoading && \" (共\".concat(progress.total, \"题)\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat((currentQuestionIndex + 1) / Math.max(questions.length, progress.total) * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-blue-600 mt-1\",\n                        children: [\n                            \"正在生成更多题目... (\",\n                            progress.percentage,\n                            \"% 完成)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-600 font-medium\",\n                                            children: [\n                                                currentQuestion.dimension,\n                                                \" 维度\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"第 \",\n                                                currentQuestion.order,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 leading-relaxed\",\n                                    children: currentQuestion.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQuestion.options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            name: currentQuestion.id,\n                                            value: option.score,\n                                            checked: answers[currentQuestion.id] === option.score,\n                                            onChange: ()=>handleAnswerChange(currentQuestion.id, option.score),\n                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-gray-700 flex-1\",\n                                            children: option.text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                option.score,\n                                                \"分\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, option.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePrevious,\n                                    disabled: currentQuestionIndex === 0,\n                                    variant: \"outline\",\n                                    children: \"上一题\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: !isLastQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleNext,\n                                        disabled: !canProceed,\n                                        children: \"下一题\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 19\n                                    }, undefined) : shouldWaitForMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        disabled: true,\n                                        className: \"bg-blue-500 hover:bg-blue-600\",\n                                        children: [\n                                            \"等待更多题目... (\",\n                                            questions.length,\n                                            \"/\",\n                                            progress.total,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleSubmit,\n                                        disabled: !canProceed,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: \"提交问卷\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, undefined),\n            Object.keys(answers).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: [\n                            \"已回答题目 (\",\n                            Object.keys(answers).length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-10 gap-2\",\n                        children: questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentQuestionIndex(index),\n                                className: \"\\n                  w-8 h-8 rounded text-xs font-medium transition-colors\\n                  \".concat(answers[question.id] ? \"bg-green-100 text-green-700 border border-green-300\" : \"bg-gray-100 text-gray-500 border border-gray-300\", \"\\n                  \").concat(index === currentQuestionIndex ? \"ring-2 ring-blue-500\" : \"hover:bg-gray-200\", \"\\n                \"),\n                                children: index + 1\n                            }, question.id, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StreamQuestionnaireRenderer, \"9s8JVJ2J9HFzF5Pb8TcNsjPeBcY=\");\n_c = StreamQuestionnaireRenderer;\nvar _c;\n$RefreshReg$(_c, \"StreamQuestionnaireRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3F1ZXN0aW9ubmFpcmUvU3RyZWFtUXVlc3Rpb25uYWlyZVJlbmRlcmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUVrRDtBQUNQO0FBQ0k7QUFnQy9DOzs7Q0FHQyxHQUNNLE1BQU1JLDhCQUEwRTtRQUFDLEVBQ3RGQyxTQUFTLEVBQ1RDLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxjQUFjLEVBQ2RDLFVBQVUsRUFDVkMsWUFBWSxFQUFFLEVBQ2Y7O0lBQ0MsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdYLCtDQUFRQSxDQUFzQixDQUFDO0lBQzdELE1BQU0sQ0FBQ1ksc0JBQXNCQyx3QkFBd0IsR0FBR2IsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDYyxtQkFBbUJDLHFCQUFxQixHQUFHZiwrQ0FBUUEsQ0FBQztJQUUzRDs7R0FFQyxHQUNELE1BQU1nQixxQkFBcUIsQ0FBQ0MsWUFBb0JDO1FBQzlDLE1BQU1DLGFBQWE7WUFBRSxHQUFHVCxPQUFPO1lBQUUsQ0FBQ08sV0FBVyxFQUFFQztRQUFPO1FBQ3REUCxXQUFXUTtRQUVYLElBQUlaLGdCQUFnQjtZQUNsQkEsZUFBZVUsWUFBWUM7UUFDN0I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUUsYUFBYTtRQUNqQixJQUFJUix1QkFBdUJSLFVBQVVpQixNQUFNLEdBQUcsR0FBRztZQUMvQ1Isd0JBQXdCRCx1QkFBdUI7UUFDakQ7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTVUsaUJBQWlCO1FBQ3JCLElBQUlWLHVCQUF1QixHQUFHO1lBQzVCQyx3QkFBd0JELHVCQUF1QjtRQUNqRDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNVyxlQUFlO1FBQ25CLE1BQU1DLFlBQVlDLE9BQU9DLE9BQU8sQ0FBQ2hCLFNBQVNpQixHQUFHLENBQUM7Z0JBQUMsQ0FBQ1YsWUFBWUMsT0FBTzttQkFBTTtnQkFDdkVEO2dCQUNBQztnQkFDQVUsV0FBVyxJQUFJQztZQUNqQjs7UUFFQSxJQUFJckIsWUFBWTtZQUNkQSxXQUFXZ0I7UUFDYjtJQUNGO0lBRUEsZ0JBQWdCO0lBQ2hCLElBQUlwQixVQUFVaUIsTUFBTSxLQUFLLEdBQUc7UUFDMUIscUJBQ0UsOERBQUNwQixxREFBSUE7WUFBQ1EsV0FBVyxtQkFBNkIsT0FBVkE7c0JBQ2xDLDRFQUFDcUI7Z0JBQUlyQixXQUFVOzBCQUNiLDRFQUFDcUI7b0JBQUlyQixXQUFVOztzQ0FDYiw4REFBQ3NCO3NDQUFFOzs7Ozs7c0NBQ0gsOERBQUNEOzRCQUFJckIsV0FBVTtzQ0FDYiw0RUFBQ3FCO2dDQUFJckIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTTNCO0lBRUEsTUFBTXVCLGtCQUFrQjVCLFNBQVMsQ0FBQ1EscUJBQXFCO0lBQ3ZELE1BQU1xQixpQkFBaUJyQix5QkFBeUJSLFVBQVVpQixNQUFNLEdBQUc7SUFDbkUsTUFBTWEsYUFBYXhCLE9BQU8sQ0FBQ3NCLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCRyxFQUFFLENBQUM7SUFFL0MsbUJBQW1CO0lBQ25CLE1BQU1DLG9CQUFvQkgsa0JBQWtCNUIsYUFBYUQsVUFBVWlCLE1BQU0sR0FBR2YsU0FBUytCLEtBQUs7SUFFMUYscUJBQ0UsOERBQUNQO1FBQUlyQixXQUFXLGFBQXVCLE9BQVZBOzswQkFFM0IsOERBQUNSLHFEQUFJQTtnQkFBQ1EsV0FBVTs7a0NBQ2QsOERBQUNxQjt3QkFBSXJCLFdBQVU7OzBDQUNiLDhEQUFDNkI7Z0NBQUs3QixXQUFVOzBDQUFvQzs7Ozs7OzBDQUdwRCw4REFBQzZCO2dDQUFLN0IsV0FBVTs7b0NBQ2JHLHVCQUF1QjtvQ0FBRTtvQ0FBSVIsVUFBVWlCLE1BQU07b0NBQzdDaEIsYUFBYSxNQUFxQixPQUFmQyxTQUFTK0IsS0FBSyxFQUFDOzs7Ozs7Ozs7Ozs7O2tDQUd2Qyw4REFBQ1A7d0JBQUlyQixXQUFVO2tDQUNiLDRFQUFDcUI7NEJBQ0NyQixXQUFVOzRCQUNWOEIsT0FBTztnQ0FDTEMsT0FBTyxHQUFtRixPQUFoRixDQUFFNUIsdUJBQXVCLEtBQUs2QixLQUFLQyxHQUFHLENBQUN0QyxVQUFVaUIsTUFBTSxFQUFFZixTQUFTK0IsS0FBSyxJQUFLLEtBQUk7NEJBQzVGOzs7Ozs7Ozs7OztvQkFHSGhDLDJCQUNDLDhEQUFDMEI7d0JBQUV0QixXQUFVOzs0QkFBNkI7NEJBQzFCSCxTQUFTcUMsVUFBVTs0QkFBQzs7Ozs7Ozs7Ozs7OztZQU12Q1gsaUNBQ0MsOERBQUMvQixxREFBSUE7Z0JBQUNRLFdBQVU7MEJBQ2QsNEVBQUNxQjtvQkFBSXJCLFdBQVU7O3NDQUViLDhEQUFDcUI7OzhDQUNDLDhEQUFDQTtvQ0FBSXJCLFdBQVU7O3NEQUNiLDhEQUFDNkI7NENBQUs3QixXQUFVOztnREFDYnVCLGdCQUFnQlksU0FBUztnREFBQzs7Ozs7OztzREFFN0IsOERBQUNOOzRDQUFLN0IsV0FBVTs7Z0RBQXdCO2dEQUNuQ3VCLGdCQUFnQmEsS0FBSztnREFBQzs7Ozs7Ozs7Ozs7Ozs4Q0FHN0IsOERBQUNDO29DQUFHckMsV0FBVTs4Q0FDWHVCLGdCQUFnQmUsSUFBSTs7Ozs7Ozs7Ozs7O3NDQUt6Qiw4REFBQ2pCOzRCQUFJckIsV0FBVTtzQ0FDWnVCLGdCQUFnQmdCLE9BQU8sQ0FBQ3JCLEdBQUcsQ0FBQyxDQUFDc0IsdUJBQzVCLDhEQUFDQztvQ0FFQ3pDLFdBQVU7O3NEQUVWLDhEQUFDMEM7NENBQ0NDLE1BQUs7NENBQ0xDLE1BQU1yQixnQkFBZ0JHLEVBQUU7NENBQ3hCbUIsT0FBT0wsT0FBT00sS0FBSzs0Q0FDbkJDLFNBQVM5QyxPQUFPLENBQUNzQixnQkFBZ0JHLEVBQUUsQ0FBQyxLQUFLYyxPQUFPTSxLQUFLOzRDQUNyREUsVUFBVSxJQUFNekMsbUJBQW1CZ0IsZ0JBQWdCRyxFQUFFLEVBQUVjLE9BQU9NLEtBQUs7NENBQ25FOUMsV0FBVTs7Ozs7O3NEQUVaLDhEQUFDNkI7NENBQUs3QixXQUFVO3NEQUNid0MsT0FBT0YsSUFBSTs7Ozs7O3NEQUVkLDhEQUFDVDs0Q0FBSzdCLFdBQVU7O2dEQUNid0MsT0FBT00sS0FBSztnREFBQzs7Ozs7Ozs7bUNBZlhOLE9BQU9kLEVBQUU7Ozs7Ozs7Ozs7c0NBc0JwQiw4REFBQ0w7NEJBQUlyQixXQUFVOzs4Q0FDYiw4REFBQ1AseURBQU1BO29DQUNMd0QsU0FBU3BDO29DQUNUcUMsVUFBVS9DLHlCQUF5QjtvQ0FDbkNnRCxTQUFROzhDQUNUOzs7Ozs7OENBSUQsOERBQUM5QjtvQ0FBSXJCLFdBQVU7OENBQ1osQ0FBQ3dCLCtCQUNBLDhEQUFDL0IseURBQU1BO3dDQUNMd0QsU0FBU3RDO3dDQUNUdUMsVUFBVSxDQUFDekI7a0RBQ1o7Ozs7O29EQUdDRSxrQ0FDRiw4REFBQ2xDLHlEQUFNQTt3Q0FDTHlELFVBQVU7d0NBQ1ZsRCxXQUFVOzs0Q0FDWDs0Q0FDYUwsVUFBVWlCLE1BQU07NENBQUM7NENBQUVmLFNBQVMrQixLQUFLOzRDQUFDOzs7Ozs7a0VBR2hELDhEQUFDbkMseURBQU1BO3dDQUNMd0QsU0FBU25DO3dDQUNUb0MsVUFBVSxDQUFDekI7d0NBQ1h6QixXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBV1pnQixPQUFPb0MsSUFBSSxDQUFDbkQsU0FBU1csTUFBTSxHQUFHLG1CQUM3Qiw4REFBQ3BCLHFEQUFJQTtnQkFBQ1EsV0FBVTs7a0NBQ2QsOERBQUNxRDt3QkFBR3JELFdBQVU7OzRCQUF5Qzs0QkFDN0NnQixPQUFPb0MsSUFBSSxDQUFDbkQsU0FBU1csTUFBTTs0QkFBQzs7Ozs7OztrQ0FFdEMsOERBQUNTO3dCQUFJckIsV0FBVTtrQ0FDWkwsVUFBVXVCLEdBQUcsQ0FBQyxDQUFDb0MsVUFBVUMsc0JBQ3hCLDhEQUFDQztnQ0FFQ1AsU0FBUyxJQUFNN0Msd0JBQXdCbUQ7Z0NBQ3ZDdkQsV0FBVyxnR0FNUHVELE9BSkF0RCxPQUFPLENBQUNxRCxTQUFTNUIsRUFBRSxDQUFDLEdBQ2xCLHdEQUNBLG9EQUNILHdCQUlBLE9BSEM2QixVQUFVcEQsdUJBQ1IseUJBQ0EscUJBQ0g7MENBR0ZvRCxRQUFROytCQWRKRCxTQUFTNUIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXNCaEMsRUFBQztHQS9OWWhDO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3F1ZXN0aW9ubmFpcmUvU3RyZWFtUXVlc3Rpb25uYWlyZVJlbmRlcmVyLnRzeD82MGEyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQ2FyZCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9DYXJkJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0J1dHRvbidcblxuLyoqXG4gKiDmtYHlvI/pl67ljbfpopjnm67nsbvlnotcbiAqL1xuaW50ZXJmYWNlIFN0cmVhbVF1ZXN0aW9uIHtcbiAgaWQ6IHN0cmluZ1xuICBkaW1lbnNpb246IHN0cmluZ1xuICB0eXBlOiBzdHJpbmdcbiAgdGV4dDogc3RyaW5nXG4gIG9wdGlvbnM6IEFycmF5PHtcbiAgICBpZDogc3RyaW5nXG4gICAgdGV4dDogc3RyaW5nXG4gICAgc2NvcmU6IG51bWJlclxuICB9PlxuICByZXF1aXJlZDogYm9vbGVhblxuICBvcmRlcjogbnVtYmVyXG59XG5cbmludGVyZmFjZSBTdHJlYW1RdWVzdGlvbm5haXJlUmVuZGVyZXJQcm9wcyB7XG4gIHF1ZXN0aW9uczogU3RyZWFtUXVlc3Rpb25bXVxuICBpc0xvYWRpbmc6IGJvb2xlYW5cbiAgcHJvZ3Jlc3M6IHtcbiAgICBjb21wbGV0ZWQ6IG51bWJlclxuICAgIHRvdGFsOiBudW1iZXJcbiAgICBwZXJjZW50YWdlOiBudW1iZXJcbiAgfVxuICBvbkFuc3dlckNoYW5nZT86IChxdWVzdGlvbklkOiBzdHJpbmcsIGFuc3dlcjogYW55KSA9PiB2b2lkXG4gIG9uQ29tcGxldGU/OiAocmVzcG9uc2VzOiBhbnlbXSkgPT4gdm9pZFxuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuLyoqXG4gKiDmtYHlvI/pl67ljbfmuLLmn5PlmahcbiAqIOaUr+aMgeWKqOaAgeWKoOi9veWSjOaYvuekuumimOebrlxuICovXG5leHBvcnQgY29uc3QgU3RyZWFtUXVlc3Rpb25uYWlyZVJlbmRlcmVyOiBSZWFjdC5GQzxTdHJlYW1RdWVzdGlvbm5haXJlUmVuZGVyZXJQcm9wcz4gPSAoe1xuICBxdWVzdGlvbnMsXG4gIGlzTG9hZGluZyxcbiAgcHJvZ3Jlc3MsXG4gIG9uQW5zd2VyQ2hhbmdlLFxuICBvbkNvbXBsZXRlLFxuICBjbGFzc05hbWUgPSAnJ1xufSkgPT4ge1xuICBjb25zdCBbYW5zd2Vycywgc2V0QW5zd2Vyc10gPSB1c2VTdGF0ZTxSZWNvcmQ8c3RyaW5nLCBhbnk+Pih7fSlcbiAgY29uc3QgW2N1cnJlbnRRdWVzdGlvbkluZGV4LCBzZXRDdXJyZW50UXVlc3Rpb25JbmRleF0gPSB1c2VTdGF0ZSgwKVxuICBjb25zdCBbd2FzV2FpdGluZ0Zvck1vcmUsIHNldFdhc1dhaXRpbmdGb3JNb3JlXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIC8qKlxuICAgKiDlpITnkIbnrZTmoYjlj5jmm7RcbiAgICovXG4gIGNvbnN0IGhhbmRsZUFuc3dlckNoYW5nZSA9IChxdWVzdGlvbklkOiBzdHJpbmcsIGFuc3dlcjogYW55KSA9PiB7XG4gICAgY29uc3QgbmV3QW5zd2VycyA9IHsgLi4uYW5zd2VycywgW3F1ZXN0aW9uSWRdOiBhbnN3ZXIgfVxuICAgIHNldEFuc3dlcnMobmV3QW5zd2VycylcbiAgICBcbiAgICBpZiAob25BbnN3ZXJDaGFuZ2UpIHtcbiAgICAgIG9uQW5zd2VyQ2hhbmdlKHF1ZXN0aW9uSWQsIGFuc3dlcilcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5LiL5LiA6aKYXG4gICAqL1xuICBjb25zdCBoYW5kbGVOZXh0ID0gKCkgPT4ge1xuICAgIGlmIChjdXJyZW50UXVlc3Rpb25JbmRleCA8IHF1ZXN0aW9ucy5sZW5ndGggLSAxKSB7XG4gICAgICBzZXRDdXJyZW50UXVlc3Rpb25JbmRleChjdXJyZW50UXVlc3Rpb25JbmRleCArIDEpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOS4iuS4gOmimFxuICAgKi9cbiAgY29uc3QgaGFuZGxlUHJldmlvdXMgPSAoKSA9PiB7XG4gICAgaWYgKGN1cnJlbnRRdWVzdGlvbkluZGV4ID4gMCkge1xuICAgICAgc2V0Q3VycmVudFF1ZXN0aW9uSW5kZXgoY3VycmVudFF1ZXN0aW9uSW5kZXggLSAxKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDmj5DkuqTpl67ljbdcbiAgICovXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9ICgpID0+IHtcbiAgICBjb25zdCByZXNwb25zZXMgPSBPYmplY3QuZW50cmllcyhhbnN3ZXJzKS5tYXAoKFtxdWVzdGlvbklkLCBhbnN3ZXJdKSA9PiAoe1xuICAgICAgcXVlc3Rpb25JZCxcbiAgICAgIGFuc3dlcixcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKVxuICAgIH0pKVxuICAgIFxuICAgIGlmIChvbkNvbXBsZXRlKSB7XG4gICAgICBvbkNvbXBsZXRlKHJlc3BvbnNlcylcbiAgICB9XG4gIH1cblxuICAvLyDlpoLmnpzmsqHmnInpopjnm67vvIzmmL7npLrnrYnlvoXnirbmgIFcbiAgaWYgKHF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gKFxuICAgICAgPENhcmQgY2xhc3NOYW1lPXtgcC04IHRleHQtY2VudGVyICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIDxwPuato+WcqOeUn+aIkOesrOS4gOaJuemimOebri4uLjwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmQ+XG4gICAgKVxuICB9XG5cbiAgY29uc3QgY3VycmVudFF1ZXN0aW9uID0gcXVlc3Rpb25zW2N1cnJlbnRRdWVzdGlvbkluZGV4XVxuICBjb25zdCBpc0xhc3RRdWVzdGlvbiA9IGN1cnJlbnRRdWVzdGlvbkluZGV4ID09PSBxdWVzdGlvbnMubGVuZ3RoIC0gMVxuICBjb25zdCBjYW5Qcm9jZWVkID0gYW5zd2Vyc1tjdXJyZW50UXVlc3Rpb24/LmlkXVxuXG4gIC8vIOWIpOaWreaYr+WQpuW6lOivpeaYvuekulwi562J5b6F5pu05aSa6aKY55uuXCJcbiAgY29uc3Qgc2hvdWxkV2FpdEZvck1vcmUgPSBpc0xhc3RRdWVzdGlvbiAmJiBpc0xvYWRpbmcgJiYgcXVlc3Rpb25zLmxlbmd0aCA8IHByb2dyZXNzLnRvdGFsXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YHNwYWNlLXktNiAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHsvKiDov5vluqbmjIfnpLrlmaggKi99XG4gICAgICA8Q2FyZCBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAg6aKY55uu6L+b5bqmXG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAge2N1cnJlbnRRdWVzdGlvbkluZGV4ICsgMX0gLyB7cXVlc3Rpb25zLmxlbmd0aH1cbiAgICAgICAgICAgIHtpc0xvYWRpbmcgJiYgYCAo5YWxJHtwcm9ncmVzcy50b3RhbH3popgpYH1cbiAgICAgICAgICA8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgIHN0eWxlPXt7IFxuICAgICAgICAgICAgICB3aWR0aDogYCR7KChjdXJyZW50UXVlc3Rpb25JbmRleCArIDEpIC8gTWF0aC5tYXgocXVlc3Rpb25zLmxlbmd0aCwgcHJvZ3Jlc3MudG90YWwpKSAqIDEwMH0lYCBcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAge2lzTG9hZGluZyAmJiAoXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIG10LTFcIj5cbiAgICAgICAgICAgIOato+WcqOeUn+aIkOabtOWkmumimOebri4uLiAoe3Byb2dyZXNzLnBlcmNlbnRhZ2V9JSDlrozmiJApXG4gICAgICAgICAgPC9wPlxuICAgICAgICApfVxuICAgICAgPC9DYXJkPlxuXG4gICAgICB7Lyog5b2T5YmN6aKY55uuICovfVxuICAgICAge2N1cnJlbnRRdWVzdGlvbiAmJiAoXG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICB7Lyog6aKY55uu5qCH6aKYICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAge2N1cnJlbnRRdWVzdGlvbi5kaW1lbnNpb259IOe7tOW6plxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgIOesrCB7Y3VycmVudFF1ZXN0aW9uLm9yZGVyfSDpophcbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgIHtjdXJyZW50UXVlc3Rpb24udGV4dH1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog6YCJ6aG5ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAge2N1cnJlbnRRdWVzdGlvbi5vcHRpb25zLm1hcCgob3B0aW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxhYmVsXG4gICAgICAgICAgICAgICAgICBrZXk9e29wdGlvbi5pZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHAtMyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS01MCBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYWRpb1wiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9e2N1cnJlbnRRdWVzdGlvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e29wdGlvbi5zY29yZX1cbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17YW5zd2Vyc1tjdXJyZW50UXVlc3Rpb24uaWRdID09PSBvcHRpb24uc2NvcmV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiBoYW5kbGVBbnN3ZXJDaGFuZ2UoY3VycmVudFF1ZXN0aW9uLmlkLCBvcHRpb24uc2NvcmUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtYmx1ZS02MDAgYm9yZGVyLWdyYXktMzAwIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTMgdGV4dC1ncmF5LTcwMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAge29wdGlvbi50ZXh0fVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtvcHRpb24uc2NvcmV95YiGXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOWvvOiIquaMiemSriAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gcHQtNFwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUHJldmlvdXN9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2N1cnJlbnRRdWVzdGlvbkluZGV4ID09PSAwfVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOS4iuS4gOmimFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgeyFpc0xhc3RRdWVzdGlvbiA/IChcbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTmV4dH1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFjYW5Qcm9jZWVkfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDkuIvkuIDpophcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICkgOiBzaG91bGRXYWl0Rm9yTW9yZSA/IChcbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3RydWV9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwIGhvdmVyOmJnLWJsdWUtNjAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg562J5b6F5pu05aSa6aKY55uuLi4uICh7cXVlc3Rpb25zLmxlbmd0aH0ve3Byb2dyZXNzLnRvdGFsfSlcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVN1Ym1pdH1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFjYW5Qcm9jZWVkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg5o+Q5Lqk6Zeu5Y23XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICApfVxuXG4gICAgICB7Lyog5bey5Zue562U6aKY55uu5qaC6KeIICovfVxuICAgICAge09iamVjdC5rZXlzKGFuc3dlcnMpLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTNcIj5cbiAgICAgICAgICAgIOW3suWbnuetlOmimOebriAoe09iamVjdC5rZXlzKGFuc3dlcnMpLmxlbmd0aH0pXG4gICAgICAgICAgPC9oND5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEwIGdhcC0yXCI+XG4gICAgICAgICAgICB7cXVlc3Rpb25zLm1hcCgocXVlc3Rpb24sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e3F1ZXN0aW9uLmlkfVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRRdWVzdGlvbkluZGV4KGluZGV4KX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgIHctOCBoLTggcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzXG4gICAgICAgICAgICAgICAgICAke2Fuc3dlcnNbcXVlc3Rpb24uaWRdIFxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi03MDAgYm9yZGVyIGJvcmRlci1ncmVlbi0zMDAnIFxuICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNTAwIGJvcmRlciBib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAke2luZGV4ID09PSBjdXJyZW50UXVlc3Rpb25JbmRleCBcbiAgICAgICAgICAgICAgICAgICAgPyAncmluZy0yIHJpbmctYmx1ZS01MDAnIFxuICAgICAgICAgICAgICAgICAgICA6ICdob3ZlcjpiZy1ncmF5LTIwMCdcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBgfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2luZGV4ICsgMX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJDYXJkIiwiQnV0dG9uIiwiU3RyZWFtUXVlc3Rpb25uYWlyZVJlbmRlcmVyIiwicXVlc3Rpb25zIiwiaXNMb2FkaW5nIiwicHJvZ3Jlc3MiLCJvbkFuc3dlckNoYW5nZSIsIm9uQ29tcGxldGUiLCJjbGFzc05hbWUiLCJhbnN3ZXJzIiwic2V0QW5zd2VycyIsImN1cnJlbnRRdWVzdGlvbkluZGV4Iiwic2V0Q3VycmVudFF1ZXN0aW9uSW5kZXgiLCJ3YXNXYWl0aW5nRm9yTW9yZSIsInNldFdhc1dhaXRpbmdGb3JNb3JlIiwiaGFuZGxlQW5zd2VyQ2hhbmdlIiwicXVlc3Rpb25JZCIsImFuc3dlciIsIm5ld0Fuc3dlcnMiLCJoYW5kbGVOZXh0IiwibGVuZ3RoIiwiaGFuZGxlUHJldmlvdXMiLCJoYW5kbGVTdWJtaXQiLCJyZXNwb25zZXMiLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwidGltZXN0YW1wIiwiRGF0ZSIsImRpdiIsInAiLCJjdXJyZW50UXVlc3Rpb24iLCJpc0xhc3RRdWVzdGlvbiIsImNhblByb2NlZWQiLCJpZCIsInNob3VsZFdhaXRGb3JNb3JlIiwidG90YWwiLCJzcGFuIiwic3R5bGUiLCJ3aWR0aCIsIk1hdGgiLCJtYXgiLCJwZXJjZW50YWdlIiwiZGltZW5zaW9uIiwib3JkZXIiLCJoMyIsInRleHQiLCJvcHRpb25zIiwib3B0aW9uIiwibGFiZWwiLCJpbnB1dCIsInR5cGUiLCJuYW1lIiwidmFsdWUiLCJzY29yZSIsImNoZWNrZWQiLCJvbkNoYW5nZSIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInZhcmlhbnQiLCJrZXlzIiwiaDQiLCJxdWVzdGlvbiIsImluZGV4IiwiYnV0dG9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\n"));

/***/ })

});