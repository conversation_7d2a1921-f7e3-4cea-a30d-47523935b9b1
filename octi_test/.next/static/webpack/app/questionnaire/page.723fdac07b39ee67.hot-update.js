"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/questionnaire/page",{

/***/ "(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx":
/*!**********************************************************************!*\
  !*** ./src/components/questionnaire/StreamQuestionnaireRenderer.tsx ***!
  \**********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamQuestionnaireRenderer: function() { return /* binding */ StreamQuestionnaireRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamQuestionnaireRenderer auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * 流式问卷渲染器\n * 支持动态加载和显示题目\n */ const StreamQuestionnaireRenderer = (param)=>{\n    let { questions, isLoading, progress, onAnswerChange, onComplete, className = \"\" } = param;\n    _s();\n    const [answers, setAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [wasWaitingForMore, setWasWaitingForMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    /**\n   * 监听题目数量变化，自动导航到新加载的题目\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const isLastQuestion = currentQuestionIndex === questions.length - 1;\n        const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total;\n        // 如果之前在等待更多题目，现在有新题目了，自动进入下一题\n        if (wasWaitingForMore && !shouldWaitForMore && questions.length > currentQuestionIndex + 1) {\n            console.log(\"检测到新题目加载，自动进入下一题\");\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n            setWasWaitingForMore(false);\n        } else if (shouldWaitForMore && !wasWaitingForMore) {\n            // 开始等待更多题目\n            setWasWaitingForMore(true);\n        } else if (!shouldWaitForMore && wasWaitingForMore) {\n            // 停止等待\n            setWasWaitingForMore(false);\n        }\n    }, [\n        questions.length,\n        isLoading,\n        currentQuestionIndex,\n        progress.total,\n        wasWaitingForMore\n    ]);\n    /**\n   * 处理答案变更\n   */ const handleAnswerChange = (questionId, answer)=>{\n        const newAnswers = {\n            ...answers,\n            [questionId]: answer\n        };\n        setAnswers(newAnswers);\n        if (onAnswerChange) {\n            onAnswerChange(questionId, answer);\n        }\n    };\n    /**\n   * 下一题\n   */ const handleNext = ()=>{\n        if (currentQuestionIndex < questions.length - 1) {\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n        }\n    };\n    /**\n   * 上一题\n   */ const handlePrevious = ()=>{\n        if (currentQuestionIndex > 0) {\n            setCurrentQuestionIndex(currentQuestionIndex - 1);\n        }\n    };\n    /**\n   * 提交问卷\n   */ const handleSubmit = ()=>{\n        const responses = Object.entries(answers).map((param)=>{\n            let [questionId, answer] = param;\n            return {\n                questionId,\n                answer,\n                timestamp: new Date()\n            };\n        });\n        if (onComplete) {\n            onComplete(responses);\n        }\n    };\n    // 如果没有题目，显示等待状态\n    if (questions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"p-8 text-center \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"正在生成第一批题目...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    const currentQuestion = questions[currentQuestionIndex];\n    const isLastQuestion = currentQuestionIndex === questions.length - 1;\n    const canProceed = answers[currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.id];\n    // 判断是否应该显示\"等待更多题目\"\n    const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: \"题目进度\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    currentQuestionIndex + 1,\n                                    \" / \",\n                                    questions.length,\n                                    isLoading && \" (共\".concat(progress.total, \"题)\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat((currentQuestionIndex + 1) / Math.max(questions.length, progress.total) * 100, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-blue-600 mt-1\",\n                        children: [\n                            \"正在生成更多题目... (\",\n                            progress.percentage,\n                            \"% 完成)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-blue-600 font-medium\",\n                                            children: [\n                                                currentQuestion.dimension,\n                                                \" 维度\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"第 \",\n                                                currentQuestion.order,\n                                                \" 题\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 leading-relaxed\",\n                                    children: currentQuestion.text\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: currentQuestion.options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            name: currentQuestion.id,\n                                            value: option.score,\n                                            checked: answers[currentQuestion.id] === option.score,\n                                            onChange: ()=>handleAnswerChange(currentQuestion.id, option.score),\n                                            className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-gray-700 flex-1\",\n                                            children: option.text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                option.score,\n                                                \"分\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, option.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePrevious,\n                                    disabled: currentQuestionIndex === 0,\n                                    variant: \"outline\",\n                                    children: \"上一题\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: !isLastQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleNext,\n                                        disabled: !canProceed,\n                                        children: \"下一题\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, undefined) : shouldWaitForMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        disabled: true,\n                                        className: \"bg-blue-500 hover:bg-blue-600\",\n                                        children: [\n                                            \"等待更多题目... (\",\n                                            questions.length,\n                                            \"/\",\n                                            progress.total,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleSubmit,\n                                        disabled: !canProceed,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: \"提交问卷\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, undefined),\n            Object.keys(answers).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: [\n                            \"已回答题目 (\",\n                            Object.keys(answers).length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-10 gap-2\",\n                        children: questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentQuestionIndex(index),\n                                className: \"\\n                  w-8 h-8 rounded text-xs font-medium transition-colors\\n                  \".concat(answers[question.id] ? \"bg-green-100 text-green-700 border border-green-300\" : \"bg-gray-100 text-gray-500 border border-gray-300\", \"\\n                  \").concat(index === currentQuestionIndex ? \"ring-2 ring-blue-500\" : \"hover:bg-gray-200\", \"\\n                \"),\n                                children: index + 1\n                            }, question.id, false, {\n                                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/components/questionnaire/StreamQuestionnaireRenderer.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StreamQuestionnaireRenderer, \"gdNJ62+wgu5Afx83W3/jL1p7hPY=\");\n_c = StreamQuestionnaireRenderer;\nvar _c;\n$RefreshReg$(_c, \"StreamQuestionnaireRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/questionnaire/StreamQuestionnaireRenderer.tsx\n"));

/***/ })

});