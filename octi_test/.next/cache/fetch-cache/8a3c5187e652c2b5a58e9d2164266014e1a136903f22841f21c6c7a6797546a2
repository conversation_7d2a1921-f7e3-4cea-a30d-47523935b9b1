{"kind": "FETCH", "data": {"headers": {"alb_receive_time": "1753693080.739", "alb_request_id": "4de739b17c105ed7b1b044aaf3bd4c06489ae99d", "connection": "keep-alive", "content-encoding": "br", "content-type": "application/json; charset=utf-8", "date": "Mon, 28 Jul 2025 08:58:10 GMT", "minimax-request-id": "adbc09ade5c979e70aa3f0457aa594d9", "trace-id": "04d66e98620f791440ca1ab2216e4bbd", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-mm-request-id": "1939118475410678703_1753693080246141"}, "body": "eyJpZCI6IjA0ZDY2ZTk4NjIwZjc5MTQ0MGNhMWFiMjIxNmU0YmJkIiwiY2hvaWNlcyI6W3siZmluaXNoX3JlYXNvbiI6InN0b3AiLCJpbmRleCI6MCwibWVzc2FnZSI6eyJjb250ZW50IjoiYGBganNvblxue1xuICAgXCJpZFwiOiBcInRlc3RfcXVlc3Rpb25uYWlyZVwiLFxuICAgXCJ0aXRsZVwiOiBcIua1i+ivlemXruWNt1wiLFxuICAgXCJxdWVzdGlvbnNcIjogW1xuICAgICAge1xuICAgICAgICAgXCJpZFwiOiBcInExXCIsXG4gICAgICAgICBcInRleHRcIjogXCLpl67popgxXCIsXG4gICAgICAgICBcIm9wdGlvbnNcIjogW1xuICAgICAgICAgICAge1wiaWRcIjogXCJvMVwiLCBcInRleHRcIjogXCLpgInpobkxXCIsIFwic2NvcmVcIjogMX0sXG4gICAgICAgICAgICB7XCJpZFwiOiBcIm8yXCIsIFwidGV4dFwiOiBcIumAiemhuTJcIiwgXCJzY29yZVwiOiAyfVxuICAgICAgICAgXVxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgIFwiaWRcIjogXCJxMlwiLFxuICAgICAgICAgXCJ0ZXh0XCI6IFwi6Zeu6aKYMlwiLFxuICAgICAgICAgXCJvcHRpb25zXCI6IFtcbiAgICAgICAgICAgIHtcImlkXCI6IFwibzNcIiwgXCJ0ZXh0XCI6IFwi6YCJ6aG5M1wiLCBcInNjb3JlXCI6IDN9LFxuICAgICAgICAgICAge1wiaWRcIjogXCJvNFwiLCBcInRleHRcIjogXCLpgInpobk0XCIsIFwic2NvcmVcIjogNH1cbiAgICAgICAgIF1cbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgICBcImlkXCI6IFwicTNcIixcbiAgICAgICAgIFwidGV4dFwiOiBcIumXrumimDNcIixcbiAgICAgICAgIFwib3B0aW9uc1wiOiBbXG4gICAgICAgICAgICB7XCJpZFwiOiBcIm81XCIsIFwidGV4dFwiOiBcIumAiemhuTVcIiwgXCJzY29yZVwiOiA1fSxcbiAgICAgICAgICAgIHtcImlkXCI6IFwibzZcIiwgXCJ0ZXh0XCI6IFwi6YCJ6aG5NlwiLCBcInNjb3JlXCI6IDZ9XG4gICAgICAgICBdXG4gICAgICB9XG4gICBdXG59XG5gYGAiLCJyb2xlIjoiYXNzaXN0YW50IiwibmFtZSI6Ik1N5pm66IO95Yqp55CGIiwiYXVkaW9fY29udGVudCI6IiJ9fV0sImNyZWF0ZWQiOjE3NTM2OTMwODAsIm1vZGVsIjoiYWJhYjYuNXMtY2hhdCIsIm9iamVjdCI6ImNoYXQuY29tcGxldGlvbiIsInVzYWdlIjp7InRvdGFsX3Rva2VucyI6Mzk2LCJ0b3RhbF9jaGFyYWN0ZXJzIjowLCJwcm9tcHRfdG9rZW5zIjoxNTksImNvbXBsZXRpb25fdG9rZW5zIjoyMzd9LCJpbnB1dF9zZW5zaXRpdmUiOmZhbHNlLCJvdXRwdXRfc2Vuc2l0aXZlIjpmYWxzZSwiaW5wdXRfc2Vuc2l0aXZlX3R5cGUiOjAsIm91dHB1dF9zZW5zaXRpdmVfdHlwZSI6MCwib3V0cHV0X3NlbnNpdGl2ZV9pbnQiOjAsImJhc2VfcmVzcCI6eyJzdGF0dXNfY29kZSI6MCwic3RhdHVzX21zZyI6IkludmFsaWQgcGFyYW1ldGVycyBkZXRlY3RlZCwganNvbjogdW5rbm93biBmaWVsZCBcIm1heFRva2Vuc1wiIn19", "status": 200, "url": "https://api.minimax.chat/v1/chat/completions"}, "revalidate": 31536000, "tags": []}