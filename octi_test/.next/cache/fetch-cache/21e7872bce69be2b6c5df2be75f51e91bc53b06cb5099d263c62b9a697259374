{"kind": "FETCH", "data": {"headers": {"alb_receive_time": "1753693658.639", "alb_request_id": "9bd13cb86d989549b5e0b7ba9018e4ee99cebb35", "connection": "keep-alive", "content-encoding": "br", "content-type": "application/json; charset=utf-8", "date": "Mon, 28 Jul 2025 09:07:46 GMT", "minimax-request-id": "4732cf49d3a2be49b63b95f1cda1baa8", "trace-id": "04d670daba7af6c1a647c6f8f60ce8ae", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-mm-request-id": "1939118475410678703_1753693658949555"}, "body": "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", "status": 200, "url": "https://api.minimax.chat/v1/chat/completions"}, "revalidate": 31536000, "tags": []}