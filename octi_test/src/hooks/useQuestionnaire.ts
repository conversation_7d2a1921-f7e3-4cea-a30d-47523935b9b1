'use client'

import { useState, useCallback, useMemo } from 'react'
import { QuestionnaireConfig, QuestionnaireResponse, ConfigQuestion } from '@/types'

// 简化的问题回答类型
interface QuestionResponse {
  questionId: string
  answer: any
}

interface UseQuestionnaireOptions {
  config: QuestionnaireConfig
  initialResponses: QuestionnaireResponse[]
  onComplete: (responses: QuestionnaireResponse[]) => Promise<void>
  onSave?: (responses: QuestionnaireResponse[]) => Promise<void>
}

/**
 * 问卷逻辑管理Hook
 * 将复杂的状态管理逻辑从组件中抽离
 */
export function useQuestionnaire({
  config,
  initialResponses,
  onComplete,
  onSave
}: UseQuestionnaireOptions) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [responses, setResponses] = useState<QuestionResponse[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)

  // 从QuestionnaireConfig中提取所有问题
  const allQuestions = useMemo(() => {
    const questions: ConfigQuestion[] = []
    Object.values(config.dimensions).forEach(dimension => {
      questions.push(...dimension.questions)
    })
    return questions
  }, [config])

  // 计算派生状态
  const currentQuestion = useMemo(() =>
    allQuestions[currentQuestionIndex],
    [allQuestions, currentQuestionIndex]
  )

  const isFirst = currentQuestionIndex === 0
  const isLast = currentQuestionIndex === allQuestions.length - 1
  const progress = ((currentQuestionIndex + 1) / allQuestions.length) * 100

  // 更新回答 - 修复参数类型
  const updateResponse = useCallback((questionId: string, answer: any) => {
    setResponses(prev => {
      const existing = prev.find(r => r.questionId === questionId)
      if (existing) {
        return prev.map(r =>
          r.questionId === questionId ? { ...r, answer } : r
        )
      }
      return [...prev, { questionId, answer }]
    })

    // 清除错误
    setErrors(prev => ({ ...prev, [questionId]: '' }))
  }, [])

  // 验证当前问题
  const validateCurrentQuestion = useCallback(() => {
    if (!currentQuestion) return true

    const response = responses.find(r => r.questionId === currentQuestion.id)
    if (!response || response.answer === undefined || response.answer === null || response.answer === '') {
      setErrors(prev => ({
        ...prev,
        [currentQuestion.id]: '请回答此问题后再继续'
      }))
      return false
    }
    return true
  }, [currentQuestion, responses])

  // 下一题
  const handleNext = useCallback(async () => {
    if (!validateCurrentQuestion()) return

    if (isLast) {
      setIsLoading(true)
      try {
        // 转换为QuestionnaireResponse格式
        const questionnaireResponse: QuestionnaireResponse = {
          id: `response_${Date.now()}`,
          questionnaireId: 'questionnaire_1',
          responses: responses.map(r => {
            // 找到对应的问题以获取dimension
            const question = allQuestions.find(q => q.id === r.questionId)
            return {
              id: `resp_${r.questionId}`,
              assessmentId: 'assessment_1',
              questionId: r.questionId,
              respondentId: 'user_1',
              answer: r.answer,
              dimension: question?.dimension || 'SF',
              createdAt: new Date(),
              updatedAt: new Date()
            }
          }),
          metadata: {
            startTime: new Date().toISOString(),
            endTime: new Date().toISOString(),
            totalTimeSpent: 0,
            submittedAt: new Date().toISOString(),
            version: config.version,
            completionRate: 100
          },
          status: 'completed' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        await onComplete([questionnaireResponse])
      } catch (error) {
        console.error('提交问卷失败:', error)
      } finally {
        setIsLoading(false)
      }
    } else {
      setCurrentQuestionIndex(prev => prev + 1)
    }
  }, [validateCurrentQuestion, isLast, responses, onComplete])

  // 上一题
  const handlePrevious = useCallback(() => {
    if (!isFirst) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }, [isFirst])

  return {
    currentQuestion,
    currentQuestionIndex,
    responses,
    errors,
    isLoading,
    isFirst,
    isLast,
    progress,
    handleNext,
    handlePrevious,
    updateResponse // 确保导出这个函数
  }
}
