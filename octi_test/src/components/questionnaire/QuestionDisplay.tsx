'use client'

import React from 'react'
import { ConfigQuestion } from '@/types'

interface QuestionDisplayProps {
  question: ConfigQuestion
  value?: any
  onChange: (value: any) => void
  error?: string
}

/**
 * 问题显示组件
 * 根据问题类型渲染不同的输入控件
 */
export const QuestionDisplay: React.FC<QuestionDisplayProps> = ({
  question,
  value,
  onChange,
  error
}) => {
  // 渲染单选题
  const renderSingleChoice = () => {
    if (!question.options || question.options.length === 0) {
      return (
        <div className="text-red-500">
          错误：单选题缺少选项数据
        </div>
      )
    }

    return (
      <div className="space-y-3">
        {question.options.map((option, index) => (
          <label
            key={`${question.id}-option-${index}`}
            className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          >
            <input
              type="radio"
              name={question.id}
              value={option.value || option.text}
              checked={value === (option.value || option.text)}
              onChange={(e) => onChange(e.target.value)}
              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
            />
            <span className="text-gray-900 flex-1">
              {option.text}
            </span>
            {option.score !== undefined && (
              <span className="text-sm text-gray-500">
                ({option.score}分)
              </span>
            )}
          </label>
        ))}
      </div>
    )
  }

  // 渲染多选题
  const renderMultipleChoice = () => {
    if (!question.options || question.options.length === 0) {
      return (
        <div className="text-red-500">
          错误：多选题缺少选项数据
        </div>
      )
    }

    const selectedValues = Array.isArray(value) ? value : []

    return (
      <div className="space-y-3">
        {question.options.map((option, index) => (
          <label
            key={`${question.id}-option-${index}`}
            className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          >
            <input
              type="checkbox"
              value={option.value || option.text}
              checked={selectedValues.includes(option.value || option.text)}
              onChange={(e) => {
                const optionValue = option.value || option.text
                let newValues = [...selectedValues]
                
                if (e.target.checked) {
                  if (!newValues.includes(optionValue)) {
                    newValues.push(optionValue)
                  }
                } else {
                  newValues = newValues.filter(v => v !== optionValue)
                }
                
                onChange(newValues)
              }}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <span className="text-gray-900 flex-1">
              {option.text}
            </span>
            {option.score !== undefined && (
              <span className="text-sm text-gray-500">
                ({option.score}分)
              </span>
            )}
          </label>
        ))}
      </div>
    )
  }

  // 渲染李克特量表
  const renderLikertScale = () => {
    const scaleOptions = question.options || [
      { text: '非常不同意', value: '1', score: 1 },
      { text: '不同意', value: '2', score: 2 },
      { text: '中立', value: '3', score: 3 },
      { text: '同意', value: '4', score: 4 },
      { text: '非常同意', value: '5', score: 5 }
    ]

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-5 gap-2">
          {scaleOptions.map((option, index) => (
            <label
              key={`${question.id}-scale-${index}`}
              className="flex flex-col items-center space-y-2 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <input
                type="radio"
                name={question.id}
                value={option.value}
                checked={value === option.value}
                onChange={(e) => onChange(e.target.value)}
                className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm text-center text-gray-900">
                {option.text}
              </span>
              {option.score !== undefined && (
                <span className="text-xs text-gray-500">
                  {option.score}
                </span>
              )}
            </label>
          ))}
        </div>
      </div>
    )
  }

  // 渲染文本输入
  const renderTextInput = () => {
    return (
      <textarea
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder="请输入您的答案..."
        rows={4}
        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
      />
    )
  }

  // 根据问题类型渲染对应的输入控件
  const renderQuestionInput = () => {
    switch (question.type) {
      case 'single_choice':
      case 'single-choice':
        return renderSingleChoice()
      case 'multiple_choice':
      case 'multiple-choice':
        return renderMultipleChoice()
      case 'likert_scale':
      case 'likert-scale':
        return renderLikertScale()
      case 'text':
      case 'textarea':
        return renderTextInput()
      default:
        return (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="text-yellow-800">
              <strong>不支持的问题类型:</strong> {question.type}
            </div>
            <div className="text-sm text-yellow-600 mt-2">
              请联系开发人员添加对此问题类型的支持
            </div>
            <div className="mt-3 text-xs text-gray-600">
              <strong>问题数据:</strong>
              <pre className="mt-1 bg-white p-2 rounded border text-xs overflow-auto">
                {JSON.stringify(question, null, 2)}
              </pre>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="space-y-4">
      {/* 问题标题 */}
      <div className="space-y-2">
        <h3 className="text-lg font-medium text-gray-900">
          {question.text || question.title}
        </h3>
        
        {/* 问题描述 */}
        {question.description && (
          <p className="text-sm text-gray-600">
            {question.description}
          </p>
        )}
        
        {/* 维度标签 */}
        {question.dimension && (
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {question.dimension}
            </span>
            {question.required && (
              <span className="text-red-500 text-sm">*</span>
            )}
          </div>
        )}
      </div>

      {/* 问题输入控件 */}
      <div className="mt-4">
        {renderQuestionInput()}
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mt-2 text-sm text-red-600">
          {error}
        </div>
      )}
    </div>
  )
}
