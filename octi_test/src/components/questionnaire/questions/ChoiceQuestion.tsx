'use client'

import React, { useCallback } from 'react'
import { QuestionComponentProps } from '@/types'
import { Card } from '@/components/ui/Card'

/**
 * 单选题组件 - 优化版
 */
export const ChoiceQuestion: React.FC<QuestionComponentProps> = React.memo(({
  question,
  value,
  onChange,
  disabled = false
}) => {
  // 类型安全的选项处理
  const handleOptionSelect = useCallback((optionValue: string | number) => {
    if (!disabled) {
      onChange(optionValue)
    }
  }, [onChange, disabled])

  // 验证问题类型
  if (question.type !== 'choice') {
    console.warn('ChoiceQuestion received non-choice question type:', question.type)
    return null
  }

  // 验证选项存在
  if (!question.options || question.options.length === 0) {
    return (
      <div className="text-red-500">
        问题配置错误：缺少选项
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 问题标题 */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900 leading-relaxed">
          {question.text}
        </h3>
        <div className="text-sm text-gray-500">
          {question.sub_dimension} • 单选题
        </div>
      </div>

      {/* 选项列表 */}
      <div className="space-y-3">
        {question.options.map((option, index) => {
          const isSelected = value === option.value
          
          return (
            <Card
              key={`${option.value}-${index}`}
              className={`p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                isSelected 
                  ? 'border-primary-500 bg-primary-50' 
                  : 'border-gray-200 hover:border-gray-300'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => handleOptionSelect(option.value)}
            >
              <div className="flex items-start space-x-3">
                <div className={`w-4 h-4 rounded-full border-2 mt-0.5 flex-shrink-0 ${
                  isSelected 
                    ? 'border-primary-500 bg-primary-500' 
                    : 'border-gray-300'
                }`}>
                  {isSelected && (
                    <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-gray-900 font-medium">
                    {option.text}
                  </div>
                  {/* ConfigQuestionOption没有description属性，暂时移除 */}
                </div>
              </div>
            </Card>
          )
        })}
      </div>
    </div>
  )
})

ChoiceQuestion.displayName = 'ChoiceQuestion'
