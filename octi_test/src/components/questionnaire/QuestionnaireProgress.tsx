'use client'

import React from 'react'
import { ProgressBar } from './ProgressBar'
import { QuestionCounter } from './QuestionCounter'

interface QuestionnaireProgressProps {
  current: number
  total: number
  progress: number
  dimension?: 'SF' | 'IT' | 'MV' | 'AD'
}

/**
 * 问卷进度组件
 * 显示当前进度和问题计数
 */
export const QuestionnaireProgress: React.FC<QuestionnaireProgressProps> = ({
  current,
  total,
  progress,
  dimension = 'SF'
}) => {
  return (
    <div className="space-y-4">
      <QuestionCounter current={current} total={total} dimension={dimension} />
      <ProgressBar progress={progress} />
    </div>
  )
}