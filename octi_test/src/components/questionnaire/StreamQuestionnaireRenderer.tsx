'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

/**
 * 流式问卷题目类型
 */
interface StreamQuestion {
  id: string
  dimension: string
  type: string
  text: string
  options: Array<{
    id: string
    text: string
    score: number
  }>
  required: boolean
  order: number
}

interface StreamQuestionnaireRendererProps {
  questions: StreamQuestion[]
  isLoading: boolean
  progress: {
    completed: number
    total: number
    percentage: number
  }
  onAnswerChange?: (questionId: string, answer: any) => void
  onComplete?: (responses: any[]) => void
  className?: string
}

/**
 * 流式问卷渲染器
 * 支持动态加载和显示题目
 */
export const StreamQuestionnaireRenderer: React.FC<StreamQuestionnaireRendererProps> = ({
  questions,
  isLoading,
  progress,
  onAnswerChange,
  onComplete,
  className = ''
}) => {
  const [answers, setAnswers] = useState<Record<string, any>>({})
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [wasWaitingForMore, setWasWaitingForMore] = useState(false)

  /**
   * 监听题目数量变化，自动导航到新加载的题目
   */
  useEffect(() => {
    const isLastQuestion = currentQuestionIndex === questions.length - 1
    const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total

    // 如果之前在等待更多题目，现在有新题目了，自动进入下一题
    if (wasWaitingForMore && !shouldWaitForMore && questions.length > currentQuestionIndex + 1) {
      console.log('检测到新题目加载，自动进入下一题')
      setCurrentQuestionIndex(currentQuestionIndex + 1)
      setWasWaitingForMore(false)
    } else if (shouldWaitForMore && !wasWaitingForMore) {
      // 开始等待更多题目
      setWasWaitingForMore(true)
    } else if (!shouldWaitForMore && wasWaitingForMore) {
      // 停止等待
      setWasWaitingForMore(false)
    }
  }, [questions.length, isLoading, currentQuestionIndex, progress.total, wasWaitingForMore])

  /**
   * 处理答案变更
   */
  const handleAnswerChange = (questionId: string, answer: any) => {
    const newAnswers = { ...answers, [questionId]: answer }
    setAnswers(newAnswers)
    
    if (onAnswerChange) {
      onAnswerChange(questionId, answer)
    }
  }

  /**
   * 下一题
   */
  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
    }
  }

  /**
   * 上一题
   */
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1)
    }
  }

  /**
   * 提交问卷
   */
  const handleSubmit = () => {
    const responses = Object.entries(answers).map(([questionId, answer]) => ({
      questionId,
      answer,
      timestamp: new Date()
    }))
    
    if (onComplete) {
      onComplete(responses)
    }
  }

  // 如果没有题目，显示等待状态
  if (questions.length === 0) {
    return (
      <Card className={`p-8 text-center ${className}`}>
        <div className="animate-pulse">
          <div className="text-gray-500">
            <p>正在生成第一批题目...</p>
            <div className="mt-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  const currentQuestion = questions[currentQuestionIndex]
  const isLastQuestion = currentQuestionIndex === questions.length - 1
  const canProceed = answers[currentQuestion?.id]

  // 判断是否应该显示"等待更多题目"
  const shouldWaitForMore = isLastQuestion && isLoading && questions.length < progress.total

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 进度指示器 */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            题目进度
          </span>
          <span className="text-sm text-gray-500">
            {currentQuestionIndex + 1} / {questions.length}
            {isLoading && ` (共${progress.total}题)`}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ 
              width: `${((currentQuestionIndex + 1) / Math.max(questions.length, progress.total)) * 100}%` 
            }}
          ></div>
        </div>
        {isLoading && (
          <p className="text-xs text-blue-600 mt-1">
            正在生成更多题目... ({progress.percentage}% 完成)
          </p>
        )}
      </Card>

      {/* 当前题目 */}
      {currentQuestion && (
        <Card className="p-6">
          <div className="space-y-4">
            {/* 题目标题 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-blue-600 font-medium">
                  {currentQuestion.dimension} 维度
                </span>
                <span className="text-xs text-gray-500">
                  第 {currentQuestion.order} 题
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 leading-relaxed">
                {currentQuestion.text}
              </h3>
            </div>

            {/* 选项 */}
            <div className="space-y-3">
              {currentQuestion.options.map((option) => (
                <label
                  key={option.id}
                  className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <input
                    type="radio"
                    name={currentQuestion.id}
                    value={option.score}
                    checked={answers[currentQuestion.id] === option.score}
                    onChange={() => handleAnswerChange(currentQuestion.id, option.score)}
                    className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="ml-3 text-gray-700 flex-1">
                    {option.text}
                  </span>
                  <span className="text-xs text-gray-500">
                    {option.score}分
                  </span>
                </label>
              ))}
            </div>

            {/* 导航按钮 */}
            <div className="flex justify-between pt-4">
              <Button
                onClick={handlePrevious}
                disabled={currentQuestionIndex === 0}
                variant="outline"
              >
                上一题
              </Button>

              <div className="flex space-x-3">
                {!isLastQuestion ? (
                  <Button
                    onClick={handleNext}
                    disabled={!canProceed}
                  >
                    下一题
                  </Button>
                ) : shouldWaitForMore ? (
                  <Button
                    disabled={true}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    等待更多题目... ({questions.length}/{progress.total})
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={!canProceed}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    提交问卷
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* 已回答题目概览 */}
      {Object.keys(answers).length > 0 && (
        <Card className="p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            已回答题目 ({Object.keys(answers).length})
          </h4>
          <div className="grid grid-cols-10 gap-2">
            {questions.map((question, index) => (
              <button
                key={question.id}
                onClick={() => setCurrentQuestionIndex(index)}
                className={`
                  w-8 h-8 rounded text-xs font-medium transition-colors
                  ${answers[question.id] 
                    ? 'bg-green-100 text-green-700 border border-green-300' 
                    : 'bg-gray-100 text-gray-500 border border-gray-300'
                  }
                  ${index === currentQuestionIndex 
                    ? 'ring-2 ring-blue-500' 
                    : 'hover:bg-gray-200'
                  }
                `}
              >
                {index + 1}
              </button>
            ))}
          </div>
        </Card>
      )}
    </div>
  )
}
