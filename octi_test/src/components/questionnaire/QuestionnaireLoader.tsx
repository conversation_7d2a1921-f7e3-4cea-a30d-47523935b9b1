'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { QuestionnaireConfig, QuestionnaireResponse, Question } from '@/types'

// 流式题目类型定义
interface StreamQuestion {
  id: string
  dimension: string
  type: string
  text: string
  options: Array<{
    id: string
    text: string
    score: number
  }>
  required: boolean
  order: number
}
import { Loading } from '@/components/ui/Loading'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { QuestionnaireRenderer } from './QuestionnaireRenderer'
import { StreamQuestionnaireRenderer } from './StreamQuestionnaireRenderer'

interface QuestionnaireLoaderProps {
  version: 'standard' | 'professional'
  organizationId?: string
  onComplete?: (responses: QuestionnaireResponse[]) => Promise<void>
  onConfigLoaded?: (config: QuestionnaireConfig) => void
}

interface LoaderState {
  config: QuestionnaireConfig | null
  loading: boolean
  error: string | null
  retryCount: number
  // 新增流式加载状态
  streamLoading: boolean
  loadedQuestions: StreamQuestion[]
  totalExpected: number
  currentProgress: {
    completed: number
    total: number
    percentage: number
  }
  questionnaireId: string | null
}

/**
 * 问卷加载器组件
 * 负责从API加载问卷配置并渲染问卷界面
 */
export const QuestionnaireLoader: React.FC<QuestionnaireLoaderProps> = ({ 
  version, 
  organizationId, 
  onComplete,
  onConfigLoaded 
}) => {
  // 统一状态管理
  const [state, setState] = useState<LoaderState>({
    config: null,
    loading: true,
    error: null,
    retryCount: 0,
    // 流式加载状态初始化
    streamLoading: false,
    loadedQuestions: [],
    totalExpected: 60,
    currentProgress: {
      completed: 0,
      total: 60,
      percentage: 0
    },
    questionnaireId: null
  })

  /**
   * 转换API数据为QuestionnaireConfig格式
   */
  const transformApiData = useCallback((apiData: any): QuestionnaireConfig => {
    const questionnaireConfig: QuestionnaireConfig = {
      version: apiData.version || version,
      total_questions: apiData.questions?.length || 0,
      dimensions: {
        SF: { questions: [] },
        IT: { questions: [] },
        MV: { questions: [] },
        AD: { questions: [] }
      }
    }

    // 按维度分组问题
    if (apiData.questions && Array.isArray(apiData.questions)) {
      apiData.questions.forEach((q: any) => {
        // 处理维度名称映射
        let dimensionKey: 'SF' | 'IT' | 'MV' | 'AD'
        switch (q.dimension) {
          case 'S/F':
            dimensionKey = 'SF'
            break
          case 'I/T':
            dimensionKey = 'IT'
            break
          case 'M/V':
            dimensionKey = 'MV'
            break
          case 'A/D':
            dimensionKey = 'AD'
            break
          default:
            console.warn(`未知维度: ${q.dimension}，默认使用SF`)
            dimensionKey = 'SF'
        }
        
        const configQuestion: Question = {
          id: q.id,
          dimension: dimensionKey,
          sub_dimension: q.subdimension || '',
          type: q.type as 'choice' | 'scenario' | 'ranking' | 'scale',
          text: q.title || q.text || '',
          options: q.options?.map((opt: any, index: number) => ({
            id: opt.id || `${q.id}_opt_${index}`,
            text: opt.text || opt,
            value: opt.value || opt,
            score: opt.score || index + 1
          })) || [],
          scoring: {
            dimension_weight: q.weight || 1,
            sub_dimension_weight: 1,
            option_scores: q.options?.map((_: any, index: number) => index + 1) || [],
            reverse_scoring: false
          }
        }
        
        questionnaireConfig.dimensions[dimensionKey].questions.push(configQuestion)
      })
    }

    return questionnaireConfig
  }, [version])

  /**
   * 流式加载问卷配置 - 新的核心方法
   */
  const loadQuestionnaireStreamly = useCallback(async () => {
    if (state.retryCount >= 3) {
      setState(prev => ({
        ...prev,
        error: '重试次数过多，请稍后再试',
        loading: false,
        streamLoading: false
      }))
      return
    }

    setState(prev => ({
      ...prev,
      loading: true,
      streamLoading: true,
      error: null,
      loadedQuestions: [],
      currentProgress: { completed: 0, total: 60, percentage: 0 }
    }))

    try {
      console.log('开始流式加载问卷配置:', { version, organizationId })

      // 第一步：启动问卷生成，获取第一批题目
      const firstBatchResponse = await fetch('/api/questionnaire/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          organizationType: 'technology',
          version,
          batchSize: 5
        })
      })

      if (!firstBatchResponse.ok) {
        throw new Error(`启动问卷生成失败: ${firstBatchResponse.status}`)
      }

      const firstBatch = await firstBatchResponse.json()

      if (!firstBatch.success) {
        throw new Error(firstBatch.error || '启动问卷生成失败')
      }

      console.log('收到第一批题目:', firstBatch.data)

      // 更新状态：显示第一批题目
      setState(prev => ({
        ...prev,
        loadedQuestions: firstBatch.data.questions,
        totalExpected: firstBatch.data.totalExpected,
        currentProgress: firstBatch.data.progress,
        questionnaireId: firstBatch.data.questionnaireId,
        loading: false // 第一批加载完成，可以开始显示
      }))

      // 第二步：继续加载剩余题目
      await loadRemainingQuestions(firstBatch.data)

    } catch (err) {
      console.error('流式加载问卷配置失败:', err)
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : '加载问卷配置失败',
        loading: false,
        streamLoading: false,
        retryCount: prev.retryCount + 1
      }))
    }
  }, [version, organizationId, state.retryCount])

  /**
   * 加载剩余题目
   */
  const loadRemainingQuestions = async (initialBatch: any) => {
    let nextBatch = initialBatch.nextBatch

    while (nextBatch && state.streamLoading) {
      try {
        console.log(`加载下一批题目: ${nextBatch.dimension} 从 ${nextBatch.startIndex}`)

        const response = await fetch(
          `/api/questionnaire/batch?questionnaireId=${initialBatch.questionnaireId}&dimension=${nextBatch.dimension}&startIndex=${nextBatch.startIndex}&batchSize=5`
        )

        if (!response.ok) {
          console.warn(`批次加载失败: ${response.status}，跳过此批次`)
          break
        }

        const batchData = await response.json()

        if (!batchData.success) {
          console.warn('批次数据无效，跳过此批次')
          break
        }

        console.log(`收到批次数据:`, batchData.data)

        // 累积添加新题目
        setState(prev => ({
          ...prev,
          loadedQuestions: [...prev.loadedQuestions, ...batchData.data.questions],
          currentProgress: batchData.data.progress
        }))

        // 准备下一批
        nextBatch = batchData.data.nextBatch

        // 如果没有下一批，说明全部完成
        if (!nextBatch) {
          setState(prev => ({
            ...prev,
            streamLoading: false
          }))
          console.log('所有题目加载完成!')
          break
        }

        // 短暂延迟，避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error('加载批次失败:', error)
        // 不中断整个流程，继续尝试下一批
        break
      }
    }
  }

  /**
   * 传统的完整加载方法（保留作为备用）
   * 加载问卷配置
   */
  const loadQuestionnaireConfig = useCallback(async () => {
    try {
      setState(prev => ({ 
        ...prev, 
        loading: true, 
        error: null 
      }))

      console.log('开始加载问卷配置:', { version, organizationId })

      // 创建AbortController用于超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15 * 60 * 1000) // 15分钟超时

      try {
        const response = await fetch('/api/questionnaire/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            version,
            organizationType: 'technology',
            organizationId
          }),
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          throw new Error(`生成问卷失败: ${response.status} ${response.statusText}`)
        }

        const result = await response.json()

        if (!result.success) {
          throw new Error(result.error?.message || '生成问卷失败')
        }

        console.log('API返回数据:', result.data)

        // 转换API数据
        const questionnaireConfig = transformApiData(result.data)

        setState(prev => ({
          ...prev,
          config: questionnaireConfig,
          loading: false,
          retryCount: 0
        }))

        // 通知父组件配置已加载
        if (onConfigLoaded) {
          onConfigLoaded(questionnaireConfig)
        }

      } catch (error) {
        clearTimeout(timeoutId)
        if (error instanceof Error && error.name === 'AbortError') {
          throw new Error('请求超时，问卷生成时间过长，请稍后重试')
        }
        throw error
      }
      
    } catch (err) {
      console.error('加载问卷配置失败:', err)
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : '加载问卷配置失败',
        loading: false,
        retryCount: prev.retryCount + 1
      }))
    }
  }, [version, organizationId, onConfigLoaded, transformApiData])

  /**
   * 处理问卷完成
   */
  const handleQuestionnaireComplete = useCallback(async (responses: QuestionnaireResponse[]) => {
    console.log('问卷完成，回答数量:', responses.length)
    
    try {
      if (onComplete) {
        await onComplete(responses)
      }
    } catch (error) {
      console.error('处理问卷完成失败:', error)
      // 这里可以添加错误处理逻辑
    }
  }, [onComplete])

  /**
   * 重试加载
   */
  const handleRetry = useCallback(() => {
    if (state.retryCount < 3) {
      loadQuestionnaireConfig()
    } else {
      setState(prev => ({
        ...prev,
        error: '重试次数过多，请刷新页面重试'
      }))
    }
  }, [loadQuestionnaireConfig, state.retryCount])

  // 初始化加载 - 使用流式加载
  useEffect(() => {
    loadQuestionnaireStreamly()
  }, [loadQuestionnaireStreamly])

  // 渲染初始加载状态（只在第一批题目加载前显示）
  if (state.loading && state.loadedQuestions.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <div>
            <p className="text-gray-600 font-medium">正在生成第一批题目...</p>
            <p className="text-sm text-gray-400 mt-1">版本: {version}</p>
            {organizationId && (
              <p className="text-sm text-gray-400">组织ID: {organizationId}</p>
            )}
          </div>
        </div>
      </div>
    )
  }

  // 渲染错误状态
  if (state.error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="max-w-md w-full">
          <div className="text-center p-6">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
            <p className="text-gray-600 mb-4 text-sm">{state.error}</p>
            
            <div className="space-y-2">
              <Button
                onClick={handleRetry}
                disabled={state.retryCount >= 3}
                className="w-full"
              >
                {state.retryCount >= 3 ? '重试次数已用完' : `重新加载 (${state.retryCount}/3)`}
              </Button>
              
              {state.retryCount >= 3 && (
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="w-full"
                >
                  刷新页面
                </Button>
              )}
            </div>
            
            <div className="mt-4 text-xs text-gray-400">
              <p>版本: {version}</p>
              {organizationId && <p>组织ID: {organizationId}</p>}
            </div>
          </div>
        </Card>
      </div>
    )
  }

  // 渲染流式加载状态 - 显示已加载的题目
  if (state.loadedQuestions.length > 0) {
    return (
      <div className="space-y-6">
        {/* 流式加载进度提示 */}
        <Card className={`border-2 ${state.streamLoading ? 'bg-blue-50 border-blue-200' : 'bg-green-50 border-green-200'}`}>
          <div className="p-4">
            <div className="flex items-center">
              <div className={`text-xl mr-3 ${state.streamLoading ? 'text-blue-500' : 'text-green-500'}`}>
                {state.streamLoading ? '🔄' : '✅'}
              </div>
              <div className="flex-1">
                <h3 className={`text-sm font-semibold ${state.streamLoading ? 'text-blue-900' : 'text-green-900'}`}>
                  {state.streamLoading ? '问卷生成中...' : '问卷生成完成'}
                </h3>
                <p className={`text-sm ${state.streamLoading ? 'text-blue-700' : 'text-green-700'}`}>
                  已生成 {state.loadedQuestions.length} / {state.totalExpected} 道题目
                </p>
                {state.streamLoading && (
                  <div className="mt-2">
                    <div className="w-full bg-blue-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${state.currentProgress.percentage}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      {state.currentProgress.percentage}% 完成
                    </p>
                  </div>
                )}
              </div>
              <div className="text-right text-xs text-gray-600">
                <div>版本: {version}</div>
                <div>已加载: {state.loadedQuestions.length} 题</div>
              </div>
            </div>
          </div>
        </Card>

        {/* 使用流式渲染器 */}
        <StreamQuestionnaireRenderer
          questions={state.loadedQuestions}
          isLoading={state.streamLoading}
          progress={state.currentProgress}
          onAnswerChange={(questionId, answer) => {
            console.log('答案变更:', questionId, answer)
          }}
          onComplete={(responses) => {
            console.log('问卷完成:', responses)
            if (handleQuestionnaireComplete) {
              // 转换为旧格式兼容
              const convertedResponses = responses.map(r => ({
                questionId: r.questionId,
                answer: r.answer,
                timestamp: r.timestamp
              }))
              handleQuestionnaireComplete(convertedResponses as any)
            }
          }}
          className="bg-white"
        />
      </div>
    )
  }

  // 传统的完整加载成功状态（保留兼容性）
  if (state.config) {
    return (
      <div className="space-y-6">
        <QuestionnaireRenderer
          config={state.config}
          onComplete={handleQuestionnaireComplete}
          className="bg-white"
        />
      </div>
    )
  }

  return null
}

// 默认导出以保持兼容性
export default QuestionnaireLoader
