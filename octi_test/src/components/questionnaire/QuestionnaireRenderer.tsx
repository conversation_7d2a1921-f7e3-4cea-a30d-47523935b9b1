'use client'

import React from 'react'
import { QuestionnaireConfig, QuestionnaireResponse } from '@/types'
import { useQuestionnaire } from '@/hooks/useQuestionnaire'
import { QuestionDisplay } from './QuestionDisplay'
import { QuestionnaireProgress } from './QuestionnaireProgress'
import { Card } from '@/components/ui/Card'
import { Loading } from '@/components/ui/Loading'
import { Button } from '@/components/ui/Button'

interface QuestionnaireRendererProps {
  config: QuestionnaireConfig
  initialResponses?: QuestionnaireResponse[]
  onComplete: (responses: QuestionnaireResponse[]) => Promise<void>
  onSave?: (responses: QuestionnaireResponse[]) => Promise<void>
  className?: string
}

/**
 * 问卷渲染器主组件
 * 负责协调各个子组件，保持简洁
 */
export const QuestionnaireRenderer: React.FC<QuestionnaireRendererProps> = ({
  config,
  initialResponses = [],
  onComplete,
  onSave,
  className
}) => {
  const {
    currentQuestion,
    currentQuestionIndex,
    responses,
    errors,
    isLoading,
    isFirst,
    isLast,
    handleNext,
    handlePrevious,
    updateResponse,
    progress
  } = useQuestionnaire({
    config,
    initialResponses,
    onComplete,
    onSave
  })

  // 获取当前问题的回答值
  const getCurrentValue = () => {
    if (!currentQuestion) return undefined
    const response = responses.find(r => r.questionId === currentQuestion.id)
    return response?.answer
  }

  // 处理答案更新
  const handleAnswerChange = (value: any) => {
    if (currentQuestion) {
      updateResponse(currentQuestion.id, value)
    }
  }

  // 获取当前问题的维度
  const getCurrentDimension = () => {
    return currentQuestion?.dimension || 'SF'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loading size="lg" text="正在提交问卷..." />
      </div>
    )
  }

  return (
    <div className={className}>
      {/* 进度条 */}
      <QuestionnaireProgress
        current={currentQuestionIndex + 1}
        total={config.total_questions}
        progress={progress}
        dimension={getCurrentDimension()}
      />
      
      {/* 问题内容 */}
      <Card className="p-6 mt-4">
        {currentQuestion ? (
          <QuestionDisplay
            question={currentQuestion}
            value={getCurrentValue()}
            onChange={handleAnswerChange}
            error={errors[currentQuestion.id]}
          />
        ) : (
          <div className="text-center py-8">
            <div className="text-gray-500">问题加载中...</div>
          </div>
        )}

        {/* 导航按钮 */}
        <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={isFirst || isLoading}
            className="px-6 py-2"
          >
            上一题
          </Button>

          <div className="text-sm text-gray-500">
            第 {currentQuestionIndex + 1} 题，共 {config.total_questions} 题
          </div>

          <Button
            onClick={handleNext}
            disabled={isLoading}
            className="px-6 py-2"
          >
            {isLast ? '提交问卷' : '下一题'}
          </Button>
        </div>

        {/* 错误提示 */}
        {currentQuestion && errors[currentQuestion.id] && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-red-700 text-sm">{errors[currentQuestion.id]}</span>
            </div>
          </div>
        )}
      </Card>
    </div>
  )
}
