'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { ArrowRight, Zap, Target, BarChart3 } from 'lucide-react'

interface HeroSectionProps {
  onStartAssessment: () => void
  onLearnMore: () => void
}

/**
 * 首页品牌展示区域组件
 * 展示OCTI品牌、核心价值主张和主要CTA按钮
 */
export const HeroSection: React.FC<HeroSectionProps> = ({
  onStartAssessment,
  onLearnMore
}) => {
  return (
    <section className="relative bg-gradient-to-b from-blue-50 to-white py-20 px-6">
      <div className="max-w-6xl mx-auto text-center">
        {/* 品牌Logo和标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mr-4">
              <span className="text-2xl font-bold text-white">OCTI</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              OCTI 智能评估系统
            </h1>
          </div>
          
          {/* 核心价值主张 */}
          <p className="text-xl md:text-2xl text-gray-600 mb-4 max-w-4xl mx-auto">
            基于四维八极理论的组织能力智能诊断
          </p>
          <p className="text-lg text-gray-500 mb-8 max-w-3xl mx-auto">
            运用先进的AI技术，为您的组织提供科学、准确、全面的能力评估，
            助力组织发展和人才培养
          </p>
        </div>

        {/* 核心特性展示 */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="flex flex-col items-center p-6 bg-white rounded-xl shadow-sm">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <Zap className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">智能分析</h3>
            <p className="text-gray-600 text-center">
              基于AI算法的深度分析，提供精准的能力评估结果
            </p>
          </div>
          
          <div className="flex flex-col items-center p-6 bg-white rounded-xl shadow-sm">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <Target className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">科学理论</h3>
            <p className="text-gray-600 text-center">
              基于四维八极理论，16种组织类型全面覆盖
            </p>
          </div>
          
          <div className="flex flex-col items-center p-6 bg-white rounded-xl shadow-sm">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">专业报告</h3>
            <p className="text-gray-600 text-center">
              详细的分析报告和改进建议，助力组织发展
            </p>
          </div>
        </div>

        {/* CTA按钮组 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button
            onClick={onStartAssessment}
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
          >
            开始OCTI评估
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
          
          <Button
            onClick={onLearnMore}
            size="lg"
            variant="outline"
            className="border-2 border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200"
          >
            了解更多
          </Button>
        </div>

        {/* 统计数据展示 */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">10,000+</div>
            <div className="text-gray-600">评估完成</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">500+</div>
            <div className="text-gray-600">企业客户</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">16</div>
            <div className="text-gray-600">组织类型</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">98%</div>
            <div className="text-gray-600">满意度</div>
          </div>
        </div>
      </div>
      
      {/* 背景装饰 */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-green-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-20 w-24 h-24 bg-purple-200 rounded-full opacity-20 animate-pulse delay-2000"></div>
      </div>
    </section>
  )
}
