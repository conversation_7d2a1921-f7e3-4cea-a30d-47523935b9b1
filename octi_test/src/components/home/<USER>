'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Brain, 
  Network, 
  Target, 
  TrendingUp, 
  Users, 
  Lightbulb, 
  Shield, 
  Zap,
  ArrowRight,
  BookOpen
} from 'lucide-react'

interface OCTIIntroductionProps {
  onLearnMore: () => void
}

/**
 * OCTI四维八极理论介绍组件
 * 展示理论背景、模型结构和应用价值
 */
export const OCTIIntroduction: React.FC<OCTIIntroductionProps> = ({
  onLearnMore
}) => {
  // 四维能力数据
  const dimensions = [
    {
      name: '认知维度',
      icon: Brain,
      color: 'blue',
      description: '组织的学习能力、创新思维和知识管理水平',
      capabilities: ['学习能力', '创新思维', '知识管理', '决策质量']
    },
    {
      name: '关系维度', 
      icon: Network,
      color: 'green',
      description: '组织内外部关系建立、维护和协调能力',
      capabilities: ['团队协作', '沟通交流', '合作伙伴', '客户关系']
    },
    {
      name: '执行维度',
      icon: Target,
      color: 'purple',
      description: '组织的目标达成、任务执行和结果交付能力',
      capabilities: ['目标管理', '执行效率', '质量控制', '结果导向']
    },
    {
      name: '适应维度',
      icon: TrendingUp,
      color: 'orange',
      description: '组织面对变化的应对、调整和发展能力',
      capabilities: ['变化应对', '灵活调整', '持续改进', '发展规划']
    }
  ]

  // 16种组织类型示例
  const organizationTypes = [
    { name: '创新先锋型', code: 'CCEE', description: '高认知高执行，引领行业创新' },
    { name: '协作高效型', code: 'RREE', description: '强关系强执行，团队协作卓越' },
    { name: '学习适应型', code: 'CCAA', description: '高认知强适应，持续学习成长' },
    { name: '稳健发展型', code: 'RRAA', description: '好关系强适应，稳步持续发展' },
    { name: '执行专家型', code: 'EEEE', description: '超强执行力，目标达成专家' },
    { name: '变革领导型', code: 'AAAA', description: '超强适应力，变革管理领导者' },
    { name: '平衡发展型', code: 'CREA', description: '四维均衡，全面协调发展' },
    { name: '潜力成长型', code: 'CRCE', description: '基础扎实，具备成长潜力' }
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-100 text-blue-600 border-blue-200',
      green: 'bg-green-100 text-green-600 border-green-200',
      purple: 'bg-purple-100 text-purple-600 border-purple-200',
      orange: 'bg-orange-100 text-orange-600 border-orange-200'
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl mb-6">
            <BookOpen className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            OCTI四维八极理论
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            基于组织行为学和管理科学的前沿研究，构建科学的组织能力评估框架
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
            <span className="flex items-center">
              <Shield className="w-4 h-4 mr-1" />
              科学权威
            </span>
            <span className="flex items-center">
              <Users className="w-4 h-4 mr-1" />
              实践验证
            </span>
            <span className="flex items-center">
              <Lightbulb className="w-4 h-4 mr-1" />
              持续优化
            </span>
          </div>
        </div>

        {/* 四维能力模型 */}
        <div className="mb-20">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-12">
            四维能力模型
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {dimensions.map((dimension, index) => {
              const IconComponent = dimension.icon
              return (
                <Card key={index} className="relative overflow-hidden hover:shadow-lg transition-all duration-300">
                  <CardHeader className="text-center pb-4">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4 ${getColorClasses(dimension.color)}`}>
                      <IconComponent className="w-6 h-6" />
                    </div>
                    <CardTitle className="text-lg font-bold text-gray-900">
                      {dimension.name}
                    </CardTitle>
                    <CardDescription className="text-sm text-gray-600">
                      {dimension.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {dimension.capabilities.map((capability, capIndex) => (
                        <div key={capIndex} className="flex items-center text-sm text-gray-700">
                          <div className={`w-2 h-2 rounded-full mr-2 ${dimension.color === 'blue' ? 'bg-blue-400' : 
                            dimension.color === 'green' ? 'bg-green-400' :
                            dimension.color === 'purple' ? 'bg-purple-400' : 'bg-orange-400'}`} />
                          {capability}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* 八极分析法 */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              八极分析法
            </h3>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              每个维度分为高低两极，形成2⁴=16种组织类型，精准识别组织特征
            </p>
          </div>

          {/* 可视化模型图 */}
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-12">
            <div className="relative">
              {/* 中心圆 */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="text-lg font-bold">OCTI</div>
                  <div className="text-sm">四维八极</div>
                </div>
              </div>

              {/* 四个维度的圆环 */}
              <div className="grid grid-cols-2 gap-8 max-w-2xl mx-auto">
                {dimensions.map((dimension, index) => {
                  const IconComponent = dimension.icon
                  return (
                    <div key={index} className="text-center">
                      <div className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-3 ${getColorClasses(dimension.color)}`}>
                        <IconComponent className="w-8 h-8" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-1">{dimension.name}</h4>
                      <div className="text-xs text-gray-500">
                        <div>高极 ↔ 低极</div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>

        {/* 16种组织类型 */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              16种组织类型
            </h3>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              基于四维八极组合，识别出16种不同的组织能力类型，每种类型都有独特的特征和发展路径
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {organizationTypes.map((type, index) => (
              <Card key={index} className="hover:shadow-md transition-all duration-200 border-l-4 border-l-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-gray-900 text-sm">{type.name}</h4>
                    <span className="text-xs font-mono bg-gray-100 px-2 py-1 rounded">
                      {type.code}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600">{type.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <p className="text-sm text-gray-500 mb-4">
              * C=认知高, R=关系高, E=执行高, A=适应高
            </p>
            <Button
              onClick={onLearnMore}
              variant="outline"
              className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
            >
              查看完整类型说明
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 应用价值 */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              应用价值
            </h3>
            <p className="text-lg text-gray-600">
              OCTI理论在组织发展中的实际应用价值
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">精准诊断</h4>
              <p className="text-sm text-gray-600">
                科学识别组织能力现状，发现优势和短板
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">发展规划</h4>
              <p className="text-sm text-gray-600">
                基于类型特征制定个性化发展策略
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">持续改进</h4>
              <p className="text-sm text-gray-600">
                跟踪评估效果，支持组织持续优化
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
