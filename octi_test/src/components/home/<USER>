'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Play, 
  FileText, 
  BarChart3, 
  Download, 
  Clock, 
  Users, 
  MessageCircle, 
  ArrowRight,
  CheckCircle,
  HelpCircle,
  Phone,
  Mail
} from 'lucide-react'

interface UserGuideProps {
  onStartAssessment: () => void
  onContactSupport: () => void
}

/**
 * 用户引导组件
 * 展示评估流程、常见问题和联系方式
 */
export const UserGuide: React.FC<UserGuideProps> = ({
  onStartAssessment,
  onContactSupport
}) => {
  // 评估流程步骤
  const assessmentSteps = [
    {
      step: 1,
      title: '选择评估方案',
      description: '根据组织规模和需求选择标准版或专业版',
      icon: Play,
      duration: '1分钟',
      details: ['了解两种方案差异', '选择适合的评估类型', '确认评估范围']
    },
    {
      step: 2,
      title: '完成问卷调研',
      description: '回答60道专业设计的组织能力评估题目',
      icon: FileText,
      duration: '15-25分钟',
      details: ['四维能力全面覆盖', '情景化问题设计', '支持保存和继续']
    },
    {
      step: 3,
      title: '智能分析处理',
      description: 'AI系统基于OCTI理论进行深度分析',
      icon: BarChart3,
      duration: '3-5分钟',
      details: ['多维度数据分析', '组织类型识别', '能力模型匹配']
    },
    {
      step: 4,
      title: '获取专业报告',
      description: '查看详细的分析报告和发展建议',
      icon: Download,
      duration: '随时查看',
      details: ['可视化能力雷达图', '详细分析报告', 'PDF格式下载']
    }
  ]

  // 常见问题
  const faqs = [
    {
      question: '评估需要多长时间？',
      answer: '标准版评估大约需要15-20分钟完成问卷，专业版可能需要20-30分钟。系统支持保存进度，您可以随时暂停和继续。'
    },
    {
      question: '评估结果的准确性如何？',
      answer: 'OCTI评估基于科学的组织行为学理论，经过大量实践验证。我们的AI分析系统结合了多种先进算法，确保结果的专业性和准确性。'
    },
    {
      question: '数据安全如何保障？',
      answer: '我们采用银行级别的数据加密技术，所有数据传输和存储都经过严格加密。您的评估数据仅用于生成报告，不会用于其他用途。'
    },
    {
      question: '可以重复评估吗？',
      answer: '可以。标准版用户可以重新购买评估，专业版用户享有6个月内2次免费复测机会，帮助您跟踪组织能力的改进效果。'
    },
    {
      question: '如何理解评估报告？',
      answer: '报告包含四维能力雷达图、组织类型分析、详细的能力评估和发展建议。专业版还提供专家解读和实施指导。'
    },
    {
      question: '支持团队评估吗？',
      answer: '支持。您可以邀请团队成员分别完成评估，我们提供团队版本的综合分析报告，帮助了解团队整体能力分布。'
    }
  ]

  // 客户案例
  const testimonials = [
    {
      company: '某科技创业公司',
      industry: '互联网科技',
      size: '50人',
      feedback: 'OCTI评估帮助我们清晰地认识到团队在执行力方面的短板，制定的改进计划让我们的项目交付效率提升了40%。',
      type: '标准版'
    },
    {
      company: '某制造企业',
      industry: '智能制造',
      size: '200人',
      feedback: '专业版的深度分析让我们发现了组织结构中的关键问题，专家的指导帮助我们成功完成了组织变革。',
      type: '专业版'
    },
    {
      company: '某教育机构',
      industry: '在线教育',
      size: '80人',
      feedback: '通过OCTI评估，我们识别出了自己的组织类型，并据此调整了管理策略，团队协作效率明显提升。',
      type: '标准版'
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 评估流程 */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              简单4步，完成专业评估
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              科学的评估流程，确保结果的专业性和准确性
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {assessmentSteps.map((step, index) => {
              const IconComponent = step.icon
              return (
                <div key={index} className="relative">
                  {/* 连接线 */}
                  {index < assessmentSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-blue-300 to-transparent z-0" />
                  )}
                  
                  <Card className="relative z-10 hover:shadow-lg transition-all duration-300 border-t-4 border-t-blue-500">
                    <CardHeader className="text-center pb-4">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 relative">
                        <IconComponent className="w-8 h-8 text-blue-600" />
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          {step.step}
                        </div>
                      </div>
                      <CardTitle className="text-lg font-bold text-gray-900 mb-2">
                        {step.title}
                      </CardTitle>
                      <CardDescription className="text-sm text-gray-600 mb-3">
                        {step.description}
                      </CardDescription>
                      <div className="flex items-center justify-center text-sm text-blue-600 font-medium">
                        <Clock className="w-4 h-4 mr-1" />
                        {step.duration}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-start text-sm text-gray-600">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              )
            })}
          </div>

          <div className="text-center mt-12">
            <Button
              onClick={onStartAssessment}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
            >
              立即开始评估
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              常见问题解答
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              解答您关于OCTI评估的疑问
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {faqs.map((faq, index) => (
              <Card key={index} className="hover:shadow-md transition-all duration-200">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-gray-900 flex items-start">
                    <HelpCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                    {faq.question}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* 客户案例 */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              客户成功案例
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              看看其他组织如何通过OCTI评估实现能力提升
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <CardTitle className="text-lg font-bold text-gray-900">
                        {testimonial.company}
                      </CardTitle>
                      <CardDescription className="text-sm text-gray-600">
                        {testimonial.industry} • {testimonial.size}
                      </CardDescription>
                    </div>
                    <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-xs font-semibold">
                      {testimonial.type}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <blockquote className="text-gray-700 italic leading-relaxed">
                    "{testimonial.feedback}"
                  </blockquote>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* 联系支持 */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              需要帮助？联系我们
            </h2>
            <p className="text-lg text-gray-600">
              我们的专业团队随时为您提供支持和指导
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">在线客服</h3>
              <p className="text-sm text-gray-600 mb-3">工作日 9:00-18:00</p>
              <Button variant="outline" size="sm" className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white">
                开始对话
              </Button>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Phone className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">电话咨询</h3>
              <p className="text-sm text-gray-600 mb-3">************</p>
              <Button variant="outline" size="sm" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">
                立即拨打
              </Button>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Mail className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">邮件支持</h3>
              <p className="text-sm text-gray-600 mb-3"><EMAIL></p>
              <Button variant="outline" size="sm" className="border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white">
                发送邮件
              </Button>
            </div>
          </div>

          <div className="text-center">
            <Button
              onClick={onContactSupport}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl"
            >
              联系专业顾问
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
