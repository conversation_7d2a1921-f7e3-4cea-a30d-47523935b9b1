'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Check, X, Star, ArrowRight } from 'lucide-react'

interface FeatureComparisonProps {
  onStartStandard: () => void
  onStartProfessional: () => void
}

/**
 * 功能对比表组件
 * 详细展示标准版和专业版的功能差异
 */
export const FeatureComparison: React.FC<FeatureComparisonProps> = ({
  onStartStandard,
  onStartProfessional
}) => {
  // 功能对比数据
  const features = [
    {
      category: '评估内容',
      items: [
        { name: '评估题目数量', standard: '60题', professional: '60题+定制题目' },
        { name: '四维能力分析', standard: true, professional: true },
        { name: '组织类型识别', standard: '16种基础类型', professional: '16种+细分子类型' },
        { name: '能力雷达图', standard: true, professional: true },
        { name: '多源数据融合', standard: false, professional: true },
        { name: '文档上传分析', standard: false, professional: true }
      ]
    },
    {
      category: '分析深度',
      items: [
        { name: 'AI分析模型', standard: '单模型分析', professional: '双模型协作' },
        { name: '分析报告章节', standard: '9个章节', professional: '14个章节' },
        { name: '发展建议详细度', standard: '基础建议', professional: '详细行动计划' },
        { name: '个性化程度', standard: '标准化分析', professional: '高度个性化' },
        { name: '行业对标分析', standard: false, professional: true },
        { name: '竞争力评估', standard: false, professional: true }
      ]
    },
    {
      category: '报告功能',
      items: [
        { name: 'PDF报告下载', standard: true, professional: true },
        { name: '在线查看', standard: true, professional: true },
        { name: '数据导出', standard: '基础数据', professional: '完整数据+图表' },
        { name: '报告定制', standard: false, professional: true },
        { name: '多语言支持', standard: false, professional: true },
        { name: '品牌定制', standard: false, professional: true }
      ]
    },
    {
      category: '服务支持',
      items: [
        { name: '评估有效期', standard: '6个月', professional: '12个月' },
        { name: '免费复测次数', standard: '0次', professional: '2次' },
        { name: '专家咨询', standard: false, professional: '1小时咨询' },
        { name: '实施指导', standard: false, professional: true },
        { name: '进度跟踪', standard: false, professional: true },
        { name: '优先支持', standard: false, professional: true }
      ]
    }
  ]

  const renderFeatureValue = (value: string | boolean, isProfessional: boolean = false) => {
    if (typeof value === 'boolean') {
      return value ? (
        <Check className="w-5 h-5 text-green-500" />
      ) : (
        <X className="w-5 h-5 text-gray-300" />
      )
    }
    return (
      <span className={`text-sm ${isProfessional ? 'font-semibold text-purple-700' : 'text-gray-700'}`}>
        {value}
      </span>
    )
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            功能详细对比
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            深入了解标准版和专业版的功能差异，选择最适合您组织需求的评估方案
          </p>
        </div>

        {/* 对比表格 */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          {/* 表头 */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 px-6 py-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center md:text-left">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">功能项目</h3>
                <p className="text-sm text-gray-600">详细功能对比</p>
              </div>
              
              <div className="text-center">
                <div className="bg-white rounded-xl p-6 shadow-sm border-2 border-blue-200">
                  <h3 className="text-xl font-bold text-blue-600 mb-2">标准版</h3>
                  <div className="text-3xl font-bold text-blue-600 mb-2">¥99</div>
                  <p className="text-sm text-gray-600 mb-4">基础组织能力诊断</p>
                  <Button
                    onClick={onStartStandard}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    选择标准版
                  </Button>
                </div>
              </div>
              
              <div className="text-center">
                <div className="bg-white rounded-xl p-6 shadow-sm border-2 border-purple-200 relative">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-xs font-semibold flex items-center">
                      <Star className="w-3 h-3 mr-1" />
                      推荐
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-purple-600 mb-2">专业版</h3>
                  <div className="text-3xl font-bold text-purple-600 mb-2">¥399</div>
                  <p className="text-sm text-gray-600 mb-4">深度组织能力诊断</p>
                  <Button
                    onClick={onStartProfessional}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
                  >
                    选择专业版
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* 功能对比内容 */}
          <div className="divide-y divide-gray-200">
            {features.map((category, categoryIndex) => (
              <div key={categoryIndex} className="p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <div className="w-2 h-6 bg-blue-500 rounded-full mr-3"></div>
                  {category.category}
                </h4>
                
                <div className="space-y-4">
                  {category.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center py-3 hover:bg-gray-50 rounded-lg px-4 transition-colors">
                      <div className="font-medium text-gray-900">
                        {item.name}
                      </div>
                      
                      <div className="text-center">
                        {renderFeatureValue(item.standard)}
                      </div>
                      
                      <div className="text-center">
                        {renderFeatureValue(item.professional, true)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 底部说明 */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader>
              <CardTitle className="text-blue-600">标准版适合</CardTitle>
              <CardDescription>
                以下类型的组织建议选择标准版评估
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  初次进行组织能力评估的企业
                </li>
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  50人以下的小型组织
                </li>
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  希望快速了解组织现状的团队
                </li>
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  预算有限但需要专业评估的组织
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardHeader>
              <CardTitle className="text-purple-600">专业版适合</CardTitle>
              <CardDescription>
                以下类型的组织建议选择专业版评估
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  50人以上的中大型组织
                </li>
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  需要制定详细改进计划的企业
                </li>
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  希望获得专家指导的组织
                </li>
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  需要持续跟踪改进效果的团队
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* CTA区域 */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              还有疑问？
            </h3>
            <p className="text-lg text-gray-600 mb-6">
              我们的专业团队随时为您提供咨询服务，帮助您选择最适合的评估方案
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="outline"
                className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
              >
                在线咨询
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                className="border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white"
              >
                预约演示
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
