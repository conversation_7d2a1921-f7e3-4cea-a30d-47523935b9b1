'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Check, 
  Star, 
  ArrowRight, 
  Zap, 
  Target, 
  BarChart3, 
  FileText, 
  Users, 
  Clock, 
  Shield 
} from 'lucide-react'

interface AssessmentCardsProps {
  onStartStandard: () => void
  onStartProfessional: () => void
}

/**
 * 评估类型选择卡片组件
 * 展示标准版和专业版的功能对比和价格
 */
export const AssessmentCards: React.FC<AssessmentCardsProps> = ({
  onStartStandard,
  onStartProfessional
}) => {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            选择适合您的评估方案
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            基于OCTI四维八极理论，提供标准版和专业版两种评估方案，满足不同组织的需求
          </p>
        </div>

        {/* 评估卡片 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* 标准版卡片 */}
          <Card className="relative border-2 border-gray-200 hover:border-blue-300 transition-all duration-300 hover:shadow-lg">
            <CardHeader className="text-center pb-8">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Target className="w-8 h-8 text-blue-600" />
              </div>
              <CardTitle className="text-2xl font-bold text-gray-900">标准版评估</CardTitle>
              <CardDescription className="text-lg text-gray-600">
                基础组织能力诊断
              </CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold text-blue-600">¥99</span>
                <span className="text-gray-500 ml-2">/ 次评估</span>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* 功能特点 */}
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">60题标准深度评估</p>
                    <p className="text-sm text-gray-600">覆盖四维八极核心能力指标</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">四维能力雷达图</p>
                    <p className="text-sm text-gray-600">可视化展示组织能力分布</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">组织类型识别</p>
                    <p className="text-sm text-gray-600">16种组织类型精准匹配</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">基础发展建议</p>
                    <p className="text-sm text-gray-600">9个章节详细分析报告</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">PDF报告下载</p>
                    <p className="text-sm text-gray-600">专业格式，便于分享</p>
                  </div>
                </div>
              </div>

              {/* 适用场景 */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">适用场景</h4>
                <p className="text-sm text-blue-700">
                  适合初次评估、小型组织或希望快速了解组织能力现状的用户
                </p>
              </div>

              {/* CTA按钮 */}
              <Button
                onClick={onStartStandard}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-semibold rounded-xl shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                开始标准版评估
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </CardContent>
          </Card>

          {/* 专业版卡片 */}
          <Card className="relative border-2 border-purple-200 hover:border-purple-300 transition-all duration-300 hover:shadow-xl bg-gradient-to-br from-white to-purple-50">
            {/* 推荐标签 */}
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center">
                <Star className="w-4 h-4 mr-1" />
                推荐方案
              </div>
            </div>

            <CardHeader className="text-center pb-8 pt-12">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-purple-600" />
              </div>
              <CardTitle className="text-2xl font-bold text-gray-900">专业版评估</CardTitle>
              <CardDescription className="text-lg text-gray-600">
                深度组织能力诊断与分析
              </CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold text-purple-600">¥399</span>
                <span className="text-gray-500 ml-2">/ 次评估</span>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* 包含标准版所有功能 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600 font-medium">包含标准版所有功能，另外增加：</p>
              </div>

              {/* 专业版独有功能 */}
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">多源数据融合分析</p>
                    <p className="text-sm text-gray-600">支持文档上传和网络数据采集</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">双模型协作分析</p>
                    <p className="text-sm text-gray-600">MiniMax + DeepSeek深度分析</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">详细专业报告</p>
                    <p className="text-sm text-gray-600">14个章节深度分析报告</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">个性化发展路径</p>
                    <p className="text-sm text-gray-600">定制化改进建议和行动计划</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Check className="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">6个月免费复测</p>
                    <p className="text-sm text-gray-600">跟踪改进效果，持续优化</p>
                  </div>
                </div>
              </div>

              {/* 适用场景 */}
              <div className="bg-purple-50 rounded-lg p-4">
                <h4 className="font-medium text-purple-900 mb-2">适用场景</h4>
                <p className="text-sm text-purple-700">
                  适合中大型组织、需要深度分析或制定详细改进计划的用户
                </p>
              </div>

              {/* CTA按钮 */}
              <Button
                onClick={onStartProfessional}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 text-lg font-semibold rounded-xl shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                开始专业版评估
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 底部说明 */}
        <div className="mt-12 text-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="flex items-center justify-center space-x-2 text-gray-600">
              <Shield className="w-5 h-5" />
              <span className="text-sm">数据安全保护</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-gray-600">
              <Clock className="w-5 h-5" />
              <span className="text-sm">15-30分钟完成</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-gray-600">
              <Users className="w-5 h-5" />
              <span className="text-sm">专业团队支持</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
