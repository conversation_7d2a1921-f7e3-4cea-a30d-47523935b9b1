import { LLMApiClient, LLMRequest } from '@/services/llm/llm-api-client'
import { Logger } from '@/lib/logger'
import { BaseAgent, AgentInput, AgentOutput, AgentStatus } from '@/types/agents'
import { PromptBuilder } from '@/services/llm/prompt-builder'
import { DataFusionEngine } from '@/services/data/data-fusion-engine'

const logger = new Logger('QuestionDesignerAgent')

export interface DesignOptions {
  version: 'standard' | 'professional'
  organizationType: string
  targetAudience: string
  customRequirements: string
  questionCount: number
  dimensions: string[]
  industryContext?: string
}

export interface Questionnaire {
  id: string
  title: string
  description: string
  version: string
  organizationType: string
  assessmentType: string
  dimensions: string[]
  questions: Question[]
  totalQuestions: number
  estimatedTime: string
  createdAt: string
  metadata?: {
    generationTime: string
    model: string
    provider: string
  }
}

export type QuestionType = 'single_choice' | 'multiple_choice' | 'scale' | 'open_ended' | 'ranking' | 'matrix' | 'likert_scale' | 'choice' | 'scenario';

export interface Question {
  id: string
  dimension: string
  subdimension?: string
  type: QuestionType
  depth?: string
  text: string
  title?: string
  description?: string
  options?: QuestionOption[]
  scale?: any
  reversed?: boolean
  weight?: number
  metadata?: any
  required: boolean
  order: number
}

export interface QuestionOption {
  id: string
  text: string
  score: number
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
  score: number
  qualityScore?: number
}

/**
 * 问卷设计师智能体
 */
export class QuestionDesignerAgent implements BaseAgent {
  public readonly name = 'question_designer';
  private logger: Logger;
  private initialized = false;
  private lastActivity = new Date();
  private questionCache = new Map<string, Questionnaire>();

  constructor(
    private llmClient: LLMApiClient,
    private promptBuilder: PromptBuilder,
    private dataFusionEngine: DataFusionEngine
  ) {
    this.logger = new Logger('QuestionDesignerAgent');
  }

  /**
   * 初始化智能体
   */
  async initialize(): Promise<void> {
    this.logger.info('问卷设计师智能体初始化完成');
    this.initialized = true;
    this.lastActivity = new Date();
  }

  /**
   * 获取智能体状态
   */
  getStatus(): AgentStatus {
    return {
      name: this.name,
      initialized: this.initialized,
      lastActivity: this.lastActivity,
      config: {
        llmProvider: 'minimax',
        version: '1.0.0'
      }
    };
  }

  /**
   * 执行智能体任务
   */
  async execute(input: AgentInput): Promise<AgentOutput> {
    this.logger.info('执行问卷设计任务', { input });
    this.lastActivity = new Date();

    try {
      // 这里应该调用现有的 designQuestionnaire 方法
      // 为了保持兼容性，我们将输入转换为 DesignOptions 格式
      const designOptions: DesignOptions = {
        version: input.version || 'standard',
        organizationType: input.organizationType || '公益组织',
        targetAudience: input.targetAudience || '组织成员',
        customRequirements: input.customRequirements || '',
        questionCount: input.questionCount || 20,
        dimensions: input.dimensions || ['战略规划', '团队协作', '资源管理', '影响力']
      };

      const questionnaire = await this.design(designOptions);

      return {
        success: true,
        data: questionnaire
      };
    } catch (error) {
      this.logger.error('问卷设计失败', { error });
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 设计问卷
   */
  async design(options: DesignOptions): Promise<Questionnaire> {
    const startTime = Date.now()

    try {
      logger.info('开始分批设计问卷', options)

      // 使用分批生成策略
      const questionnaire = await this.generateQuestionnaireBatched(options)

      const generationTime = ((Date.now() - startTime) / 1000).toFixed(1) + 's'

      // 添加元数据
      questionnaire.metadata = {
        generationTime,
        model: 'deepseek-chat',
        provider: 'deepseek'
      }

      logger.info('问卷设计完成', {
        questionCount: questionnaire.questions.length,
        generationTime
      })

      return questionnaire

    } catch (error) {
      logger.error('问卷设计失败', { error, options })
      throw error
    }
  }

  /**
   * 构建LLM提示词
   */
  private buildPrompt(options: DesignOptions) {
    const systemPrompt = `你是OCTI（Organization Capability Type Indicator）智能评估系统的问卷设计专家。

OCTI评估框架包含四个核心维度：
1. S/F (Structure/Flexibility) - 结构化与灵活性
2. I/T (Innovation/Tradition) - 创新性与传统性  
3. M/V (Management/Vision) - 管理导向与愿景导向
4. A/D (Action/Decision) - 行动导向与决策导向

请严格按照以下要求设计问卷：
- 每个维度15道题，总共60道题
- 每道题5个选项，分值1-5分
- 题目要针对${options.organizationType}类型组织
- ${options.version === 'professional' ? '专业版需要更深入的情境化问题' : '标准版使用通用性问题'}
- 确保题目的专业性和实用性

重要：
1. 必须返回完整的JSON格式，不要截断
2. 使用紧凑的JSON格式，减少不必要的空格
3. 确保所有60道题目都包含在响应中`

    const userPrompt = `请为${options.organizationType}类型组织设计一套${options.version}版OCTI评估问卷。

具体要求：
- 组织类型：${options.organizationType}
- 目标受众：${options.targetAudience}
- 版本：${options.version}
${options.customRequirements ? `- 特殊要求：${options.customRequirements}` : ''}

请严格按照以下JSON格式返回完整的60道题目：

\`\`\`json
{
  "id": "questionnaire_${Date.now()}",
  "title": "问卷标题",
  "description": "问卷描述",
  "version": "${options.version}",
  "organizationType": "${options.organizationType}",
  "assessmentType": "${options.version === 'professional' ? 'professional' : 'basic'}",
  "dimensions": ["S/F", "I/T", "M/V", "A/D"],
  "questions": [
    {
      "id": "q_1",
      "dimension": "S/F",
      "type": "single_choice",
      "title": "问题标题",
      "description": "问题描述",
      "options": [
        {"id": "o_1", "text": "完全不同意", "score": 1},
        {"id": "o_2", "text": "不同意", "score": 2},
        {"id": "o_3", "text": "中立", "score": 3},
        {"id": "o_4", "text": "同意", "score": 4},
        {"id": "o_5", "text": "完全同意", "score": 5}
      ],
      "required": true,
      "order": 1
    }
  ]
}
\`\`\`

注意：
1. 必须包含完整的60道题目
2. 每个维度15道题
3. 确保JSON格式完整，不要截断
4. 所有字段都必须填写完整`

    return {
      systemPrompt,
      userPrompt
    }
  }

  /**
   * 解析LLM响应
   */
  private parseResponse(content: string, options: DesignOptions): Questionnaire {
    try {
      console.log('原始LLM响应长度:', content.length)
      console.log('原始LLM响应前500字符:', content.substring(0, 500))

      // 检查响应是否被截断
      if (content.length < 1000) {
        console.warn('LLM响应长度过短，可能被截断:', content.length)
      }

      // 尝试多种JSON提取方式
      let jsonStr = ''

      // 方式1: 提取```json代码块
      const jsonBlockMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
      if (jsonBlockMatch) {
        jsonStr = jsonBlockMatch[1].trim()
        console.log('提取到JSON代码块，长度:', jsonStr.length)
      }

      // 方式2: 提取完整的JSON对象
      if (!jsonStr) {
        const jsonObjectMatch = content.match(/\{[\s\S]*\}/)
        if (jsonObjectMatch) {
          jsonStr = jsonObjectMatch[0].trim()
          console.log('提取到JSON对象，长度:', jsonStr.length)
        }
      }
      
      // 方式3: 查找最后一个完整的}
      if (!jsonStr) {
        const startIndex = content.indexOf('{')
        if (startIndex !== -1) {
          let braceCount = 0
          let endIndex = -1
          
          for (let i = startIndex; i < content.length; i++) {
            if (content[i] === '{') braceCount++
            if (content[i] === '}') {
              braceCount--
              if (braceCount === 0) {
                endIndex = i
                break
              }
            }
          }
          
          if (endIndex !== -1) {
            jsonStr = content.substring(startIndex, endIndex + 1)
            console.log('通过括号匹配提取JSON，长度:', jsonStr.length)
          }
        }
      }
      
      if (jsonStr) {
        try {
          const parsedContent = JSON.parse(jsonStr)
          console.log('JSON解析成功，问题数量:', parsedContent.questions?.length || 0)
          return this.validateAndFormatQuestionnaire(parsedContent, options)
        } catch (parseError) {
          console.error('JSON解析失败:', parseError)
          console.log('尝试修复JSON...')
          
          // 尝试修复常见的JSON问题
          const fixedJson = this.fixJsonString(jsonStr)
          if (fixedJson) {
            try {
              const parsedContent = JSON.parse(fixedJson)
              console.log('修复后JSON解析成功，问题数量:', parsedContent.questions?.length || 0)
              return this.validateAndFormatQuestionnaire(parsedContent, options)
            } catch (fixError) {
              console.error('修复后仍然解析失败:', fixError)
            }
          }
        }
      }
      
      // 如果JSON解析都失败，使用结构化解析
      console.log('JSON解析失败，使用结构化解析')
      return this.parseStructuredResponse(content, options)
      
    } catch (error) {
      console.error('响应解析完全失败:', error)
      logger.error('LLM响应解析失败', { 
        error: error instanceof Error ? error.message : String(error),
        contentPreview: content.substring(0, 500)
      })
      throw new Error(`问卷解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 尝试修复JSON字符串
   */
  private fixJsonString(jsonStr: string): string | null {
    try {
      // 移除可能的控制字符
      let fixed = jsonStr.replace(/[\x00-\x1F\x7F]/g, '')
      
      // 修复常见的引号问题
      fixed = fixed.replace(/'/g, '"')
      
      // 修复末尾缺少的括号或引号
      if (!fixed.endsWith('}')) {
        // 尝试找到最后一个完整的对象
        const lastCompleteObject = fixed.lastIndexOf('"}')
        if (lastCompleteObject !== -1) {
          fixed = fixed.substring(0, lastCompleteObject + 2) + '}'
        }
      }
      
      // 验证修复后的JSON
      JSON.parse(fixed)
      return fixed
    } catch (error) {
      console.error('JSON修复失败:', error)
      return null
    }
  }

  /**
   * 验证和格式化解析后的问卷
   */
  private validateAndFormatQuestionnaire(parsedContent: any, options: DesignOptions): Questionnaire {
    const errors: string[] = []
    
    if (!parsedContent.id) {
      errors.push('问卷ID不能为空')
    }
    if (!parsedContent.title) {
      errors.push('问卷标题不能为空')
    }
    if (!parsedContent.description) {
      errors.push('问卷描述不能为空')
    }
    if (!parsedContent.version) {
      errors.push('问卷版本不能为空')
    }
    if (!parsedContent.organizationType) {
      errors.push('组织类型不能为空')
    }
    if (!parsedContent.assessmentType) {
      errors.push('评估类型不能为空')
    }
    if (!parsedContent.dimensions || !Array.isArray(parsedContent.dimensions) || parsedContent.dimensions.length !== 4) {
      errors.push('问卷必须包含四个维度')
    }
    if (!parsedContent.questions || !Array.isArray(parsedContent.questions) || parsedContent.questions.length !== 60) {
      errors.push('问卷必须包含60道题')
    }
    
    if (errors.length > 0) {
      throw new Error(`问卷解析失败：${errors.join(', ')}`)
    }
    
    return {
      id: parsedContent.id,
      title: parsedContent.title,
      description: parsedContent.description,
      version: parsedContent.version,
      organizationType: parsedContent.organizationType,
      assessmentType: parsedContent.assessmentType,
      dimensions: parsedContent.dimensions,
      questions: parsedContent.questions.map((q: any, index: number) => ({
        id: q.id || `q_${index + 1}`,
        dimension: q.dimension,
        type: q.type,
        title: q.title,
        description: q.description || '',
        options: q.options.map((o: any, optionIndex: number) => ({
          id: o.id || `o_${index + 1}_${optionIndex + 1}`,
          text: o.text,
          score: o.score
        })),
        required: q.required || true,
        order: q.order || index + 1
      })),
      totalQuestions: parsedContent.questions.length,
      estimatedTime: this.calculateEstimatedTime(parsedContent.questions),
      createdAt: new Date().toISOString(),
      metadata: {
        generationTime: '',
        model: '',
        provider: ''
      }
    }
  }

  /**
   * 改进的结构化解析
   */
  private parseStructuredResponse(content: string, options: DesignOptions): Questionnaire {
    console.log('开始结构化解析，内容长度:', content.length)
    
    const result: Questionnaire = {
      id: `questionnaire_${Date.now()}`,
      title: `${options.organizationType}组织OCTI评估问卷`,
      description: `针对${options.organizationType}类型组织的OCTI能力评估问卷`,
      version: options.version,
      organizationType: options.organizationType,
      assessmentType: options.version === 'professional' ? 'professional' : 'basic',
      dimensions: options.dimensions,
      questions: [],
      totalQuestions: 0,
      estimatedTime: '',
      createdAt: new Date().toISOString(),
      metadata: {
        generationTime: '',
        model: '',
        provider: ''
      }
    }

    // 如果结构化解析也失败，生成默认问卷
    if (result.questions.length === 0) {
      console.log('结构化解析失败，生成默认问卷')
      result.questions = this.generateDefaultQuestions(options)
    }

    result.totalQuestions = result.questions.length
    result.estimatedTime = this.calculateEstimatedTime(result.questions)

    console.log('结构化解析完成，问题数量:', result.questions.length)
    return result
  }

  private extractValue(line: string): string {
    const colonIndex = line.indexOf(':')
    return colonIndex > -1 ? line.substring(colonIndex + 1).trim() : line
  }

  // 解析问题列表
  private parseQuestions(rawQuestions: any[]): Question[] {
    return rawQuestions.map((rawQ, index) => {
      const question: Question = {
        id: rawQ.id || `q_${index + 1}`,
        dimension: rawQ.dimension || this.inferDimension(rawQ.text),
        subdimension: rawQ.subdimension || '',
        type: rawQ.type || this.inferQuestionType(rawQ.text),
        depth: rawQ.depth || 'intermediate',
        text: rawQ.text || rawQ.question || '',
        options: rawQ.options,
        scale: rawQ.scale,
        reversed: rawQ.reversed || false,
        weight: rawQ.weight || 1,
        required: rawQ.required !== false,
        order: rawQ.order || index + 1,
        metadata: {
          difficulty: rawQ.difficulty || 0.5,
          discriminationIndex: rawQ.discriminationIndex || 0.5,
          expectedResponseTime: rawQ.expectedResponseTime || 30,
        },
      };

      // 为选择题生成默认选项
      if ((question.type === 'single_choice' || question.type === 'multiple_choice') && !question.options) {
        question.options = this.generateDefaultOptions(question.type);
      }

      // 为量表题生成默认量表
      if (question.type === 'likert_scale' && !question.scale) {
        question.scale = {
          min: 1,
          max: 5,
          labels: ['完全不同意', '不同意', '中立', '同意', '完全同意'],
        };
      }

      return question;
    });
  }

  // 推断问题类型
  private inferQuestionType(text: string): QuestionType {
    if (text.includes('排序') || text.includes('排列')) {
      return 'ranking';
    }
    if (text.includes('多选') || text.includes('可以选择多个')) {
      return 'multiple_choice';
    }
    if (text.includes('同意') || text.includes('程度')) {
      return 'likert_scale';
    }
    if (text.includes('描述') || text.includes('说明') || text.includes('举例')) {
      return 'open_ended';
    }
    return 'single_choice';
  }

  // 推断维度
  private inferDimension(text: string): string {
    if (text.includes('权力') || text.includes('等级') || text.includes('层级')) {
      return '权力距离';
    }
    if (text.includes('个人') || text.includes('集体') || text.includes('团队')) {
      return '个人主义vs集体主义';
    }
    if (text.includes('竞争') || text.includes('合作') || text.includes('成就')) {
      return '男性化vs女性化';
    }
    if (text.includes('不确定') || text.includes('风险') || text.includes('变化')) {
      return '不确定性规避';
    }
    return '综合';
  }

  // 生成默认选项
  private generateDefaultOptions(type: QuestionType): QuestionOption[] {
    if (type === 'single_choice' || type === 'multiple_choice') {
      return [
        { id: 'opt_1', text: '完全不符合', score: 1 },
        { id: 'opt_2', text: '基本不符合', score: 2 },
        { id: 'opt_3', text: '部分符合', score: 3 },
        { id: 'opt_4', text: '基本符合', score: 4 },
        { id: 'opt_5', text: '完全符合', score: 5 },
      ];
    }
    return [];
  }

  // 验证问卷
  private async validateQuestionnaire(questionnaire: Questionnaire): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 基本验证
    if (!questionnaire.title) {
      errors.push('问卷标题不能为空');
    }
    
    if (questionnaire.questions.length === 0) {
      errors.push('问卷必须包含至少一个问题');
    }

    // 问题数量验证
    const expectedQuestions = questionnaire.version === 'professional' ? 40 : 20;
    if (questionnaire.questions.length < expectedQuestions * 0.8) {
      warnings.push(`问题数量偏少，建议至少${expectedQuestions}个问题`);
    }

    // 维度覆盖验证
    const dimensions = new Set(questionnaire.questions.map(q => q.dimension));
    if (dimensions.size < 4) {
      errors.push('问卷应覆盖OCTI四个维度');
    }

    // 问题类型分布验证
    const typeDistribution = this.analyzeQuestionTypes(questionnaire.questions);
    if (typeDistribution.likert_scale < 0.5) {
      suggestions.push('建议增加更多量表题以提高测量精度');
    }

    // 问题质量验证
    for (const question of questionnaire.questions) {
      if (!question.text || question.text.length < 10) {
        errors.push(`问题${question.id}内容过短`);
      }
      
      if (question.type === 'single_choice' && (!question.options || question.options.length < 3)) {
        errors.push(`问题${question.id}选项不足`);
      }
    }

    // 计算质量分数
    let qualityScore = 1.0;
    qualityScore -= errors.length * 0.2;
    qualityScore -= warnings.length * 0.1;
    qualityScore = Math.max(0, qualityScore);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score: qualityScore,
      qualityScore,
    };
  }

  // 重新生成问卷
  private async regenerateQuestionnaire(
    options: DesignOptions,
    suggestions: string[]
  ): Promise<Questionnaire> {
    logger.info('Regenerating questionnaire with improvements', { suggestions });
    
    // 添加改进建议到自定义要求
    const improvedOptions = {
      ...options,
      customRequirements: [
        options.customRequirements || '',
        '请特别注意以下改进建议：',
        ...suggestions,
      ].filter(Boolean).join('\n'),
    };

    // 递归调用，但限制重试次数
    return this.design(improvedOptions);
  }

  // 分析问题类型分布
  private analyzeQuestionTypes(questions: Question[]): Record<QuestionType, number> {
    const distribution: Record<QuestionType, number> = {
      single_choice: 0,
      multiple_choice: 0,
      scale: 0,
      open_ended: 0,
      ranking: 0,
      matrix: 0,
      likert_scale: 0,
      choice: 0,
      scenario: 0,
    };

    questions.forEach(q => {
      distribution[q.type] = (distribution[q.type] || 0) + 1;
    });

    // 转换为比例
    const total = questions.length;
    Object.keys(distribution).forEach(key => {
      distribution[key as QuestionType] = distribution[key as QuestionType] / total;
    });

    return distribution;
  }

  // 计算预估时间
  private calculateEstimatedTime(questions: any[]): string {
    const totalSeconds = questions.reduce((total, q) => {
      const baseTime = q.type === 'open_ended' ? 60 : 30;
      return total + (q.expectedResponseTime || baseTime);
    }, 0);

    const minutes = Math.ceil(totalSeconds / 60);
    return `${minutes}分钟`;
  }

  // 计算难度
  private calculateDifficulty(questions: any[]): number {
    if (questions.length === 0) return 0.5;
    
    const avgDifficulty = questions.reduce((sum, q) => {
      return sum + (q.difficulty || 0.5);
    }, 0) / questions.length;
    
    return avgDifficulty;
  }

  // 生成问卷ID
  private generateQuestionnaireId(): string {
    return `questionnaire_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 生成缓存键
  private generateCacheKey(options: DesignOptions): string {
    const keyParts = [
      options.version,
      options.organizationType || 'default',
      options.industryContext || 'default',
      options.targetAudience || 'default',
      options.customRequirements || 'default',
    ];
    
    return keyParts.join('_').replace(/[^a-zA-Z0-9_]/g, '_');
  }

  // 获取问卷预览
  async getQuestionnairePreview(questionnaireId: string): Promise<{
    title: string;
    description: string;
    questionCount: number;
    estimatedTime: number;
    dimensions: string[];
  } | null> {
    // 从缓存中查找
    for (const questionnaire of Array.from(this.questionCache.values())) {
      if (questionnaire.id === questionnaireId) {
        return {
          title: questionnaire.title,
          description: questionnaire.description,
          questionCount: questionnaire.questions.length,
          estimatedTime: parseInt(questionnaire.estimatedTime) || 20,
          dimensions: Array.from(new Set(questionnaire.questions.map(q => q.dimension))),
        };
      }
    }
    
    return null;
  }

  // 清除缓存
  clearCache(): void {
    this.questionCache.clear();
    logger.info('QuestionDesignerAgent cache cleared');
  }

  // 获取统计信息
  getStats(): {
    cacheSize: number;
    totalQuestionnaires: number;
    versionDistribution: Record<string, number>;
  } {
    const questionnaires = Array.from(this.questionCache.values());
    const versionDistribution: Record<string, number> = {};
    
    questionnaires.forEach((q: Questionnaire) => {
      versionDistribution[q.version] = (versionDistribution[q.version] || 0) + 1;
    });
    
    return {
      cacheSize: this.questionCache.size,
      totalQuestionnaires: questionnaires.length,
      versionDistribution,
    };
  }

  /**
   * 分批生成问卷 - 核心实现
   */
  async generateQuestionnaireBatched(options: DesignOptions): Promise<Questionnaire> {
    const dimensions = ['S/F', 'I/T', 'M/V', 'A/D']
    const questionsPerBatch = 5 // 每批生成5道题
    const questionsPerDimension = 15

    // 创建基础问卷结构
    const questionnaire: Questionnaire = {
      id: `questionnaire_${Date.now()}`,
      title: `OCTI智能评估 - ${options.organizationType}`,
      description: `基于OCTI四维八极理论的${options.organizationType}组织能力评估`,
      version: options.version,
      organizationType: options.organizationType,
      assessmentType: options.version === 'professional' ? 'professional' : 'basic',
      dimensions: dimensions,
      questions: [],
      totalQuestions: 60,
      estimatedTime: '20分钟',
      createdAt: new Date().toISOString()
    }

    // 为每个维度分批生成题目
    for (const dimension of dimensions) {
      logger.info(`开始生成维度 ${dimension} 的题目`)

      for (let batch = 0; batch < Math.ceil(questionsPerDimension / questionsPerBatch); batch++) {
        const startIndex = batch * questionsPerBatch
        const endIndex = Math.min(startIndex + questionsPerBatch, questionsPerDimension)
        const questionsInBatch = endIndex - startIndex

        try {
          const batchQuestions = await this.generateQuestionBatch(
            dimension,
            questionsInBatch,
            startIndex + 1,
            options
          )

          questionnaire.questions.push(...batchQuestions)
          logger.info(`维度 ${dimension} 第 ${batch + 1} 批题目生成完成`, {
            batchSize: batchQuestions.length,
            totalGenerated: questionnaire.questions.length
          })

          // 缓存当前进度
          this.questionCache.set(questionnaire.id, questionnaire)

        } catch (error) {
          logger.warn(`维度 ${dimension} 第 ${batch + 1} 批生成失败，使用默认题目`, { error })

          // 生成默认题目作为后备
          const defaultQuestions = this.generateDefaultQuestionsBatch(
            dimension,
            questionsInBatch,
            startIndex + 1
          )
          questionnaire.questions.push(...defaultQuestions)
        }
      }
    }

    // 更新最终统计
    questionnaire.totalQuestions = questionnaire.questions.length
    questionnaire.estimatedTime = this.calculateEstimatedTime(questionnaire.questions)

    return questionnaire
  }

  /**
   * 生成单批次题目
   */
  async generateQuestionBatch(
    dimension: string,
    questionCount: number,
    startIndex: number,
    options: DesignOptions
  ): Promise<Question[]> {
    const prompt = this.buildBatchPrompt(dimension, questionCount, startIndex, options)

    const llmRequest: LLMRequest = {
      model: 'deepseek-chat',
      messages: [
        { role: 'system', content: prompt.systemPrompt },
        { role: 'user', content: prompt.userPrompt }
      ],
      temperature: 0.7,
      maxTokens: 3000 // 较小的token限制，因为只生成几道题
    }

    const provider = 'deepseek'
    const response = await this.llmClient.chat(provider, llmRequest)

    return this.parseBatchResponse(response.content || '', dimension, startIndex)
  }

  /**
   * 构建批次提示词
   */
  private buildBatchPrompt(dimension: string, questionCount: number, startIndex: number, options: DesignOptions) {
    const dimensionDescriptions = {
      'S/F': '结构化与灵活性 - 评估组织在规范化管理与灵活应变之间的平衡',
      'I/T': '创新性与传统性 - 评估组织在创新突破与传统稳定之间的取向',
      'M/V': '管理导向与愿景导向 - 评估组织在日常管理与长远愿景之间的重点',
      'A/D': '行动导向与决策导向 - 评估组织在快速行动与深度决策之间的偏好'
    }

    const systemPrompt = `你是OCTI问卷设计专家，专门为${dimension}维度生成高质量的评估题目。

${dimension}维度说明：${dimensionDescriptions[dimension as keyof typeof dimensionDescriptions]}

请生成${questionCount}道针对${options.organizationType}类型组织的专业题目。`

    const userPrompt = `请为${dimension}维度生成${questionCount}道题目（编号从${startIndex}开始）。

要求：
- 每道题5个选项，分值1-5分
- 题目要具体、实用、针对${options.organizationType}
- ${options.version === 'professional' ? '使用深度情境化问题' : '使用通用性问题'}

请严格按照以下JSON格式返回：

\`\`\`json
{
  "questions": [
    {
      "id": "q_${startIndex}",
      "dimension": "${dimension}",
      "type": "single_choice",
      "text": "具体的问题文本",
      "options": [
        {"id": "o_${startIndex}_1", "text": "完全不符合", "score": 1},
        {"id": "o_${startIndex}_2", "text": "基本不符合", "score": 2},
        {"id": "o_${startIndex}_3", "text": "部分符合", "score": 3},
        {"id": "o_${startIndex}_4", "text": "基本符合", "score": 4},
        {"id": "o_${startIndex}_5", "text": "完全符合", "score": 5}
      ],
      "required": true,
      "order": ${startIndex}
    }
  ]
}
\`\`\`

确保返回完整的JSON格式。`

    return { systemPrompt, userPrompt }
  }

  /**
   * 解析批次响应
   */
  private parseBatchResponse(content: string, dimension: string, startIndex: number): Question[] {
    try {
      console.log('批次响应内容长度:', content.length)
      console.log('批次响应前500字符:', content.substring(0, 500))

      // 提取JSON
      let jsonStr = ''
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
      if (jsonMatch) {
        jsonStr = jsonMatch[1].trim()
      } else {
        const objectMatch = content.match(/\{[\s\S]*/)
        if (objectMatch) {
          jsonStr = objectMatch[0].trim()
        }
      }

      if (!jsonStr) {
        throw new Error('未找到JSON格式')
      }

      console.log('提取的JSON字符串长度:', jsonStr.length)

      // 尝试修复不完整的JSON
      let fixedJson = this.fixBatchJson(jsonStr)
      console.log('修复后的JSON:', fixedJson.substring(0, 200) + '...')

      const parsed = JSON.parse(fixedJson)

      if (parsed.questions && Array.isArray(parsed.questions)) {
        const questions = parsed.questions.map((q: any, index: number) => ({
          id: q.id || `q_${startIndex + index}`,
          dimension: q.dimension || dimension,
          type: q.type || 'single_choice',
          text: q.text || q.title || `${dimension}维度问题 ${startIndex + index}`,
          options: q.options || this.generateDefaultOptions('single_choice'),
          required: q.required !== false,
          order: q.order || startIndex + index
        }))

        console.log(`成功解析 ${questions.length} 道题目`)
        return questions
      }

      throw new Error('解析的JSON中没有questions数组')
    } catch (error) {
      console.error('批次解析失败:', error)
      logger.warn(`批次解析失败，使用默认题目`, { error, dimension, startIndex })
      return this.generateDefaultQuestionsBatch(dimension, 1, startIndex)
    }
  }

  /**
   * 修复不完整的批次JSON
   */
  private fixBatchJson(jsonStr: string): string {
    try {
      // 如果JSON已经完整，直接返回
      JSON.parse(jsonStr)
      return jsonStr
    } catch (error) {
      console.log('JSON不完整，尝试修复...')

      let fixed = jsonStr

      // 确保有questions数组开始
      if (!fixed.includes('"questions"')) {
        throw new Error('JSON中没有questions字段')
      }

      // 修复可能缺少的结尾
      if (!fixed.endsWith('}')) {
        // 找到最后一个完整的问题对象
        const lastCompleteQuestion = fixed.lastIndexOf('"order"')
        if (lastCompleteQuestion !== -1) {
          // 找到这个order字段的值结尾
          const afterOrder = fixed.substring(lastCompleteQuestion)
          const numberMatch = afterOrder.match(/"order":\s*(\d+)/)
          if (numberMatch) {
            const endPos = lastCompleteQuestion + afterOrder.indexOf(numberMatch[1]) + numberMatch[1].length
            fixed = fixed.substring(0, endPos) + '\n     }\n   ]\n}'
          }
        }
      }

      // 验证修复结果
      JSON.parse(fixed)
      console.log('JSON修复成功')
      return fixed
    }
  }

  /**
   * 生成默认题目批次
   */
  private generateDefaultQuestionsBatch(dimension: string, questionCount: number, startIndex: number): Question[] {
    const questions: Question[] = []

    for (let i = 0; i < questionCount; i++) {
      const questionIndex = startIndex + i
      questions.push({
        id: `q_${questionIndex}`,
        dimension,
        type: 'single_choice',
        text: `关于${dimension}维度的评估问题 ${questionIndex}`,
        options: this.generateDefaultOptions('single_choice'),
        required: true,
        order: questionIndex
      })
    }

    return questions
  }

  /**
   * 生成默认问卷（当LLM解析失败时的备用方案）
   */
  private generateDefaultQuestions(options: DesignOptions): Question[] {
    const questions: Question[] = []
    const dimensionQuestions = {
      'S/F': [
        '您的组织更倾向于遵循既定的流程和规范',
        '在面对新情况时，您的组织能够快速调整策略',
        '您的组织有明确的层级结构和职责分工',
        '您的组织鼓励员工在工作中发挥创造性',
        '您的组织重视标准化的工作流程'
      ],
      'I/T': [
        '您的组织积极采用新技术和新方法',
        '您的组织重视传统经验和成熟做法',
        '您的组织鼓励员工提出创新想法',
        '您的组织在决策时会充分考虑历史经验',
        '您的组织愿意承担创新带来的风险'
      ],
      'M/V': [
        '您的组织注重日常运营管理的效率',
        '您的组织有清晰的长远发展愿景',
        '您的组织重视绩效指标的达成',
        '您的组织经常讨论未来发展方向',
        '您的组织善于制定详细的执行计划'
      ],
      'A/D': [
        '您的组织在决策时会进行充分的分析和讨论',
        '您的组织能够快速响应市场变化',
        '您的组织重视数据分析在决策中的作用',
        '您的组织鼓励员工主动采取行动',
        '您的组织在重要决策前会征求多方意见'
      ]
    }

    let questionId = 1
    options.dimensions.forEach(dimension => {
      const dimensionTexts = dimensionQuestions[dimension as keyof typeof dimensionQuestions] || []
      dimensionTexts.forEach(text => {
        questions.push({
          id: `q_${questionId}`,
          dimension,
          type: 'single_choice',
          text: text,
          options: [
            { id: `o_${questionId}_1`, text: '完全不同意', score: 1 },
            { id: `o_${questionId}_2`, text: '不同意', score: 2 },
            { id: `o_${questionId}_3`, text: '中立', score: 3 },
            { id: `o_${questionId}_4`, text: '同意', score: 4 },
            { id: `o_${questionId}_5`, text: '完全同意', score: 5 }
          ],
          required: true,
          order: questionId
        })
        questionId++
      })
    })

    // 补充到60题
    while (questions.length < 60) {
      const dimension = options.dimensions[questions.length % 4]
      questions.push({
        id: `q_${questions.length + 1}`,
        dimension,
        type: 'single_choice',
        text: `关于${dimension}维度的评估问题 ${questions.length + 1}`,
        options: [
          { id: `o_${questions.length + 1}_1`, text: '完全不同意', score: 1 },
          { id: `o_${questions.length + 1}_2`, text: '不同意', score: 2 },
          { id: `o_${questions.length + 1}_3`, text: '中立', score: 3 },
          { id: `o_${questions.length + 1}_4`, text: '同意', score: 4 },
          { id: `o_${questions.length + 1}_5`, text: '完全同意', score: 5 }
        ],
        required: true,
        order: questions.length + 1
      })
    }

    return questions
  }
}
