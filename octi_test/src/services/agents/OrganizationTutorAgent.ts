import { Logger } from '@/lib/logger';
import { LLMApiClient, type LLMRequest, type LLMResponse } from '../llm/llm-api-client';
import { PromptBuilder } from '../llm/prompt-builder';
import { DataFusionEngine } from '../data/data-fusion-engine';
import { PromptBuildOptions, DataFusionConfig, RawData } from '@/types/agents';
import { BaseAgent, AgentStatus } from '@/types/agents';

const logger = new Logger('OrganizationTutorAgent');

// 评估数据接口
export interface AssessmentData {
  organizationId: string;
  responses: Array<{
    questionId: string;
    answer: number | string;
    dimension: string;
    subdimension: string;
  }>;
  metadata: {
    completedAt: string;
    version: 'standard' | 'professional';
    participantCount: number;
    organizationType?: string;
    industryContext?: string;
  };
}

// 外部数据源
export interface ExternalDataSource {
  type: 'financial' | 'hr' | 'operational' | 'market' | 'custom';
  source: string;
  data: any;
  timestamp: string;
  reliability: number; // 0-1
}

// 评估选项
export interface TutorOptions {
  version: 'standard' | 'professional';
  analysisMode: 'basic' | 'comprehensive' | 'comparative';
  includeRecommendations: boolean;
  customFocus?: string[];
  externalData?: ExternalDataSource[];
  dataFusion?: DataFusionConfig;
  outputLanguage?: 'zh' | 'en';
}

// 评估结果
export interface AssessmentResult {
  organizationId: string;
  overallScore: number;
  dimensionScores: Record<string, {
    score: number;
    level: 'low' | 'medium' | 'high' | 'excellent';
    subdimensions: Record<string, number>;
  }>;
  insights: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    risks: string[];
  };
  recommendations: {
    immediate: string[];
    shortTerm: string[];
    longTerm: string[];
    priority: 'high' | 'medium' | 'low';
  }[];
  report: {
    executive: string;
    detailed: string;
    actionPlan: string;
  };
  metadata: {
    version: string;
    generatedAt: string;
    dataSourcesUsed: string[];
    confidenceLevel: number;
    analysisDepth: string;
  };
}

// 缓存条目
interface CacheEntry {
  data: AssessmentReport;
  timestamp: number;
}

interface AssessmentReport {
  id: string;
  organizationId: string;
  version: 'standard' | 'professional';
  overallScore: number;
  dimensionScores: Record<string, number>;
  strengths: string[];
  improvements: string[];
  recommendations: any[];
  nextSteps: string[];
  metadata: {
    generatedAt: Date;
    analysisMode: string;
    dataSourcesUsed: string[];
  };
}

export class OrganizationTutorAgent implements BaseAgent {
  public readonly name = 'organization_tutor';
  private llmClient: LLMApiClient;
  private promptBuilder: PromptBuilder;
  private dataFusionEngine: DataFusionEngine;
  private resultCache = new Map<string, CacheEntry>();
  private readonly cacheExpiry = 24 * 60 * 60 * 1000; // 24小时
  private isInitialized = false;

  constructor(
    llmClient: LLMApiClient,
    promptBuilder: PromptBuilder,
    dataFusionEngine: DataFusionEngine
  ) {
    this.llmClient = llmClient;
    this.promptBuilder = promptBuilder;
    this.dataFusionEngine = dataFusionEngine;
  }

  // 添加缺失的初始化方法
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('OrganizationTutorAgent 已经初始化');
      return;
    }

    try {
      // 验证必需的依赖项
      if (!this.llmClient) {
        throw new Error('LLMApiClient 是必需的依赖项');
      }
      if (!this.promptBuilder) {
        throw new Error('PromptBuilder 是必需的依赖项');
      }
      if (!this.dataFusionEngine) {
        throw new Error('DataFusionEngine 是必需的依赖项');
      }

      // 清理过期缓存
      this.cleanExpiredCache();
      
      this.isInitialized = true;
      logger.info('OrganizationTutorAgent 初始化完成');
    } catch (error) {
      logger.error('OrganizationTutorAgent 初始化失败', { error });
      throw error;
    }
  }

  /**
   * 实现BaseAgent接口的execute方法
   */
  public async execute(input: any): Promise<any> {
    return await this.generateAssessment(input.assessmentData, input.options);
  }

  /**
   * 实现BaseAgent接口的getStatus方法
   */
  public getStatus(): AgentStatus {
    return {
      name: this.name,
      initialized: this.isInitialized,
      lastActivity: new Date(),
      config: {
        cacheExpiry: this.cacheExpiry,
        cacheSize: this.resultCache.size
      }
    };
  }

  // 生成评估报告
  public async generateAssessment(
    assessmentData: AssessmentData,
    options: TutorOptions = {
      version: 'standard',
      analysisMode: 'basic',
      includeRecommendations: true,
      outputLanguage: 'zh'
    }
  ): Promise<AssessmentReport> {
    if (!this.isInitialized) {
      throw new Error('OrganizationTutorAgent 未初始化');
    }

    // 输入验证
    if (!assessmentData || !assessmentData.organizationId) {
      throw new Error('评估数据无效：缺少组织ID');
    }

    if (!assessmentData.responses || assessmentData.responses.length === 0) {
      throw new Error('评估数据无效：缺少响应数据');
    }

    const startTime = Date.now();
    
    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(assessmentData, options);
      const cached = this.resultCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        logger.info('使用缓存的评估结果', { cacheKey });
        return cached.data;
      }

      // 数据预处理
      const processedData = this.preprocessAssessmentData(assessmentData);
      
      // 数据融合（如果有外部数据）
      let fusedData: any = null;
      if (options.externalData && options.externalData.length > 0) {
        fusedData = await this.dataFusionEngine.fuseData(
          options.externalData.map(source => ({
            sourceId: source.source,
            content: JSON.stringify(source.data),
            contentType: source.type,
            metadata: {
              timestamp: source.timestamp,
              reliability: source.reliability
            }
          })),
          options.dataFusion
        );
      }

      // 构建提示词变量
      const promptVariables = {
        version: options.version,
        assessmentData: JSON.stringify(processedData),
        analysisMode: options.analysisMode || 'basic',
        includeRecommendations: options.includeRecommendations ? '是' : '否',
        customFocus: options.customFocus || '无',
        outputLanguage: options.outputLanguage || 'zh',
        organizationType: assessmentData.metadata.organizationType || '未指定',
        industryContext: assessmentData.metadata.industryContext || '通用行业'
      };

      const prompt = this.promptBuilder.buildPrompt('organization_tutor', promptVariables);

      // 调用LLM生成评估
      let llmResponse: LLMResponse;
      if (options.version === 'professional') {
        // 专业版：MiniMax 先评估 → DeepSeek 再总结
        llmResponse = await this.runProfessionalDualModelAnalysis(prompt, assessmentData);
      } else {
        // 标准版：仅使用 MiniMax M1
        const standardRequest: LLMRequest = {
          model: 'MiniMax-M1',
          messages: [
            { role: 'system', content: prompt.systemPrompt },
            { role: 'user', content: prompt.userPrompt }
          ],
          temperature: 0.3,
          maxTokens: 4000
        };
        llmResponse = await this.llmClient.chat('minimax', standardRequest);
      }

      // 解析响应
      const report = await this.parseAssessmentResponse(
        llmResponse.content || '',
        assessmentData,
        options
      );

      // 缓存结果
      this.resultCache.set(cacheKey, {
        data: report,
        timestamp: Date.now()
      });

      const processingTime = Date.now() - startTime;
      logger.info('评估报告生成完成', {
        organizationId: assessmentData.organizationId,
        version: options.version,
        processingTime
      });

      return report;
    } catch (error) {
      logger.error('评估报告生成失败', { 
        error: error instanceof Error ? error.message : String(error),
        organizationId: assessmentData.organizationId,
        options 
      });
      throw new Error(`评估报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 专业版双模型分析：MiniMax 先评估 → DeepSeek 再总结
   */
  private async runProfessionalDualModelAnalysis(prompt: any, assessmentData: any): Promise<LLMResponse> {
    logger.info('开始专业版双模型分析');

    // 第一步：使用 MiniMax M1 进行初步评估
    const minimaxRequest: LLMRequest = {
      model: 'MiniMax-M1',
      messages: [
        { role: 'system', content: prompt.systemPrompt },
        { role: 'user', content: prompt.userPrompt }
      ],
      temperature: 0.3,
      maxTokens: 6000
    };

    logger.info('第一步：MiniMax M1 初步评估');
    const minimaxResponse = await this.llmClient.chat('minimax', minimaxRequest);

    // 第二步：使用 DeepSeek 对 MiniMax 的结果进行深度总结和优化
    const deepseekPrompt = this.buildDeepSeekSummaryPrompt(minimaxResponse.content || '', assessmentData);
    const deepseekRequest: LLMRequest = {
      model: 'deepseek-reasoner',
      messages: [
        { role: 'system', content: deepseekPrompt.systemPrompt },
        { role: 'user', content: deepseekPrompt.userPrompt }
      ],
      temperature: 0.2,
      maxTokens: 8000
    };

    logger.info('第二步：DeepSeek 深度总结');
    const deepseekResponse = await this.llmClient.chat('deepseek', deepseekRequest);

    // 返回 DeepSeek 的最终结果
    return deepseekResponse;
  }

  /**
   * 构建 DeepSeek 总结提示词
   */
  private buildDeepSeekSummaryPrompt(minimaxResult: string, assessmentData: any) {
    const systemPrompt = `你是一位资深的组织发展专家和数据分析师，专门负责对OCTI评估报告进行深度总结和优化。

你的任务是：
1. 分析MiniMax模型生成的初步评估报告
2. 提取关键洞察和发现
3. 优化报告结构和表达
4. 增加深层次的分析和建议
5. 确保报告的专业性和实用性

请保持客观、专业的分析态度，提供具有实际指导价值的建议。`;

    const userPrompt = `请对以下MiniMax生成的OCTI评估报告进行深度总结和优化：

## MiniMax初步评估报告：
${minimaxResult}

## 评估数据概览：
- 组织类型：${assessmentData.metadata?.organizationType || '未指定'}
- 评估时间：${assessmentData.metadata?.createdAt || '未指定'}
- 题目数量：${assessmentData.questions?.length || 0}

## 请按以下要求优化报告：

1. **深度分析**：对四个维度的表现进行更深入的解读
2. **关联分析**：分析维度间的相互影响和潜在关联
3. **风险识别**：识别组织可能面临的潜在风险和挑战
4. **机遇发现**：基于优势分析，识别发展机遇
5. **行动建议**：提供具体、可操作的改进建议
6. **资源推荐**：推荐相关的学习资源和工具

请确保最终报告：
- 结构清晰，逻辑严密
- 语言专业但易懂
- 建议具体可行
- 符合专业版报告的高标准

请以JSON格式返回优化后的完整报告。`;

    return { systemPrompt, userPrompt };
  }

  // 添加缺失的辅助方法
  private generateCacheKey(data: AssessmentData, options: TutorOptions): string {
    const keyParts = [
      data.organizationId,
      options.version,
      options.analysisMode,
      JSON.stringify(options.customFocus || []),
      data.metadata.completedAt
    ];
    return keyParts.join('_').replace(/[^a-zA-Z0-9_]/g, '_');
  }

  private preprocessAssessmentData(data: AssessmentData): any {
    // 实现数据预处理逻辑
    return {
      organizationId: data.organizationId,
      responses: data.responses.map(r => ({
        questionId: r.questionId,
        answer: r.answer,
        dimension: r.dimension,
        subdimension: r.subdimension
      })),
      metadata: data.metadata
    };
  }

  private async parseAssessmentResponse(
    content: string,
    assessmentData: AssessmentData,
    options: TutorOptions
  ): Promise<AssessmentReport> {
    // 实现响应解析逻辑
    try {
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || 
                       content.match(/\{[\s\S]*\}/);
      
      let parsedContent: any;
      if (jsonMatch) {
        parsedContent = JSON.parse(jsonMatch[1] || jsonMatch[0]);
      } else {
        // 结构化解析
        parsedContent = this.parseStructuredReport(content);
      }

      return {
        id: `report_${Date.now()}`,
        organizationId: assessmentData.organizationId,
        version: options.version,
        overallScore: parsedContent.overallScore || 0,
        dimensionScores: parsedContent.dimensionScores || {},
        strengths: parsedContent.strengths || [],
        improvements: parsedContent.improvements || [],
        recommendations: parsedContent.recommendations || [],
        nextSteps: parsedContent.nextSteps || [],
        metadata: {
          generatedAt: new Date(),
          analysisMode: options.analysisMode,
          dataSourcesUsed: options.externalData?.map(d => d.source) || []
        }
      };
    } catch (error) {
      logger.error('评估响应解析失败', { error, content: content.substring(0, 500) });
      throw new Error('评估响应解析失败');
    }
  }

  private parseStructuredReport(content: string): any {
    // 实现结构化报告解析
    return {
      overallScore: 75,
      dimensionScores: {},
      strengths: [],
      improvements: [],
      recommendations: [],
      nextSteps: []
    };
  }

  // 添加缓存清理方法
  private cleanExpiredCache(): void {
    const now = Date.now();
    Array.from(this.resultCache.entries()).forEach(([key, entry]) => {
      if (now - entry.timestamp > this.cacheExpiry) {
        this.resultCache.delete(key);
      }
    });
  }

  // 获取缓存统计
  getCacheStats(): {
    size: number;
    hitRate: number;
    oldestEntry: string | null;
  } {
    const entries = Array.from(this.resultCache.values());
    const oldestEntry = entries.length > 0
      ? entries.reduce((oldest, entry) =>
          new Date(entry.timestamp) < new Date(oldest.timestamp) ? entry : oldest
        ).timestamp.toString()
      : null;
    
    return {
      size: this.resultCache.size,
      hitRate: 0, // 需要额外跟踪命中率
      oldestEntry
    };
  }

  // 清空缓存
  clearCache(): void {
    this.resultCache.clear();
    logger.info('Assessment cache cleared');
  }
}

export default OrganizationTutorAgent;
