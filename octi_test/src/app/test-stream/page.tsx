'use client'

import React, { useState, useEffect } from 'react'
import { StreamQuestionnaireRenderer } from '@/components/questionnaire/StreamQuestionnaireRenderer'

// 流式题目类型定义
interface StreamQuestion {
  id: string
  dimension: string
  type: string
  text: string
  options: Array<{
    id: string
    text: string
    score: number
  }>
  required: boolean
  order: number
}

export default function TestStreamPage() {
  const [questions, setQuestions] = useState<StreamQuestion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [progress, setProgress] = useState({ completed: 0, total: 60, percentage: 0 })
  const [questionnaireId, setQuestionnaireId] = useState<string | null>(null)

  /**
   * 开始流式加载测试
   */
  const startStreamTest = async () => {
    setIsLoading(true)
    setQuestions([])
    setProgress({ completed: 0, total: 60, percentage: 0 })

    try {
      console.log('开始流式加载测试')

      // 第一步：启动问卷生成，获取第一批题目
      const firstBatchResponse = await fetch('/api/questionnaire/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          organizationType: 'technology',
          version: 'standard',
          batchSize: 3
        })
      })

      if (!firstBatchResponse.ok) {
        throw new Error(`启动问卷生成失败: ${firstBatchResponse.status}`)
      }

      const firstBatch = await firstBatchResponse.json()
      
      if (!firstBatch.success) {
        throw new Error(firstBatch.error || '启动问卷生成失败')
      }

      console.log('收到第一批题目:', firstBatch.data)

      // 更新状态：显示第一批题目
      setQuestions(firstBatch.data.questions)
      setProgress(firstBatch.data.progress)
      setQuestionnaireId(firstBatch.data.questionnaireId)

      // 第二步：继续加载剩余题目
      await loadRemainingQuestions(firstBatch.data)

    } catch (err) {
      console.error('流式加载测试失败:', err)
      alert(`流式加载失败: ${err instanceof Error ? err.message : '未知错误'}`)
    }
  }

  /**
   * 加载剩余题目
   */
  const loadRemainingQuestions = async (initialBatch: any) => {
    let nextBatch = initialBatch.nextBatch
    let shouldContinue = true

    while (nextBatch && shouldContinue) {
      try {
        console.log(`加载下一批题目: ${nextBatch.dimension} 从 ${nextBatch.startIndex}`)
        
        const response = await fetch(
          `/api/questionnaire/batch?questionnaireId=${initialBatch.questionnaireId}&dimension=${nextBatch.dimension}&startIndex=${nextBatch.startIndex}&batchSize=3`
        )

        if (!response.ok) {
          console.warn(`批次加载失败: ${response.status}，跳过此批次`)
          break
        }

        const batchData = await response.json()
        
        if (!batchData.success) {
          console.warn('批次数据无效，跳过此批次')
          break
        }

        console.log(`收到批次数据:`, batchData.data)

        // 累积添加新题目
        setQuestions(prev => [...prev, ...batchData.data.questions])
        setProgress(batchData.data.progress)

        // 准备下一批
        nextBatch = batchData.data.nextBatch
        
        // 如果没有下一批，说明全部完成
        if (!nextBatch) {
          setIsLoading(false)
          shouldContinue = false
          console.log('所有题目加载完成!')
          break
        }

        // 短暂延迟，避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 2000))

      } catch (error) {
        console.error('加载批次失败:', error)
        shouldContinue = false
        break
      }
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">流式问卷加载测试</h1>
      
      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">测试控制</h2>
          <button
            onClick={startStreamTest}
            disabled={isLoading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg"
          >
            {isLoading ? '加载中...' : '开始流式加载测试'}
          </button>
        </div>
        
        {/* 状态显示 */}
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-600">已加载题目:</span>
            <span className="font-medium ml-2">{questions.length}</span>
          </div>
          <div>
            <span className="text-gray-600">总进度:</span>
            <span className="font-medium ml-2">{progress.percentage}%</span>
          </div>
          <div>
            <span className="text-gray-600">问卷ID:</span>
            <span className="font-medium ml-2 text-xs">{questionnaireId || '未生成'}</span>
          </div>
        </div>
      </div>
      
      {/* 流式问卷渲染器 */}
      {questions.length > 0 && (
        <StreamQuestionnaireRenderer
          questions={questions}
          isLoading={isLoading}
          progress={progress}
          onAnswerChange={(questionId, answer) => {
            console.log('答案变更:', questionId, answer)
          }}
          onComplete={(responses) => {
            console.log('问卷完成:', responses)
            alert(`问卷完成！共回答了 ${responses.length} 道题目`)
          }}
          className="bg-white"
        />
      )}
      
      {/* 调试信息 */}
      <div className="mt-8 bg-gray-100 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">调试信息</h3>
        <div className="text-sm space-y-1">
          <div>加载状态: {isLoading ? '加载中' : '已停止'}</div>
          <div>题目数量: {questions.length}</div>
          <div>进度: {progress.completed}/{progress.total} ({progress.percentage}%)</div>
        </div>
      </div>
    </div>
  )
}
