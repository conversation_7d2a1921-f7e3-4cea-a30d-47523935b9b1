'use client'

import React, { useState } from 'react'
import { QuestionnaireLoader } from '@/components/questionnaire/QuestionnaireLoader'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'

import { QuestionnaireConfig } from '@/types'

interface QuestionnairePageState {
  mode: 'selection' | 'questionnaire' | 'completed'
  selectedVersion: 'standard' | 'professional' | null
  questionnaireConfig: QuestionnaireConfig | null
  isLoading: boolean
  error: string | null
}

export default function QuestionnairePage() {
  const [state, setState] = useState<QuestionnairePageState>({
    mode: 'selection',
    selectedVersion: null,
    questionnaireConfig: null,
    isLoading: false,
    error: null
  })

  // 版本选择处理 - 简化为直接切换到问卷模式，让QuestionnaireLoader处理流式加载
  const handleVersionSelect = async (version: 'standard' | 'professional') => {
    setState(prev => ({
      ...prev,
      selectedVersion: version,
      mode: 'questionnaire',
      error: null,
      isLoading: false
    }))
  }

  // 问卷完成处理
  const handleQuestionnaireComplete = async (responses: any) => {
    console.log('问卷完成:', responses)
    setState(prev => ({ ...prev, mode: 'completed' }))
  }

  // 重新开始
  const handleRestart = () => {
    setState({
      mode: 'selection',
      selectedVersion: null,
      questionnaireConfig: null,
      isLoading: false,
      error: null
    })
  }

  // 渲染版本选择界面
  const renderVersionSelection = () => (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          OCTI 组织文化评估问卷
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          选择适合您组织的评估版本，开始深入了解您的组织文化特征
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* 标准版 */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-blue-500">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl">标准版</CardTitle>
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">推荐</span>
            </div>
            <CardDescription>
              适合大多数组织的基础评估，快速了解组织文化现状
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span className="text-sm">约 40 道题目</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm">预计 15-20 分钟</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span className="text-sm">适合中小型团队</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                <span className="text-sm">基础分析报告</span>
              </div>
            </div>
            <Button 
              className="w-full mt-6" 
              onClick={() => handleVersionSelect('standard')}
              disabled={state.isLoading}
            >
              {state.isLoading && state.selectedVersion === 'standard' ? (
                <>生成中...</>
              ) : (
                '选择标准版'
              )}
            </Button>
          </CardContent>
        </Card>

        {/* 专业版 */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-purple-500">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl">专业版</CardTitle>
              <span className="px-2 py-1 border border-gray-300 text-gray-700 text-xs rounded-full">深度分析</span>
            </div>
            <CardDescription>
              深度评估组织文化，提供详细的分析和改进建议
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span className="text-sm">约 60 道题目</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span className="text-sm">预计 25-35 分钟</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span className="text-sm">适合大型组织</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                <span className="text-sm">详细分析报告</span>
              </div>
            </div>
            <Button 
              className="w-full mt-6" 
              variant="outline"
              onClick={() => handleVersionSelect('professional')}
              disabled={state.isLoading}
            >
              {state.isLoading && state.selectedVersion === 'professional' ? (
                <>生成中...</>
              ) : (
                '选择专业版'
              )}
            </Button>
          </CardContent>
        </Card>
      </div>

      {state.error && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{state.error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={() => setState(prev => ({ ...prev, error: null }))}
          >
            重试
          </Button>
        </div>
      )}
    </div>
  )

  // 渲染问卷界面
  const renderQuestionnaire = () => {
    if (!state.questionnaireConfig) return null
    
    return (
      <div className="min-h-screen bg-gray-50">
        <QuestionnaireLoader 
          version={state.selectedVersion || 'standard'}
          organizationId="demo-org"
          onComplete={handleQuestionnaireComplete}
        />
      </div>
    )
  }

  // 渲染完成界面
  const renderCompleted = () => (
    <div className="container mx-auto px-4 py-8 max-w-2xl text-center">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="mb-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">问卷完成！</h2>
          <p className="text-gray-600">
            感谢您完成 OCTI 组织文化评估问卷。我们正在分析您的回答，稍后将为您生成详细的评估报告。
          </p>
        </div>
        
        <div className="space-y-4">
          <Button onClick={handleRestart} variant="outline" className="w-full">
            重新开始评估
          </Button>
          <Button className="w-full">
            查看评估报告
          </Button>
        </div>
      </div>
    </div>
  )

  // 主渲染逻辑
  switch (state.mode) {
    case 'selection':
      return renderVersionSelection()
    case 'questionnaire':
      return renderQuestionnaire()
    case 'completed':
      return renderCompleted()
    default:
      return renderVersionSelection()
  }
}
