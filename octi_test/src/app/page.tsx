'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Loading } from '@/components/ui/Loading'
import { Progress } from '@/components/ui/Progress'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/Dialog'
import { QuickForm, FormField } from '@/components/ui/Form'

/**
 * 系统状态接口
 */
interface SystemStatus {
  health: 'healthy' | 'warning' | 'error'
  services: {
    config: boolean
    agents: boolean
    api: boolean
  }
  metrics: {
    uptime: string
    memory: number
    cpu: number
  }
}

/**
 * 智能体状态接口
 */
interface AgentStatus {
  id: string
  name: string
  status: 'active' | 'idle' | 'error'
  lastActivity: string
}

/**
 * 评估数据接口
 */
interface Assessment {
  id: string
  title: string
  description: string
  status: 'draft' | 'active' | 'completed'
  createdAt: string
  completedCount: number
  totalQuestions: number
}

/**
 * OCTI系统主页面组件
 */
export default function HomePage() {
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null)
  const [agents, setAgents] = useState<AgentStatus[]>([])
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 加载系统数据
  useEffect(() => {
    loadSystemData()
  }, [])

  const loadSystemData = async () => {
    try {
      setLoading(true)
      setError(null)

      // 并行加载所有数据
      const [statusResponse, agentsResponse, assessmentsResponse] = await Promise.all([
        fetch('/api/system?action=status'),
        fetch('/api/agents'),
        fetch('/api/assessments')
      ])

      if (!statusResponse.ok || !agentsResponse.ok || !assessmentsResponse.ok) {
        throw new Error('加载系统数据失败')
      }

      const [statusData, agentsData, assessmentsData] = await Promise.all([
        statusResponse.json(),
        agentsResponse.json(),
        assessmentsResponse.json()
      ])

      setSystemStatus(statusData.data)
      setAgents(agentsData.data || [])
      setAssessments(assessmentsData.data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setLoading(false)
    }
  }

  // 创建新评估的表单字段
  const createAssessmentFields: FormField[] = [
    {
      name: 'title',
      label: '评估标题',
      type: 'text',
      placeholder: '请输入评估标题',
      rules: [
        { required: true, message: '评估标题不能为空' },
        { minLength: 2, message: '标题至少需要2个字符' },
        { maxLength: 100, message: '标题不能超过100个字符' }
      ]
    },
    {
      name: 'description',
      label: '评估描述',
      type: 'text',
      placeholder: '请输入评估描述',
      rules: [
        { maxLength: 500, message: '描述不能超过500个字符' }
      ]
    },
    {
      name: 'type',
      label: '评估类型',
      type: 'select',
      options: [
        { label: '能力评估', value: 'capability' },
        { label: '知识测试', value: 'knowledge' },
        { label: '技能验证', value: 'skill' },
        { label: '综合评估', value: 'comprehensive' }
      ],
      rules: [
        { required: true, message: '请选择评估类型' }
      ]
    }
  ]

  // 处理创建评估
  const handleCreateAssessment = async (data: any) => {
    try {
      const response = await fetch('/api/assessments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error('创建评估失败')
      }

      // 重新加载评估列表
      await loadSystemData()
    } catch (err) {
      console.error('创建评估失败:', err)
      throw err
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'active':
        return 'text-green-600'
      case 'warning':
      case 'idle':
        return 'text-yellow-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'healthy':
        return '健康'
      case 'warning':
        return '警告'
      case 'error':
        return '错误'
      case 'active':
        return '活跃'
      case 'idle':
        return '空闲'
      case 'draft':
        return '草稿'
      case 'completed':
        return '已完成'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="加载系统数据中..." />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">加载失败</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={loadSystemData} className="w-full">
              重新加载
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            OCTI 智能评估系统
          </h1>
          <p className="text-gray-600">
            基于AI的在线能力测试与智能评估平台
          </p>
          <div className="mt-4 flex space-x-4">
            <Button
              onClick={() => window.location.href = '/assessment'}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700"
            >
              开始OCTI评估
            </Button>
            <Button 
              onClick={() => window.location.href = '/report'}
              size="lg"
              variant="outline"
            >
              查看报告
            </Button>
          </div>
        </div>

        {/* 系统状态概览 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">系统状态</CardTitle>
            </CardHeader>
            <CardContent>
              {systemStatus && (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>整体健康度</span>
                    <span className={getStatusColor(systemStatus.health)}>
                      {getStatusText(systemStatus.health)}
                    </span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>配置服务</span>
                      <span className={systemStatus.services?.config ? 'text-green-600' : 'text-red-600'}>
                        {systemStatus.services?.config ? '正常' : '异常'}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>智能体服务</span>
                      <span className={systemStatus.services?.agents ? 'text-green-600' : 'text-red-600'}>
                        {systemStatus.services?.agents ? '正常' : '异常'}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>API服务</span>
                      <span className={systemStatus.services?.api ? 'text-green-600' : 'text-red-600'}>
                        {systemStatus.services?.api ? '正常' : '异常'}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">系统指标</CardTitle>
            </CardHeader>
            <CardContent>
              {systemStatus && (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>运行时间</span>
                    <span className="text-sm text-gray-600">
                      {systemStatus.metrics?.uptime}
                    </span>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>内存使用</span>
                      <span>{systemStatus.metrics?.memory}%</span>
                    </div>
                    <Progress value={systemStatus.metrics?.memory || 0} size="sm" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>CPU使用</span>
                      <span>{systemStatus.metrics?.cpu}%</span>
                    </div>
                    <Progress value={systemStatus.metrics?.cpu || 0} size="sm" />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">智能体状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {agents.map((agent, index) => (
                  <div key={`agent-${agent.id}-${index}`} className="flex justify-between items-center text-sm">
                    <span>{agent.name}</span>
                    <span className={getStatusColor(agent.status)}>
                      {getStatusText(agent.status)}
                    </span>
                  </div>
                ))}
                {agents.length === 0 && (
                  <p className="text-sm text-gray-500">暂无智能体</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 评估管理 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-xl">评估管理</CardTitle>
                <CardDescription>管理和创建在线评估</CardDescription>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button>创建评估</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>创建新评估</DialogTitle>
                  </DialogHeader>
                  <QuickForm
                    fields={createAssessmentFields}
                    onSubmit={handleCreateAssessment}
                    submitText="创建评估"
                  />
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            {assessments.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {assessments.map((assessment, index) => (
                  <Card key={`assessment-${assessment.id}-${index}`} variant="outlined">
                    <CardHeader>
                      <CardTitle className="text-lg">{assessment.title}</CardTitle>
                      <CardDescription>{assessment.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>状态</span>
                          <span className={getStatusColor(assessment.status)}>
                            {getStatusText(assessment.status)}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>完成数</span>
                          <span>{assessment.completedCount}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>题目数</span>
                          <span>{assessment.totalQuestions}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>创建时间</span>
                          <span>{new Date(assessment.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="mt-4 flex space-x-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          编辑
                        </Button>
                        <Button size="sm" className="flex-1">
                          查看
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">暂无评估项目</p>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline">创建第一个评估</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>创建新评估</DialogTitle>
                    </DialogHeader>
                    <QuickForm
                      fields={createAssessmentFields}
                      onSubmit={handleCreateAssessment}
                      submitText="创建评估"
                    />
                  </DialogContent>
                </Dialog>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}