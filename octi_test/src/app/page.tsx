'use client'

import React from 'react'
import { HeroSection } from '@/components/home/<USER>'
import { AssessmentCards } from '@/components/home/<USER>'
import { OCTIIntroduction } from '@/components/home/<USER>'
import { FeatureComparison } from '@/components/home/<USER>'
import { UserGuide } from '@/components/home/<USER>'

/**
 * OCTI智能评估系统 - 新版首页
 * 基于用户导向的产品展示页面设计
 */
export default function HomePage() {
  // 处理开始评估
  const handleStartAssessment = () => {
    window.location.href = '/assessment'
  }

  // 处理标准版评估
  const handleStartStandard = () => {
    window.location.href = '/assessment?version=standard'
  }

  // 处理专业版评估
  const handleStartProfessional = () => {
    window.location.href = '/assessment?version=professional'
  }

  // 处理了解更多
  const handleLearnMore = () => {
    // 滚动到OCTI介绍部分
    const introSection = document.getElementById('octi-introduction')
    if (introSection) {
      introSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // 处理联系支持
  const handleContactSupport = () => {
    // 这里可以打开客服聊天或跳转到联系页面
    window.open('mailto:<EMAIL>', '_blank')
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 品牌展示区域 */}
      <HeroSection
        onStartAssessment={handleStartAssessment}
        onLearnMore={handleLearnMore}
      />

      {/* 评估类型选择 */}
      <section className="py-16 px-6 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              选择适合您的评估版本
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              根据您的组织规模和需求，选择最适合的OCTI评估版本，获得精准的能力分析
            </p>
          </div>
          <AssessmentCards
            onStartStandard={handleStartStandard}
            onStartProfessional={handleStartProfessional}
          />
        </div>
      </section>

      {/* OCTI理论介绍 */}
      <section id="octi-introduction" className="py-16 px-6">
        <OCTIIntroduction onLearnMore={handleLearnMore} />
      </section>

      {/* 功能对比表 */}
      <section className="py-16 px-6 bg-gray-50">
        <FeatureComparison
          onStartStandard={handleStartStandard}
          onStartProfessional={handleStartProfessional}
        />
      </section>

      {/* 用户引导 */}
      <section className="py-16 px-6">
        <UserGuide
          onStartAssessment={handleStartAssessment}
          onContactSupport={handleContactSupport}
        />
      </section>
    </div>
  )
}