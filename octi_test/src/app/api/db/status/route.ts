import { NextResponse } from 'next/server'

/**
 * 数据库状态检查API
 */
export async function GET() {
  try {
    // 简化的数据库连接测试，不依赖Prisma
    const { Pool } = require('pg')
    
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/octi_db'
    })
    
    // 测试连接
    const client = await pool.connect()
    await client.query('SELECT 1')
    client.release()
    await pool.end()
    
    return NextResponse.json({
      success: true,
      status: 'connected',
      timestamp: new Date().toISOString(),
      database: 'PostgreSQL'
    })
  } catch (error) {
    console.error('数据库连接测试失败:', error)
    
    return NextResponse.json({
      success: false,
      status: 'disconnected',
      error: error instanceof Error ? error.message : 'Database connection failed',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
