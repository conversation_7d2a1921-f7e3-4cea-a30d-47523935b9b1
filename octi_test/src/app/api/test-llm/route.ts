import { NextRequest, NextResponse } from 'next/server'
import { LLMApiClient } from '@/services/llm/llm-api-client'

export async function GET(request: NextRequest) {
  try {
    const llmClient = new LLMApiClient()
    
    const testRequest = {
      model: 'abab6.5s-chat',
      messages: [
        { 
          role: 'system' as const, 
          content: '你是一个JSON生成器。请严格按照要求返回JSON格式的数据。' 
        },
        { 
          role: 'user' as const, 
          content: `请生成一个简单的问卷JSON，包含3道题目。格式如下：

\`\`\`json
{
  "id": "test_questionnaire",
  "title": "测试问卷",
  "questions": [
    {
      "id": "q1",
      "text": "问题1",
      "options": [
        {"id": "o1", "text": "选项1", "score": 1},
        {"id": "o2", "text": "选项2", "score": 2}
      ]
    }
  ]
}
\`\`\`

请确保返回完整的JSON格式。` 
        }
      ],
      temperature: 0.7,
      maxTokens: 2000
    }
    
    console.log('发送测试请求到MiniMax...')
    const response = await llmClient.chat('minimax', testRequest)
    
    console.log('收到响应，长度:', response.content?.length || 0)
    console.log('完整响应内容:', response.content)
    
    return NextResponse.json({
      success: true,
      data: {
        responseLength: response.content?.length || 0,
        content: response.content,
        fullResponse: response
      }
    })
    
  } catch (error) {
    console.error('测试LLM调用失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
