import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { LLMApiClient } from '@/services/llm/llm-api-client'
import { QuestionDesignerAgent } from '@/services/agents/QuestionDesignerAgent'
import { PromptBuilder } from '@/services/llm/prompt-builder'
import { DataFusionEngine } from '@/services/data/data-fusion-engine'

/**
 * 问卷生成请求验证Schema
 */
const GenerateQuestionnaireSchema = z.object({
  organizationType: z.string().min(1, '组织类型不能为空'),
  version: z.enum(['standard', 'professional'], {
    errorMap: () => ({ message: '版本类型必须是 standard 或 professional' })
  }).optional(),
  assessmentType: z.enum(['basic', 'professional'], {
    errorMap: () => ({ message: '评估类型必须是 basic 或 professional' })
  }).optional(),
  targetAudience: z.string().optional(),
  customRequirements: z.string().optional()
}).transform((data) => {
  // 版本映射逻辑
  if (data.version && !data.assessmentType) {
    data.assessmentType = data.version === 'standard' ? 'basic' : 'professional'
  }
  if (!data.assessmentType) {
    data.assessmentType = 'basic'
  }
  return data
})

/**
 * 问卷生成API - 使用真实LLM
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    console.log('收到问卷生成请求:', body)
    
    // 验证请求参数
    const validatedData = GenerateQuestionnaireSchema.parse(body)
    const { organizationType, assessmentType, customRequirements, targetAudience, version } = validatedData
    
    console.log(`开始生成问卷: ${organizationType} - ${assessmentType}`)
    
    // 初始化LLM客户端和问卷设计师
    const llmClient = new LLMApiClient()
    const promptBuilder = new PromptBuilder()
    const dataFusionEngine = new DataFusionEngine()
    const questionDesigner = new QuestionDesignerAgent(llmClient, promptBuilder, dataFusionEngine)
    
    // 构建设计选项
    const designOptions = {
      version: (version || (assessmentType === 'professional' ? 'professional' : 'standard')) as 'standard' | 'professional',
      organizationType,
      targetAudience: targetAudience || 'general',
      customRequirements: customRequirements || '',
      questionCount: 60, // OCTI标准题目数量
      dimensions: ['S/F', 'I/T', 'M/V', 'A/D'] // OCTI四个维度
    }
    
    // 调用LLM生成问卷
    const questionnaire = await questionDesigner.design(designOptions)
    
    console.log(`问卷生成成功: ${questionnaire.questions.length} 道题目`)
    
    return NextResponse.json({
      success: true,
      data: questionnaire,
      message: '问卷生成成功',
      timestamp: new Date().toISOString(),
      metadata: {
        generationTime: questionnaire.metadata?.generationTime || 'unknown',
        model: questionnaire.metadata?.model || 'llm-generated',
        version: questionnaire.version,
        targetAudience: targetAudience || 'general',
        llmProvider: questionnaire.metadata?.provider || 'unknown'
      }
    })
    
  } catch (error) {
    console.error('问卷生成失败:', error)
    
    // Zod验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'validation_error',
          message: '请求参数验证失败',
          details: error.errors
        }
      }, { status: 400 })
    }
    
    // LLM API错误
    if (error instanceof Error && error.message.includes('API')) {
      return NextResponse.json({
        success: false,
        error: {
          type: 'llm_api_error',
          message: 'LLM服务调用失败，请稍后重试',
          details: error.message
        }
      }, { status: 503 })
    }
    
    // 其他错误
    return NextResponse.json({
      success: false,
      error: {
        type: 'generation_error',
        message: error instanceof Error ? error.message : '问卷生成失败'
      }
    }, { status: 500 })
  }
}

/**
 * 获取问卷生成状态和能力
 */
export async function GET() {
  try {
    // 检查LLM服务健康状态
    const llmClient = new LLMApiClient()
    const healthStatus = await llmClient.healthCheck()
    
    return NextResponse.json({
      success: true,
      status: 'ready',
      llmStatus: healthStatus,
      supportedTypes: {
        organizationTypes: ['tech', 'finance', 'healthcare', 'education', 'manufacturing', 'retail', 'consulting'],
        assessmentTypes: ['basic', 'professional'],
        versions: ['standard', 'professional']
      },
      capabilities: {
        maxQuestions: 60,
        dimensions: ['S/F', 'I/T', 'M/V', 'A/D'],
        questionTypes: ['single_choice', 'multiple_choice', 'scale', 'scenario'],
        llmProviders: ['minimax', 'deepseek']
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('获取问卷生成状态失败:', error)
    
    return NextResponse.json({
      success: false,
      status: 'error',
      error: {
        type: 'service_error',
        message: '服务状态检查失败'
      }
    }, { status: 500 })
  }
}
