import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { LLMApiClient } from '@/services/llm/llm-api-client'
import { QuestionDesignerAgent } from '@/services/agents/QuestionDesignerAgent'
import { PromptBuilder } from '@/services/llm/prompt-builder'
import { DataFusionEngine } from '@/services/data/data-fusion-engine'

/**
 * 分批问卷生成请求验证Schema
 */
const BatchQuestionnaireSchema = z.object({
  organizationType: z.string().min(1, '组织类型不能为空'),
  version: z.enum(['standard', 'professional'], {
    errorMap: () => ({ message: '版本类型必须是 standard 或 professional' })
  }).optional(),
  batchSize: z.number().min(1).max(15).optional().default(5),
  dimension: z.enum(['S/F', 'I/T', 'M/V', 'A/D']).optional(),
  startIndex: z.number().min(1).optional().default(1)
})

/**
 * POST /api/questionnaire/batch
 * 分批生成问卷题目
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('收到分批问卷生成请求:', body)
    
    const validatedData = BatchQuestionnaireSchema.parse(body)
    const { organizationType, version = 'standard', batchSize, dimension, startIndex } = validatedData
    
    // 初始化服务
    const llmClient = new LLMApiClient()
    const promptBuilder = new PromptBuilder()
    const dataFusionEngine = new DataFusionEngine()
    const questionDesigner = new QuestionDesignerAgent(llmClient, promptBuilder, dataFusionEngine)
    
    if (dimension) {
      // 生成特定维度的题目批次
      console.log(`生成维度 ${dimension} 的 ${batchSize} 道题目`)
      
      const questions = await questionDesigner.generateQuestionBatch(
        dimension,
        batchSize,
        startIndex,
        {
          version,
          organizationType,
          targetAudience: 'general',
          customRequirements: '',
          questionCount: batchSize,
          dimensions: [dimension]
        }
      )
      
      return NextResponse.json({
        success: true,
        data: {
          dimension,
          questions,
          batchSize: questions.length,
          startIndex,
          nextIndex: startIndex + questions.length
        }
      })
    } else {
      // 生成完整问卷的第一批题目
      console.log(`开始生成完整问卷的第一批题目`)
      
      const designOptions = {
        version,
        organizationType,
        targetAudience: 'general',
        customRequirements: '',
        questionCount: 60,
        dimensions: ['S/F', 'I/T', 'M/V', 'A/D']
      }
      
      // 生成第一个维度的第一批题目
      const firstBatch = await questionDesigner.generateQuestionBatch(
        'S/F',
        batchSize,
        1,
        designOptions
      )
      
      return NextResponse.json({
        success: true,
        data: {
          questionnaireId: `questionnaire_${Date.now()}`,
          dimension: 'S/F',
          questions: firstBatch,
          batchSize: firstBatch.length,
          totalExpected: 60,
          progress: {
            completed: firstBatch.length,
            total: 60,
            percentage: Math.round((firstBatch.length / 60) * 100)
          },
          nextBatch: {
            dimension: 'S/F',
            startIndex: 1 + firstBatch.length
          }
        }
      })
    }
    
  } catch (error) {
    console.error('分批问卷生成失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数验证失败',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}

/**
 * GET /api/questionnaire/batch?questionnaireId=xxx&dimension=xxx&startIndex=xxx
 * 获取问卷的下一批题目
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const questionnaireId = searchParams.get('questionnaireId')
    const dimension = searchParams.get('dimension')
    const startIndex = parseInt(searchParams.get('startIndex') || '1')
    const batchSize = parseInt(searchParams.get('batchSize') || '5')
    
    if (!questionnaireId || !dimension) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数: questionnaireId 和 dimension'
      }, { status: 400 })
    }
    
    console.log(`获取问卷 ${questionnaireId} 维度 ${dimension} 从 ${startIndex} 开始的 ${batchSize} 道题目`)
    
    // 这里可以从缓存中获取已生成的题目，或者生成新的题目
    // 为了演示，我们生成新的题目
    const llmClient = new LLMApiClient()
    const promptBuilder = new PromptBuilder()
    const dataFusionEngine = new DataFusionEngine()
    const questionDesigner = new QuestionDesignerAgent(llmClient, promptBuilder, dataFusionEngine)
    
    const questions = await questionDesigner.generateQuestionBatch(
      dimension,
      batchSize,
      startIndex,
      {
        version: 'standard',
        organizationType: 'technology',
        targetAudience: 'general',
        customRequirements: '',
        questionCount: batchSize,
        dimensions: [dimension]
      }
    )
    
    // 计算下一批的信息
    const dimensionOrder = ['S/F', 'I/T', 'M/V', 'A/D']
    const currentDimensionIndex = dimensionOrder.indexOf(dimension)
    const questionsPerDimension = 15
    const nextIndexInDimension = startIndex + batchSize
    
    let nextBatch = null
    if (nextIndexInDimension <= questionsPerDimension) {
      // 当前维度还有题目
      nextBatch = {
        dimension,
        startIndex: nextIndexInDimension
      }
    } else if (currentDimensionIndex < dimensionOrder.length - 1) {
      // 切换到下一个维度
      nextBatch = {
        dimension: dimensionOrder[currentDimensionIndex + 1],
        startIndex: 1
      }
    }
    
    return NextResponse.json({
      success: true,
      data: {
        questionnaireId,
        dimension,
        questions,
        batchSize: questions.length,
        startIndex,
        nextBatch,
        progress: {
          completed: (currentDimensionIndex * questionsPerDimension) + Math.min(startIndex + batchSize - 1, questionsPerDimension),
          total: 60,
          percentage: Math.round((((currentDimensionIndex * questionsPerDimension) + Math.min(startIndex + batchSize - 1, questionsPerDimension)) / 60) * 100)
        }
      }
    })
    
  } catch (error) {
    console.error('获取分批题目失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
