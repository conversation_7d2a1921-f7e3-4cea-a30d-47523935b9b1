
import { NextResponse } from 'next/server'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

/**
 * 问卷设计配置API
 */
export async function GET() {
  try {
    // 修复：使用正确的目录名 configs（带s）
    const configPath = join(process.cwd(), 'configs', 'question_design_prompt.json')
    
    console.log('尝试读取配置文件:', configPath)
    
    // 检查配置文件是否存在
    if (!existsSync(configPath)) {
      console.log('配置文件不存在，使用默认配置')
      
      // 返回默认配置
      const defaultConfig = {
        version: "1.0.0",
        name: "问卷设计配置",
        description: "OCTI智能评估系统问卷设计提示词配置",
        prompt_template: {
          system_prompt: "你是OCTI智能评估系统的问卷设计专家...",
          user_prompt_template: "请为{organizationType}类型的组织设计一份{assessmentType}评估问卷...",
          constraints: {
            question_count: 60,
            dimensions: ["S/F", "I/T", "M/V", "A/D"],
            question_types: ["single_choice", "multiple_choice", "scale"]
          }
        },
        generation_config: {
          temperature: 0.7,
          max_tokens: 4000,
          top_p: 0.9
        }
      }
      
      return NextResponse.json({
        success: true,
        data: defaultConfig,
        source: 'default',
        timestamp: new Date().toISOString()
      })
    }
    
    // 读取配置文件
    const configContent = readFileSync(configPath, 'utf-8')
    const config = JSON.parse(configContent)
    
    console.log('配置文件读取成功')
    
    return NextResponse.json({
      success: true,
      data: config,
      source: 'file',
      path: configPath,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('配置加载失败:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Config loading failed',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

