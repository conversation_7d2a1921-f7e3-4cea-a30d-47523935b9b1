import { NextRequest, NextResponse } from 'next/server'
import { LLMApiClient } from '@/services/llm/llm-api-client'

export async function GET(request: NextRequest) {
  try {
    const llmClient = new LLMApiClient()
    
    const systemPrompt = `你是OCTI问卷设计专家，专门为S/F维度生成高质量的评估题目。

S/F维度说明：结构化与灵活性 - 评估组织在规范化管理与灵活应变之间的平衡

请生成3道针对technology类型组织的专业题目。`

    const userPrompt = `请为S/F维度生成3道题目（编号从1开始）。

要求：
- 每道题5个选项，分值1-5分
- 题目要具体、实用、针对technology
- 使用通用性问题

请严格按照以下JSON格式返回：

\`\`\`json
{
  "questions": [
    {
      "id": "q_1",
      "dimension": "S/F",
      "type": "single_choice",
      "text": "具体的问题文本",
      "options": [
        {"id": "o_1_1", "text": "完全不符合", "score": 1},
        {"id": "o_1_2", "text": "基本不符合", "score": 2},
        {"id": "o_1_3", "text": "部分符合", "score": 3},
        {"id": "o_1_4", "text": "基本符合", "score": 4},
        {"id": "o_1_5", "text": "完全符合", "score": 5}
      ],
      "required": true,
      "order": 1
    }
  ]
}
\`\`\`

确保返回完整的JSON格式。`
    
    const testRequest = {
      model: 'abab6.5s-chat',
      messages: [
        { role: 'system' as const, content: systemPrompt },
        { role: 'user' as const, content: userPrompt }
      ],
      temperature: 0.7,
      maxTokens: 3000
    }
    
    console.log('发送批次测试请求到MiniMax...')
    const response = await llmClient.chat('minimax', testRequest)
    
    console.log('收到批次响应，长度:', response.content?.length || 0)
    console.log('完整响应内容:', response.content)
    
    // 尝试解析JSON
    let parsedResult = null
    let parseError = null
    
    try {
      const content = response.content || ''
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0]
        parsedResult = JSON.parse(jsonStr.trim())
      }
    } catch (error) {
      parseError = error instanceof Error ? error.message : '解析错误'
    }
    
    return NextResponse.json({
      success: true,
      data: {
        responseLength: response.content?.length || 0,
        content: response.content,
        parsedResult,
        parseError,
        fullResponse: response
      }
    })
    
  } catch (error) {
    console.error('测试批次LLM调用失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
