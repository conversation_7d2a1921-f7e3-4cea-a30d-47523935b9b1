'use client'

import { useState, useEffect } from 'react'

interface Question {
  id: string
  dimension: string
  type: string
  text: string
  options: Array<{
    id: string
    text: string
    score: number
  }>
  required: boolean
  order: number
}

interface BatchResponse {
  questionnaireId: string
  dimension: string
  questions: Question[]
  batchSize: number
  totalExpected: number
  progress: {
    completed: number
    total: number
    percentage: number
  }
  nextBatch?: {
    dimension: string
    startIndex: number
  }
}

export default function QuestionnaireBatchPage() {
  const [questions, setQuestions] = useState<Question[]>([])
  const [loading, setLoading] = useState(false)
  const [progress, setProgress] = useState({ completed: 0, total: 60, percentage: 0 })
  const [currentBatch, setCurrentBatch] = useState<BatchResponse | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 开始生成问卷
  const startQuestionnaire = async () => {
    setLoading(true)
    setError(null)
    setQuestions([])
    
    try {
      const response = await fetch('/api/questionnaire/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationType: 'technology',
          version: 'standard',
          batchSize: 3
        })
      })
      
      const result = await response.json()
      
      if (result.success) {
        const batchData = result.data as BatchResponse
        setQuestions(batchData.questions)
        setProgress(batchData.progress)
        setCurrentBatch(batchData)
        console.log('第一批题目加载完成:', batchData)
      } else {
        setError(result.error || '生成失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络错误')
    } finally {
      setLoading(false)
    }
  }

  // 加载下一批题目
  const loadNextBatch = async () => {
    if (!currentBatch?.nextBatch) return
    
    setLoading(true)
    
    try {
      const { dimension, startIndex } = currentBatch.nextBatch
      const url = `/api/questionnaire/batch?questionnaireId=${currentBatch.questionnaireId}&dimension=${dimension}&startIndex=${startIndex}&batchSize=3`
      
      const response = await fetch(url)
      const result = await response.json()
      
      if (result.success) {
        const batchData = result.data as BatchResponse
        setQuestions(prev => [...prev, ...batchData.questions])
        setProgress(batchData.progress)
        setCurrentBatch(batchData)
        console.log(`加载维度 ${dimension} 的题目完成:`, batchData)
      } else {
        setError(result.error || '加载失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络错误')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">OCTI问卷分批加载演示</h1>
      
      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">问卷生成控制</h2>
          <button
            onClick={startQuestionnaire}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg"
          >
            {loading ? '生成中...' : '开始生成问卷'}
          </button>
        </div>
        
        {/* 进度条 */}
        {progress.total > 0 && (
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>进度: {progress.completed}/{progress.total} 题</span>
              <span>{progress.percentage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress.percentage}%` }}
              ></div>
            </div>
          </div>
        )}
        
        {/* 加载下一批按钮 */}
        {currentBatch?.nextBatch && (
          <button
            onClick={loadNextBatch}
            disabled={loading}
            className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg"
          >
            {loading ? '加载中...' : `加载下一批 (${currentBatch.nextBatch.dimension})`}
          </button>
        )}
        
        {error && (
          <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            错误: {error}
          </div>
        )}
      </div>
      
      {/* 题目列表 */}
      {questions.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-6">已生成的题目 ({questions.length})</h2>
          
          <div className="space-y-6">
            {questions.map((question, index) => (
              <div key={question.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-medium">
                    题目 {index + 1} ({question.dimension})
                  </h3>
                  <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {question.dimension}
                  </span>
                </div>
                
                <p className="text-gray-700 mb-4">{question.text}</p>
                
                <div className="space-y-2">
                  {question.options.map((option) => (
                    <label key={option.id} className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name={question.id}
                        value={option.score}
                        className="w-4 h-4 text-blue-600"
                      />
                      <span className="text-gray-700">{option.text}</span>
                      <span className="text-sm text-gray-500">({option.score}分)</span>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
          
          {questions.length < 60 && (
            <div className="mt-6 text-center text-gray-500">
              还有 {60 - questions.length} 道题目待生成...
            </div>
          )}
        </div>
      )}
    </div>
  )
}
