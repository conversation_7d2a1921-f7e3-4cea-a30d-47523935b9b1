'use client'

import React, { useState } from 'react'
import { QuestionnaireLoader } from '@/components/questionnaire/QuestionnaireLoader'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'

import { QuestionnaireConfig } from '@/types'

interface AssessmentPageState {
  mode: 'selection' | 'questionnaire' | 'completed'
  selectedVersion: 'standard' | 'professional' | null
  questionnaireConfig: QuestionnaireConfig | null
  isLoading: boolean
  error: string | null
}

export default function AssessmentPage() {
  const [state, setState] = useState<AssessmentPageState>({
    mode: 'selection',
    selectedVersion: null,
    questionnaireConfig: null,
    isLoading: false,
    error: null
  })

  // 版本选择处理 - 简化为直接切换到问卷模式，让QuestionnaireLoader处理流式加载
  const handleVersionSelect = async (version: 'standard' | 'professional') => {
    setState(prev => ({
      ...prev,
      selectedVersion: version,
      mode: 'questionnaire',
      error: null,
      isLoading: false
    }))
  }

  // 问卷完成处理
  const handleQuestionnaireComplete = async (responses: any) => {
    console.log('问卷完成:', responses)
    setState(prev => ({ ...prev, mode: 'completed' }))
  }

  // 重新开始
  const handleRestart = () => {
    setState({
      mode: 'selection',
      selectedVersion: null,
      questionnaireConfig: null,
      isLoading: false,
      error: null
    })
  }

  // 渲染版本选择界面
  const renderVersionSelection = () => (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            开始OCTI评估
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            基于四维八极理论的组织能力智能诊断
          </p>
          <p className="text-lg text-gray-500">
            选择适合您组织的评估版本，开始深入了解您的组织能力特征
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* 标准版 */}
          <Card className="cursor-pointer hover:shadow-xl transition-all duration-300 border-2 hover:border-blue-500 hover:scale-105">
            <CardHeader className="text-center pb-4">
              <div className="flex items-center justify-between mb-2">
                <CardTitle className="text-2xl font-bold text-blue-600">标准版</CardTitle>
                <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">推荐</span>
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">¥99</div>
              <CardDescription className="text-base">
                基础组织能力诊断，适合初次评估的组织
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">60道专业评估题目</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">预计15-20分钟完成</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm">四维能力分析</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-sm">16种基础类型识别</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-sm">能力雷达图</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                  <span className="text-sm">PDF报告下载</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                  <span className="text-sm">6个月有效期</span>
                </div>
              </div>
              <Button 
                className="w-full py-3 text-lg font-semibold" 
                onClick={() => handleVersionSelect('standard')}
                disabled={state.isLoading}
              >
                {state.isLoading && state.selectedVersion === 'standard' ? (
                  <>生成中...</>
                ) : (
                  '选择标准版'
                )}
              </Button>
            </CardContent>
          </Card>

          {/* 专业版 */}
          <Card className="cursor-pointer hover:shadow-xl transition-all duration-300 border-2 hover:border-purple-500 hover:scale-105 relative">
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span className="px-4 py-1 bg-purple-600 text-white text-sm rounded-full font-medium">推荐</span>
            </div>
            <CardHeader className="text-center pb-4 pt-6">
              <div className="flex items-center justify-between mb-2">
                <CardTitle className="text-2xl font-bold text-purple-600">专业版</CardTitle>
                <span className="px-3 py-1 border border-purple-300 text-purple-700 text-sm rounded-full font-medium">深度分析</span>
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">¥399</div>
              <CardDescription className="text-base">
                深度组织能力诊断，提供详细分析和专家指导
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">60题+定制题目</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm">预计25-35分钟完成</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm">16种+细分子类型</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="text-sm">多源数据融合</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-sm">详细行动计划</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                  <span className="text-sm">1小时专家咨询</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                  <span className="text-sm">12个月有效期+2次免费复测</span>
                </div>
              </div>
              <Button 
                className="w-full py-3 text-lg font-semibold bg-purple-600 hover:bg-purple-700" 
                onClick={() => handleVersionSelect('professional')}
                disabled={state.isLoading}
              >
                {state.isLoading && state.selectedVersion === 'professional' ? (
                  <>生成中...</>
                ) : (
                  '选择专业版'
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 适用场景说明 */}
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mt-12">
          <div className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">标准版适合</h3>
            <ul className="space-y-2 text-blue-800">
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                <span className="text-sm">初次进行组织能力评估的企业</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                <span className="text-sm">50人以下的小型组织</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                <span className="text-sm">希望快速了解组织现状的团队</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                <span className="text-sm">预算有限但需要专业评估的组织</span>
              </li>
            </ul>
          </div>

          <div className="bg-purple-50 rounded-lg p-6 border-l-4 border-purple-500">
            <h3 className="text-lg font-semibold text-purple-900 mb-3">专业版适合</h3>
            <ul className="space-y-2 text-purple-800">
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                <span className="text-sm">50人以上的中大型组织</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                <span className="text-sm">需要制定详细改进计划的企业</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                <span className="text-sm">希望获得专家指导的组织</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                <span className="text-sm">需要持续跟踪改进效果的团队</span>
              </li>
            </ul>
          </div>
        </div>

        {state.error && (
          <div className="mt-8 max-w-2xl mx-auto">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600">{state.error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-2"
                onClick={() => setState(prev => ({ ...prev, error: null }))}
              >
                重试
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )

  // 渲染问卷界面
  const renderQuestionnaire = () => {
    return (
      <div className="min-h-screen bg-gray-50">
        <QuestionnaireLoader 
          version={state.selectedVersion || 'standard'}
          organizationId="零活实验室（环保公益组织）"
          onComplete={handleQuestionnaireComplete}
        />
      </div>
    )
  }

  // 渲染完成界面
  const renderCompleted = () => (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-white flex items-center justify-center">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="bg-white rounded-xl shadow-xl p-8 text-center">
          <div className="mb-8">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">评估完成！</h2>
            <p className="text-lg text-gray-600 mb-6">
              感谢您完成OCTI组织能力评估。我们正在基于四维八极理论分析您的回答，稍后将为您生成详细的评估报告。
            </p>
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <p className="text-blue-800 text-sm">
                <strong>预计分析时间：</strong>3-5分钟<br/>
                <strong>报告内容：</strong>四维能力分析、组织类型识别、发展建议
              </p>
            </div>
          </div>
          
          <div className="space-y-4">
            <Button className="w-full py-3 text-lg">
              查看评估报告
            </Button>
            <Button onClick={handleRestart} variant="outline" className="w-full py-3">
              重新开始评估
            </Button>
          </div>
        </div>
      </div>
    </div>
  )

  // 主渲染逻辑
  switch (state.mode) {
    case 'selection':
      return renderVersionSelection()
    case 'questionnaire':
      return renderQuestionnaire()
    case 'completed':
      return renderCompleted()
    default:
      return renderVersionSelection()
  }
}
