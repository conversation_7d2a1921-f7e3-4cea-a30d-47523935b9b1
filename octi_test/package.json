{"name": "octi-assessment-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@prisma/client": "^5.7.0", "@types/bcryptjs": "^3.0.0", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "eslint": "^8", "eslint-config-next": "14.0.4", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "next": "14.0.4", "pg": "^8.16.3", "postcss": "^8", "prisma": "^5.7.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.0", "redis": "^4.6.0", "tailwindcss": "^3.3.0", "typescript": "^5", "winston": "^3.11.0", "zod": "^3.25.76", "zustand": "^4.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@types/jest": "^29.5.0", "@types/pg": "^8.15.4", "jest": "^29.5.0", "prettier": "^3.1.0"}}