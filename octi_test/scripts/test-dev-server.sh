#!/bin/bash
# 开发服务器测试脚本

echo "🧪 测试开发服务器功能..."

BASE_URL="http://localhost:3000"

# 等待服务器启动
echo "⏳ 等待服务器启动..."
for i in {1..30}; do
  if curl -s "$BASE_URL" > /dev/null; then
    echo "✅ 服务器已启动"
    break
  fi
  sleep 2
  echo "   等待中... ($i/30)"
done

# 1. 测试健康检查端点
echo "📋 测试健康检查..."
HEALTH_RESPONSE=$(curl -s "$BASE_URL/api/health" || echo "ERROR")
if [[ "$HEALTH_RESPONSE" == *"success"* ]]; then
  echo "✅ 健康检查通过"
else
  echo "❌ 健康检查失败: $HEALTH_RESPONSE"
fi

# 2. 测试主页
echo "📋 测试主页..."
HOME_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/")
if [ "$HOME_RESPONSE" = "200" ]; then
  echo "✅ 主页访问正常"
else
  echo "❌ 主页访问失败: HTTP $HOME_RESPONSE"
fi

# 3. 测试API端点
echo "📋 测试API端点..."

# 测试问卷生成API
QUESTIONNAIRE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/questionnaire/generate" \
  -H "Content-Type: application/json" \
  -d '{"organizationType": "tech", "assessmentType": "basic"}' \
  -w "%{http_code}" -o /tmp/questionnaire_response.json)

if [ "$QUESTIONNAIRE_RESPONSE" = "200" ] || [ "$QUESTIONNAIRE_RESPONSE" = "201" ]; then
  echo "✅ 问卷生成API响应正常"
  echo "   响应内容: $(head -c 100 /tmp/questionnaire_response.json)..."
else
  echo "❌ 问卷生成API失败: HTTP $QUESTIONNAIRE_RESPONSE"
  echo "   错误内容: $(cat /tmp/questionnaire_response.json 2>/dev/null || echo '无响应内容')"
fi

# 4. 测试配置加载
echo "📋 测试配置加载..."
CONFIG_RESPONSE=$(curl -s "$BASE_URL/api/config/question-design" -w "%{http_code}" -o /tmp/config_response.json)
if [ "$CONFIG_RESPONSE" = "200" ]; then
  echo "✅ 配置加载正常"
else
  echo "❌ 配置加载失败: HTTP $CONFIG_RESPONSE"
fi

# 5. 测试数据库连接
echo "📋 测试数据库连接..."
DB_RESPONSE=$(curl -s "$BASE_URL/api/db/status" -w "%{http_code}" -o /tmp/db_response.json)
if [ "$DB_RESPONSE" = "200" ]; then
  echo "✅ 数据库连接正常"
else
  echo "❌ 数据库连接失败: HTTP $DB_RESPONSE"
fi

# 清理临时文件
rm -f /tmp/*_response.json

echo "🎯 测试完成!"
echo ""
echo "📊 测试结果总结:"
echo "- 服务器启动: ✅"
echo "- 健康检查: $([ "$HEALTH_RESPONSE" != "ERROR" ] && echo "✅" || echo "❌")"
echo "- 主页访问: $([ "$HOME_RESPONSE" = "200" ] && echo "✅" || echo "❌")"
echo "- API功能: $([ "$QUESTIONNAIRE_RESPONSE" = "200" ] || [ "$QUESTIONNAIRE_RESPONSE" = "201" ] && echo "✅" || echo "❌")"
echo "- 配置系统: $([ "$CONFIG_RESPONSE" = "200" ] && echo "✅" || echo "❌")"
echo "- 数据库: $([ "$DB_RESPONSE" = "200" ] && echo "✅" || echo "❌")"