#!/bin/bash
# 开发服务器启动脚本 - 增强版（包含端口清理）

echo "🚀 启动 OCTI 智能评估系统开发服务器..."

# 端口清理函数
cleanup_ports() {
    echo "🧹 清理端口占用..."
    
    # 定义需要清理的端口范围
    local ports=(3000 3001 3002 3003 3004 3005 3006 3007 3008 3009 3010)
    local killed_processes=()
    
    for port in "${ports[@]}"; do
        echo "🔍 检查端口 $port..."
        
        # 查找占用端口的进程
        local pids=$(lsof -ti :$port 2>/dev/null)
        
        if [ -n "$pids" ]; then
            echo "⚠️  端口 $port 被占用，进程ID: $pids"
            
            # 获取进程信息
            for pid in $pids; do
                local process_info=$(ps -p $pid -o pid,ppid,comm,args --no-headers 2>/dev/null)
                if [ -n "$process_info" ]; then
                    echo "   进程信息: $process_info"
                    
                    # 友好地终止进程
                    echo "   🔄 尝试友好终止进程 $pid..."
                    kill -TERM $pid 2>/dev/null
                    
                    # 等待进程终止
                    local count=0
                    while kill -0 $pid 2>/dev/null && [ $count -lt 5 ]; do
                        sleep 1
                        ((count++))
                        echo "     等待进程终止... ($count/5)"
                    done
                    
                    # 如果进程仍然存在，强制终止
                    if kill -0 $pid 2>/dev/null; then
                        echo "   💀 强制终止进程 $pid..."
                        kill -KILL $pid 2>/dev/null
                        sleep 1
                    fi
                    
                    # 验证进程是否已终止
                    if ! kill -0 $pid 2>/dev/null; then
                        echo "   ✅ 进程 $pid 已终止"
                        killed_processes+=("$port:$pid")
                    else
                        echo "   ❌ 进程 $pid 终止失败"
                    fi
                fi
            done
        else
            echo "   ✅ 端口 $port 空闲"
        fi
    done
    
    # 显示清理结果
    if [ ${#killed_processes[@]} -gt 0 ]; then
        echo "🎯 端口清理完成，已终止进程:"
        for process in "${killed_processes[@]}"; do
            echo "   - $process"
        done
    else
        echo "✅ 所有端口都是空闲的"
    fi
    
    echo ""
}

# 验证端口清理结果
verify_ports_clean() {
    echo "🔍 验证端口清理结果..."
    
    local ports=(3000 3001 3002 3003 3004 3005 3006 3007 3008 3009 3010)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -ti :$port >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo "⚠️  以下端口仍被占用: ${occupied_ports[*]}"
        echo "   您可能需要手动处理这些进程"
        
        # 显示详细信息
        for port in "${occupied_ports[@]}"; do
            echo "   端口 $port 占用情况:"
            lsof -i :$port 2>/dev/null | head -5
        done
        
        # 询问是否继续
        read -p "是否继续启动开发服务器? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ 启动已取消"
            exit 1
        fi
    else
        echo "✅ 所有目标端口已清理完成"
    fi
    
    echo ""
}

# 智能端口选择
select_available_port() {
    local start_port=3000
    local max_port=3010
    
    echo "🔍 寻找可用端口..."
    
    for ((port=start_port; port<=max_port; port++)); do
        if ! lsof -ti :$port >/dev/null 2>&1; then
            echo "✅ 找到可用端口: $port"
            export PORT=$port
            return 0
        fi
    done
    
    echo "❌ 端口范围 $start_port-$max_port 内没有可用端口"
    return 1
}

# 主启动流程
main() {
    # 1. 清理端口占用
    cleanup_ports
    
    # 2. 验证清理结果
    verify_ports_clean
    
    # 3. 运行启动前检查
    if [ -f "./scripts/pre-start-check.sh" ]; then
        echo "🔍 运行启动前检查..."
        ./scripts/pre-start-check.sh
    else
        echo "⚠️  启动前检查脚本不存在，跳过..."
    fi
    
    # 4. 智能选择端口
    if ! select_available_port; then
        echo "❌ 无法找到可用端口，启动失败"
        exit 1
    fi
    
    # 5. 清理之前的Node.js进程
    echo "🧹 清理Node.js相关进程..."
    pkill -f "next dev" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "node.*next" 2>/dev/null || true
    
    # 等待进程完全终止
    sleep 2
    
    # 6. 设置环境变量
    export NODE_ENV=development
    export NEXT_TELEMETRY_DISABLED=1
    
    # 7. 启动开发服务器
    echo "🌟 启动 Next.js 开发服务器..."
    echo "📍 访问地址: http://localhost:${PORT:-3000}"
    echo "🔧 调试端口: 9229"
    echo "📊 监控面板: http://localhost:3001 (如果可用)"
    echo ""
    echo "💡 提示:"
    echo "   - 按 Ctrl+C 停止服务器"
    echo "   - 修改代码会自动热重载"
    echo "   - 查看 http://localhost:${PORT:-3000}/api/health 检查服务状态"
    echo "=========================="
    echo ""
    
    # 启动开发服务器，使用选定的端口
    if [ -n "$PORT" ] && [ "$PORT" != "3000" ]; then
        echo "🔄 使用端口 $PORT 启动服务器..."
        npm run dev -- --port $PORT
    else
        npm run dev
    fi
}

# 信号处理 - 优雅关闭
cleanup_on_exit() {
    echo ""
    echo "🛑 收到停止信号，正在关闭服务器..."
    
    # 终止子进程
    if [ -n "$!" ]; then
        kill $! 2>/dev/null || true
    fi
    
    # 清理端口
    if [ -n "$PORT" ]; then
        local pids=$(lsof -ti :$PORT 2>/dev/null)
        if [ -n "$pids" ]; then
            echo "🧹 清理端口 $PORT..."
            kill $pids 2>/dev/null || true
        fi
    fi
    
    echo "✅ 服务器已关闭"
    exit 0
}

# 设置信号处理
trap cleanup_on_exit SIGINT SIGTERM

# 检查是否以root权限运行
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  不建议以root权限运行开发服务器"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 执行主流程
main "$@"