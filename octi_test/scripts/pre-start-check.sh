#!/bin/bash
# 开发服务器启动前检查脚本

echo "🔍 开发服务器启动前检查..."

# 1. 检查环境变量
echo "📋 检查环境变量..."
if [ -f ".env" ]; then
  echo "✅ .env 文件存在"
  
  # 检查关键环境变量
  source .env
  
  if [ -n "$DATABASE_URL" ]; then
    echo "✅ DATABASE_URL 已配置"
  else
    echo "❌ DATABASE_URL 未配置"
  fi
  
  if [ -n "$MINIMAX_API_KEY" ]; then
    echo "✅ MINIMAX_API_KEY 已配置"
  else
    echo "⚠️  MINIMAX_API_KEY 未配置"
  fi
  
  if [ -n "$DEEPSEEK_API_KEY" ]; then
    echo "✅ DEEPSEEK_API_KEY 已配置"
  else
    echo "⚠️  DEEPSEEK_API_KEY 未配置"
  fi
else
  echo "❌ .env 文件不存在，从模板复制..."
  cp .env.example .env
  echo "📝 请编辑 .env 文件配置必要的环境变量"
fi

# 2. 检查依赖安装
echo "📋 检查依赖安装..."
if [ -d "node_modules" ]; then
  echo "✅ node_modules 存在"
else
  echo "📦 安装依赖..."
  npm install
fi

# 3. 检查数据库连接
echo "📋 检查数据库连接..."
if command -v docker &> /dev/null; then
  if docker ps | grep -q postgres; then
    echo "✅ PostgreSQL 容器运行中"
  else
    echo "🐳 启动数据库容器..."
    docker-compose -f docker-compose.dev.yml up -d postgres
    sleep 10
  fi
else
  echo "⚠️  Docker 未安装，请手动启动数据库"
fi

# 4. 运行数据库迁移
echo "📋 运行数据库迁移..."
npx prisma migrate dev --name init 2>/dev/null || echo "⚠️  数据库迁移可能需要手动处理"

# 5. 生成 Prisma 客户端
echo "📋 生成 Prisma 客户端..."
npx prisma generate

echo "✅ 启动前检查完成!"