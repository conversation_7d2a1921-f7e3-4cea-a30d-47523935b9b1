#!/bin/bash
# 阶段3修复验证脚本

echo "🔍 验证阶段3构造函数修复结果..."

# 1. 检查构造函数参数匹配
echo "📋 检查构造函数调用..."

# 检查是否还有无参数的构造函数调用
ZERO_PARAM_CALLS=$(grep -r "new LLMApiClient()\|new PromptBuilder()\|new DataFusionEngine()" src/ --include="*.ts")

if [ -n "$ZERO_PARAM_CALLS" ]; then
  echo "✅ 发现无参数构造函数调用（这是正常的）:"
  echo "$ZERO_PARAM_CALLS"
else
  echo "📝 所有构造函数调用都使用了参数"
fi

# 2. 检查类型定义是否完整
echo "📋 检查接口定义..."

REQUIRED_INTERFACES=(
  "LLMConfig"
  "PromptBuilderConfig" 
  "FusionConfig"
)

for interface in "${REQUIRED_INTERFACES[@]}"; do
  if grep -q "interface $interface" src/services/llm/llm-api-client.ts src/services/llm/prompt-builder.ts src/services/data/data-fusion-engine.ts; then
    echo "✅ $interface 接口已定义"
  else
    echo "❌ $interface 接口缺失"
  fi
done

# 3. 运行类型检查
echo "🔧 运行类型检查..."
npm run type-check 2>&1 | grep -E "(Expected.*arguments|Cannot find module)" || echo "✅ 构造函数参数类型检查通过"

# 4. 检查构建是否成功
echo "🏗️  测试构建..."
npm run build > /dev/null 2>&1

if [ $? -eq 0 ]; then
  echo "✅ 构建成功"
else
  echo "❌ 构建失败，需要进一步检查"
  npm run build
fi

echo "📊 阶段3修复验证完成!"